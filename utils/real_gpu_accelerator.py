#!/usr/bin/env python3
"""
Real GPU Accelerator for Strategy Evolution
Uses vectorbt for backtesting
"""

import numpy as np
import cupy as cp
import torch
import logging
from typing import Dict, List, Any
import time
import vectorbt as vbt
import polars as pl
# Removed numba.cuda import as kernels are no longer used

logger = logging.getLogger(__name__)

class RealGPUAccelerator:
    """Real GPU acceleration using vectorbt"""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.cuda_available = torch.cuda.is_available()
        
        if self.cuda_available:
            logger.info(f"🚀 Real GPU Accelerator initialized on {torch.cuda.get_device_name(0)}")
        else:
            logger.warning("CUDA not available - using CPU fallback")
    
    # Removed process_strategies_parallel_gpu and _launch_strategy_kernels methods
    # as they are no longer needed for pre-calculated indicators.

    def vectorized_backtest_gpu(self, close_prices: np.ndarray, 
                                  entries: Dict[str, np.ndarray], 
                                  exits: Dict[str, np.ndarray],
                                  strategies: List[Dict[str, Any]]) -> List[Dict[str, float]]:
        """
        Run vectorized backtesting on GPU using vectorbt with pre-generated signals.
        
        Args:
            close_prices: NumPy array of close prices.
            entries: Dictionary where keys are strategy names and values are boolean NumPy arrays for entry signals.
            exits: Dictionary where keys are strategy names and values are boolean NumPy arrays for exit signals.
            strategies: List of strategy configurations (used for names and risk_reward_ratios).
            
        Returns:
            List of dictionaries containing backtest results for each strategy.
        """
        if not self.cuda_available:
            logger.warning("CUDA not available - skipping GPU backtest.")
            return []
        
        try:
            start_time = time.time()
            
            results = []
            for strategy in strategies:
                strategy_name = strategy.get('name', 'UnknownStrategy')
                risk_reward_ratios = strategy.get('risk_reward_ratios', [[1, 2]]) # Default RR if not specified

                strategy_entries = entries.get(strategy_name)
                strategy_exits = exits.get(strategy_name)

                if strategy_entries is None or strategy_exits is None:
                    logger.warning(f"Signals not found for strategy {strategy_name}. Skipping backtest for this strategy.")
                    continue

                # Ensure entries and exits are boolean arrays
                strategy_entries = np.asarray(strategy_entries, dtype=bool)
                strategy_exits = np.asarray(strategy_exits, dtype=bool)

                # Debug: Count signals before vectorbt
                entry_count = int(strategy_entries.sum())
                exit_count = int(strategy_exits.sum())
                logger.info(f"[GPU_DEBUG] {strategy_name}: entry_signals={entry_count}, exit_signals={exit_count}, data_length={len(close_prices)}")

                # Iterate through risk_reward_ratios for each strategy
                for rr in risk_reward_ratios:
                    sl_stop = rr[0] / 100.0 # Convert percentage to decimal
                    tp_stop = rr[1] / 100.0 # Convert percentage to decimal

                    portfolio = vbt.Portfolio.from_signals(
                        close_prices,
                        entries=strategy_entries,
                        exits=strategy_exits,
                        sl_stop=sl_stop,
                        tp_stop=tp_stop
                    )

                    stats = portfolio.stats()
                    total_trades = int(stats.get('Total Trades', 0))
                    logger.info(f"[GPU_DEBUG] {strategy_name} RR {rr[0]}:{rr[1]}: vectorbt generated {total_trades} trades")
                    if total_trades > 0:
                        win_rate_pct = float(stats.get('Win Rate [%]', 0.0))
                        winning_trades = int(round(total_trades * (win_rate_pct / 100.0)))
                        total_return_pct = float(stats.get('Total Return [%]', 0.0))
                        sharpe_ratio = float(stats.get('Sharpe Ratio', 0.0))
                        max_dd = stats.get('Max Drawdown [%]', stats.get('Max Drawdown', 0.0))
                        max_drawdown = float(max_dd)

                        results.append({
                            'strategy_name': strategy_name,
                            'risk_reward_ratio': f"{rr[0]}:{rr[1]}",
                            'total_trades': total_trades,
                            'winning_trades': winning_trades,
                            'total_pnl': total_return_pct,
                            'roi': total_return_pct,
                            'win_rate': win_rate_pct,
                            'sharpe_ratio': sharpe_ratio,
                            'max_drawdown': max_drawdown
                        })
                    else:
                        logger.info(f"No trades generated for strategy {strategy_name} with RR {rr[0]}:{rr[1]}. Skipping stats calculation.")
                        results.append({
                            'strategy_name': strategy_name,
                            'risk_reward_ratio': f"{rr[0]}:{rr[1]}",
                            'total_trades': 0,
                            'winning_trades': 0,
                            'total_pnl': 0.0,
                            'roi': 0.0,
                            'win_rate': 0.0,
                            'sharpe_ratio': 0.0,
                            'max_drawdown': 0.0
                        })
            
            processing_time = time.time() - start_time
            logger.info(f"🚀 GPU vectorized backtest completed in {processing_time:.3f}s")
            
            return results
            
        except Exception as e:
            logger.error(f"GPU vectorized backtest failed: {e}")
            return []
    
    def cleanup_gpu_memory(self):
        """Clean up GPU memory"""
        try:
            if self.cuda_available:
                cp.get_default_memory_pool().free_all_blocks()
                torch.cuda.empty_cache()
                logger.debug("GPU memory cleaned up")
        except Exception as e:
            logger.warning(f"GPU cleanup failed: {e}")

# Global instance
real_gpu_accelerator = RealGPUAccelerator()
