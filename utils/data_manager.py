#!/usr/bin/env python3
"""
Data Manager for SmartAPI Integration
Handles data download, processing, and real-time WebSocket feeds
"""

import os
import sys
import asyncio
import logging
import json
import polars as pl
import pyarrow as pa
from datetime import datetime, timedelta, time
from typing import Dict, List, Any, Optional, Tuple
import warnings
warnings.filterwarnings('ignore')

# SmartAPI integration
try:
    from SmartApi import SmartConnect
    from SmartApi.smartWebSocketV2 import SmartWebSocketV2
except ImportError:
    print("[WARN]  SmartAPI not installed. Install with: pip install smartapi-python")
    SmartConnect = None
    SmartWebSocketV2 = None

# Import Angel API utilities
try:
    from utils.angel_api import AngelOneAPIClient
except ImportError:
    print("[WARN]  Angel API utilities not found")
    AngelOneAPIClient = None

logger = logging.getLogger(__name__)

# ═══════════════════════════════════════════════════════════════════════════════
# [STATUS] DATA MODELS
# ═══════════════════════════════════════════════════════════════════════════════

class DataManager:
    """
    Data Manager for SmartAPI Integration
    
    Features:
    - Download 5-min candle data from SmartAPI
    - Convert to multiple timeframes (15min, 30min, 1hr, 1day)
    - Real-time WebSocket data processing
    - Optimized with polars and pyarrow
    - Support for 500+ stocks
    """
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize Data Manager"""
        
        self.config = config
        self.angel_api = None
        self.websocket = None
        
        # Data settings
        self.base_timeframe = "5min"
        self.supported_timeframes = ["5min", "15min", "30min", "1hr", "1day"]
        self.max_symbols = 500
        
        # Data storage paths
        self.data_dir = "data/historical"
        self.live_data_dir = "data/live"
        os.makedirs(self.data_dir, exist_ok=True)
        os.makedirs(self.live_data_dir, exist_ok=True)
        
        # Symbol list (NSE top 500)
        self.symbols = self._load_symbol_list()
        
        # WebSocket settings
        self.websocket_subscriptions = []
        self.live_data_buffer = {}
        
        logger.info(f"[STATUS] Data Manager initialized for {len(self.symbols)} symbols")
    
    def _load_symbol_list(self) -> List[Dict[str, Any]]:
        """Load symbol list for data download"""
        try:
            # Try to load from stock universe first
            try:
                from utils.stock_universe import StockUniverse
                stock_universe = StockUniverse()
                if stock_universe.load_stock_universe():
                    # Get all active stocks
                    all_stocks = stock_universe.get_all_stocks()
                    symbols = []
                    for stock in all_stocks:
                        symbols.append({
                            "symbol": f"{stock.symbol}-EQ",
                            "token": stock.token,
                            "exchange": "NSE"
                        })
                    logger.info(f"[SUCCESS] Loaded {len(symbols)} symbols from stock universe")
                    return symbols
            except ImportError:
                pass

            # If stock universe not available, return empty list
            logger.warning("[WARN] Stock universe not available for symbol loading")
            return []
                {"symbol": "KOTAKBANK-EQ", "token": "1922", "exchange": "NSE"},
                {"symbol": "LT-EQ", "token": "11483", "exchange": "NSE"},
                {"symbol": "AXISBANK-EQ", "token": "5900", "exchange": "NSE"},
                {"symbol": "ASIANPAINT-EQ", "token": "3718", "exchange": "NSE"},
                {"symbol": "MARUTI-EQ", "token": "10999", "exchange": "NSE"},
                {"symbol": "SUNPHARMA-EQ", "token": "3351", "exchange": "NSE"},
                {"symbol": "TITAN-EQ", "token": "3506", "exchange": "NSE"},
                {"symbol": "ULTRACEMCO-EQ", "token": "11532", "exchange": "NSE"},
                {"symbol": "WIPRO-EQ", "token": "3787", "exchange": "NSE"},
                {"symbol": "NESTLEIND-EQ", "token": "17963", "exchange": "NSE"},
                {"symbol": "POWERGRID-EQ", "token": "14977", "exchange": "NSE"}
            ]
            
            # Try to load from file if exists
            symbol_file = "agents/config/symbols.json"
            if os.path.exists(symbol_file):
                with open(symbol_file, 'r') as f:
                    symbols = json.load(f)
                logger.info(f"[SUCCESS] Loaded {len(symbols)} symbols from {symbol_file}")
                return symbols[:self.max_symbols]  # Limit to max symbols
            
            logger.info(f"[LIST] Using default symbol list: {len(default_symbols)} symbols")
            return default_symbols
            
        except Exception as e:
            logger.error(f"[ERROR] Error loading symbol list: {e}")
            return []
    
    async def initialize(self) -> bool:
        """Initialize Angel One API client"""
        try:
            if not AngelOneAPIClient:
                logger.error("[ERROR] AngelOneAPIClient not available")
                return False
            
            self.angel_api = AngelOneAPIClient(self.config)
            auth_success = await self.angel_api.authenticate()
            
            if auth_success:
                logger.info("[SUCCESS] Angel One API client initialized and authenticated")
                return True
            else:
                logger.error("[ERROR] Failed to authenticate with Angel One API")
                return False
                
        except Exception as e:
            logger.error(f"[ERROR] Error initializing Angel One API: {e}")
            return False
    
    async def download_historical_data(self, symbol: str, token: str, exchange: str,
                                     from_date: datetime, to_date: datetime,
                                     interval: str = "FIVE_MINUTE") -> Optional[pl.DataFrame]:
        """
        Download historical data for a symbol
        
        Args:
            symbol: Trading symbol
            token: Symbol token
            exchange: Exchange (NSE/BSE)
            from_date: Start date
            to_date: End date
            interval: Data interval (FIVE_MINUTE, FIFTEEN_MINUTE, etc.)
        
        Returns:
            Polars DataFrame with OHLCV data
        """
        try:
            if not self.angel_api:
                logger.error("[ERROR] Angel API not initialized")
                return None
            
            # Prepare historical data request
            hist_params = {
                "exchange": exchange,
                "symboltoken": token,
                "interval": interval,
                "fromdate": from_date.strftime("%Y-%m-%d %H:%M"),
                "todate": to_date.strftime("%Y-%m-%d %H:%M")
            }
            
            logger.debug(f"📥 Downloading {symbol} data from {from_date} to {to_date}")
            
            # Call historical data API
            response = self.angel_api.smart_api.getCandleData(hist_params)
            
            if not response.get('status'):
                logger.error(f"[ERROR] Failed to download {symbol} data: {response.get('message')}")
                return None
            
            # Parse response data
            candle_data = response.get('data', [])
            if not candle_data:
                logger.warning(f"[WARN] No data received for {symbol}")
                return None
            
            # Convert to Polars DataFrame
            df_data = []
            for candle in candle_data:
                df_data.append({
                    'timestamp': datetime.strptime(candle[0], "%Y-%m-%dT%H:%M:%S%z"),
                    'open': float(candle[1]),
                    'high': float(candle[2]),
                    'low': float(candle[3]),
                    'close': float(candle[4]),
                    'volume': int(candle[5]),
                    'symbol': symbol,
                    'exchange': exchange,
                    'token': token
                })
            
            df = pl.DataFrame(df_data)
            
            # Sort by timestamp
            df = df.sort('timestamp')
            
            logger.info(f"[SUCCESS] Downloaded {len(df)} candles for {symbol}")
            return df
            
        except Exception as e:
            logger.error(f"[ERROR] Error downloading {symbol} data: {e}")
            return None
    
    def convert_timeframe(self, df: pl.DataFrame, target_timeframe: str) -> pl.DataFrame:
        """
        Convert 5-min data to other timeframes using polars
        
        Args:
            df: Input DataFrame with 5-min data
            target_timeframe: Target timeframe (15min, 30min, 1hr, 1day)
        
        Returns:
            Converted DataFrame
        """
        try:
            if target_timeframe == "5min":
                return df
            
            # Define resampling rules
            timeframe_map = {
                "15min": "15m",
                "30min": "30m", 
                "1hr": "1h",
                "1day": "1d"
            }
            
            if target_timeframe not in timeframe_map:
                logger.error(f"[ERROR] Unsupported timeframe: {target_timeframe}")
                return df
            
            resample_rule = timeframe_map[target_timeframe]
            
            # Group by symbol and resample
            result_dfs = []
            
            for symbol in df['symbol'].unique():
                symbol_df = df.filter(pl.col('symbol') == symbol)
                
                # Resample OHLCV data
                resampled = symbol_df.group_by_dynamic(
                    'timestamp',
                    every=resample_rule,
                    closed='left'
                ).agg([
                    pl.col('open').first().alias('open'),
                    pl.col('high').max().alias('high'),
                    pl.col('low').min().alias('low'),
                    pl.col('close').last().alias('close'),
                    pl.col('volume').sum().alias('volume'),
                    pl.col('symbol').first().alias('symbol'),
                    pl.col('exchange').first().alias('exchange'),
                    pl.col('token').first().alias('token')
                ])
                
                result_dfs.append(resampled)
            
            # Combine all symbols
            if result_dfs:
                combined_df = pl.concat(result_dfs)
                combined_df = combined_df.sort(['symbol', 'timestamp'])
                
                logger.info(f"[SUCCESS] Converted to {target_timeframe}: {len(combined_df)} candles")
                return combined_df
            else:
                return df
                
        except Exception as e:
            logger.error(f"[ERROR] Error converting timeframe to {target_timeframe}: {e}")
            return df
    
    async def download_all_symbols(self, days_back: int = 30) -> Dict[str, pl.DataFrame]:
        """
        Download historical data for all symbols
        
        Args:
            days_back: Number of days to download
        
        Returns:
            Dictionary of DataFrames by timeframe
        """
        try:
            if not await self.initialize():
                logger.error("[ERROR] Failed to initialize data manager")
                return {}
            
            # Calculate date range
            to_date = datetime.now()
            from_date = to_date - timedelta(days=days_back)
            
            logger.info(f"📥 Starting download for {len(self.symbols)} symbols ({days_back} days)")
            
            # Download 5-min data for all symbols
            all_data = []
            failed_symbols = []
            
            for i, symbol_info in enumerate(self.symbols):
                try:
                    symbol = symbol_info['symbol']
                    token = symbol_info['token']
                    exchange = symbol_info['exchange']
                    
                    logger.info(f"📥 Downloading {symbol} ({i+1}/{len(self.symbols)})")
                    
                    df = await self.download_historical_data(
                        symbol, token, exchange, from_date, to_date
                    )
                    
                    if df is not None and len(df) > 0:
                        all_data.append(df)
                    else:
                        failed_symbols.append(symbol)
                    
                    # Rate limiting
                    await asyncio.sleep(0.1)
                    
                except Exception as e:
                    logger.error(f"[ERROR] Error downloading {symbol}: {e}")
                    failed_symbols.append(symbol)
            
            if failed_symbols:
                logger.warning(f"[WARN] Failed to download {len(failed_symbols)} symbols: {failed_symbols[:10]}...")
            
            if not all_data:
                logger.error("[ERROR] No data downloaded")
                return {}
            
            # Combine all 5-min data
            combined_5min = pl.concat(all_data)
            logger.info(f"[SUCCESS] Combined 5-min data: {len(combined_5min)} total candles")
            
            # Convert to all timeframes
            timeframe_data = {}
            
            for timeframe in self.supported_timeframes:
                logger.info(f"[WORKFLOW] Converting to {timeframe}...")
                
                if timeframe == "5min":
                    timeframe_data[timeframe] = combined_5min
                else:
                    timeframe_data[timeframe] = self.convert_timeframe(combined_5min, timeframe)
                
                # Save to parquet
                output_file = f"{self.data_dir}/historical_{timeframe}.parquet"
                timeframe_data[timeframe].write_parquet(output_file)
                logger.info(f"💾 Saved {timeframe} data to {output_file}")
            
            logger.info(f"🎉 Download completed for all timeframes")
            return timeframe_data
            
        except Exception as e:
            logger.error(f"[ERROR] Error downloading all symbols: {e}")
            return {}
    
    async def start_websocket_feed(self, symbols: List[Dict[str, Any]]) -> bool:
        """
        Start real-time WebSocket feed for live data
        
        Args:
            symbols: List of symbol dictionaries with token, exchange info
        
        Returns:
            Success status
        """
        try:
            if not self.angel_api or not self.angel_api.feed_token:
                logger.error("[ERROR] Angel API not authenticated for WebSocket")
                return False
            
            # Initialize WebSocket
            self.websocket = SmartWebSocketV2(
                auth_token=self.angel_api.auth_token,
                api_key=self.angel_api.api_key,
                client_code=self.angel_api.user_id,
                feed_token=self.angel_api.feed_token
            )
            
            # Set up event handlers
            self.websocket.on_open = self._on_websocket_open
            self.websocket.on_data = self._on_websocket_data
            self.websocket.on_error = self._on_websocket_error
            self.websocket.on_close = self._on_websocket_close
            
            # Prepare subscription data
            self.websocket_subscriptions = []
            for symbol_info in symbols[:100]:  # Limit to 100 symbols for WebSocket
                self.websocket_subscriptions.append({
                    "exchangeType": 1 if symbol_info['exchange'] == 'NSE' else 2,
                    "tokens": [symbol_info['token']]
                })
            
            # Connect WebSocket
            logger.info(f"[CONNECT] Starting WebSocket for {len(self.websocket_subscriptions)} symbols...")
            self.websocket.connect()
            
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Error starting WebSocket: {e}")
            return False
    
    def _on_websocket_open(self, ws):
        """WebSocket open handler"""
        logger.info("[SUCCESS] WebSocket connected")
        
        # Subscribe to symbols
        for subscription in self.websocket_subscriptions:
            ws.subscribe("mw", subscription["exchangeType"], subscription["tokens"])
        
        logger.info(f"[SIGNAL] Subscribed to {len(self.websocket_subscriptions)} symbols")
    
    def _on_websocket_data(self, ws, message):
        """WebSocket data handler"""
        try:
            # Process real-time tick data
            if isinstance(message, dict):
                token = message.get('token')
                ltp = message.get('last_traded_price')
                timestamp = datetime.now()
                
                if token and ltp:
                    # Store in live data buffer
                    self.live_data_buffer[token] = {
                        'ltp': float(ltp),
                        'timestamp': timestamp,
                        'volume': message.get('volume_traded_today', 0),
                        'open': message.get('open_price_of_the_day', 0),
                        'high': message.get('high_price_of_the_day', 0),
                        'low': message.get('low_price_of_the_day', 0),
                        'close': message.get('close_price', 0)
                    }
                    
                    # Log every 100th update to avoid spam
                    if len(self.live_data_buffer) % 100 == 0:
                        logger.debug(f"[STATUS] Live data buffer: {len(self.live_data_buffer)} symbols")
            
        except Exception as e:
            logger.error(f"[ERROR] Error processing WebSocket data: {e}")
    
    def _on_websocket_error(self, ws, error):
        """WebSocket error handler"""
        logger.error(f"[ERROR] WebSocket error: {error}")
    
    def _on_websocket_close(self, ws):
        """WebSocket close handler"""
        logger.warning("[WARN] WebSocket connection closed")
    
    def get_live_prices(self) -> Dict[str, float]:
        """Get current live prices for all subscribed symbols"""
        try:
            live_prices = {}
            for token, data in self.live_data_buffer.items():
                live_prices[token] = data['ltp']
            
            return live_prices
            
        except Exception as e:
            logger.error(f"[ERROR] Error getting live prices: {e}")
            return {}
    
    async def cleanup(self):
        """Cleanup resources"""
        try:
            if self.websocket:
                self.websocket.close()
                logger.info("[SUCCESS] WebSocket connection closed")
            
            if self.angel_api:
                await self.angel_api.close_session()
                logger.info("[SUCCESS] Angel API session closed")
                
        except Exception as e:
            logger.error(f"[ERROR] Error during cleanup: {e}")

# ═══════════════════════════════════════════════════════════════════════════════
# [CONFIG] UTILITY FUNCTIONS
# ═══════════════════════════════════════════════════════════════════════════════

async def download_sample_data(config: Dict[str, Any], days_back: int = 7) -> bool:
    """Download sample data for testing"""
    try:
        data_manager = DataManager(config)
        data = await data_manager.download_all_symbols(days_back)
        
        if data:
            logger.info(f"[SUCCESS] Sample data download completed: {len(data)} timeframes")
            return True
        else:
            logger.error("[ERROR] Sample data download failed")
            return False
            
    except Exception as e:
        logger.error(f"[ERROR] Error downloading sample data: {e}")
        return False
