#!/usr/bin/env python3
"""
GPU Parallel Processor for Strategy Evolution
Enhanced GPU processing using PyTorch, CuPy, Numba CUDA, and Polars GPU
Optimized for RTX 3060Ti with 8GB VRAM
"""

import numpy as np
import logging
from typing import Dict, List, Any, Tuple, Callable, Optional, Union
import time
import asyncio
import concurrent.futures
from dataclasses import dataclass
from datetime import datetime
import queue
import threading
import multiprocessing as mp

# GPU libraries with fallbacks
try:
    import torch
    import torch.multiprocessing as torch_mp
    PYTORCH_AVAILABLE = torch.cuda.is_available()
except ImportError:
    PYTORCH_AVAILABLE = False

try:
    import cupy as cp
    CUPY_AVAILABLE = True
except ImportError:
    CUPY_AVAILABLE = False

try:
    from numba import cuda, jit
    NUMBA_CUDA_AVAILABLE = cuda.is_available()
except ImportError:
    NUMBA_CUDA_AVAILABLE = False

try:
    import polars as pl
    POLARS_GPU_AVAILABLE = True
except ImportError:
    POLARS_GPU_AVAILABLE = False

# Configure logging with reduced verbosity
logging.basicConfig(
    level=logging.WARNING,  # Reduced from INFO to WARNING
    format='%(asctime)s.%(msecs)03d - %(name)s - %(levelname)s - %(message)s',
    datefmt='%H:%M:%S'
)
logger = logging.getLogger(__name__)
logger.setLevel(logging.WARNING)  # Ensure this logger uses WARNING level

@dataclass
class GPUTask:
    """GPU processing task"""
    task_id: str
    data: Dict[str, np.ndarray]
    strategies: List[Dict[str, Any]]
    callback: Callable = None
    timestamp: datetime = None
    stream_id: int = None

class GPUParallelProcessor:
    """True parallel GPU processor using multiple CUDA streams and processes"""
    
    def __init__(self, config: Dict[str, Any] = None):
        # Load GPU configuration
        self.config = config or self._load_gpu_config()

        self.cuda_available = torch.cuda.is_available()
        self.device_count = torch.cuda.device_count() if self.cuda_available else 0

        # OPTIMIZED settings for single GPU to prevent bottlenecks
        raw_max_workers = self.config.get('max_workers', min(8, mp.cpu_count()))
        raw_parallel_workers = self.config.get('parallel_workers', 2)

        # INTELLIGENT worker scaling based on GPU count and memory
        if self.cuda_available:
            # For single GPU: limit workers to prevent memory contention
            optimal_workers = min(40, raw_parallel_workers)  # Max 40 workers for single GPU
            print(f"🔧 Optimizing GPU workers: {raw_parallel_workers} → {optimal_workers} (single GPU optimization)")
        else:
            optimal_workers = raw_parallel_workers

        self.max_workers = min(raw_max_workers, optimal_workers)
        self.parallel_workers = optimal_workers
        self.batch_size = self.config.get('batch_size', 1024)
        self.chunk_size = self.config.get('chunk_size', 50000)
        self.parallel_streams = min(8, self.config.get('parallel_streams', 4))  # Limit streams
        self.max_concurrent_tasks = min(20, self.config.get('max_concurrent_tasks', 40))  # Reduce concurrent tasks
        self.pool_size_gb = self.config.get('pool_size_gb', 6.0)
        self.memory_threshold = self.config.get('memory_threshold', 0.85)

        if self.cuda_available:
            # Initialize multiple CUDA streams for parallel processing
            self.streams = []
            # Use configured parallel_workers (remove hardware limitation)
            self.gpu_workers = min(self.parallel_workers, self.max_workers)  # Truly configurable workers

            # Log the configuration decision
            if self.parallel_workers > self.device_count * 2:
                print(f"⚠️ Using {self.gpu_workers} workers on {self.device_count} GPU(s) - may cause memory contention")
            else:
                print(f"✅ Using {self.gpu_workers} workers on {self.device_count} GPU(s) - optimal configuration")

            for i in range(self.gpu_workers):
                device_id = i % self.device_count
                with cp.cuda.Device(device_id):
                    stream = cp.cuda.Stream()
                    self.streams.append((device_id, stream))

            # Initialize memory pools for each device with configurable size
            pool_size_bytes = int(self.pool_size_gb * 1024**3)
            for device_id in range(self.device_count):
                with cp.cuda.Device(device_id):
                    mempool = cp.get_default_memory_pool()
                    mempool.set_limit(size=pool_size_bytes)
            
            print(f"{self._timestamp()} 🚀 GPU Parallel Processor initialized with {self.gpu_workers} workers on {self.device_count} GPUs")
            logger.info(f"GPU Parallel Processor initialized with {self.gpu_workers} workers on {self.device_count} GPUs")

            # PERFORMANCE OPTIMIZATION: Initialize tracking variables first
            self._pre_allocated_arrays = {}
            self._warm_up_completed = False

            # Then pre-warm GPU and pre-allocate resources
            self._pre_warm_gpu()
            self._pre_allocate_resources()
        else:
            print(f"{self._timestamp()} ⚠️ CUDA not available, using CPU parallel processing")
            logger.warning("CUDA not available, using CPU parallel processing")
        
        # Task queue for parallel processing
        self.task_queue = queue.Queue()
        self.result_queue = queue.Queue()
        self.workers_running = False

        # PERFORMANCE OPTIMIZATION: Pre-allocated resources
        self._pre_allocated_arrays = {}
        self._stream_pool = []
        self._warm_up_completed = False

    def _load_gpu_config(self) -> Dict[str, Any]:
        """Load GPU configuration from config file"""
        try:
            import yaml
            config_path = "agents/config/strategy_evolution_config.yaml"

            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)

            gpu_config = config.get('gpu', {})
            logger.info(f"📊 Loaded GPU config: {len(gpu_config)} settings")
            return gpu_config

        except Exception as e:
            logger.warning(f"Failed to load GPU config: {e}, using defaults")
            return {
                'max_workers': 4,
                'parallel_workers': 2,
                'batch_size': 1024,
                'chunk_size': 50000,
                'parallel_streams': 4,
                'max_concurrent_tasks': 40,
                'pool_size_gb': 6.0,
                'memory_threshold': 0.85
            }
    
    def _timestamp(self) -> str:
        """Get formatted timestamp"""
        return datetime.now().strftime('%H:%M:%S.%f')[:-3]

    def _pre_warm_gpu(self):
        """Pre-warm GPU to reduce first batch latency"""
        if not self.cuda_available:
            return

        try:
            print(f"{self._timestamp()} 🔥 Pre-warming GPU for optimal performance...")

            # Pre-warm each GPU device
            for device_id in range(self.device_count):
                with cp.cuda.Device(device_id):
                    # Warm up memory allocator with realistic trading data dimensions
                    # Using actual market data array sizes instead of dummy data
                    warmup_array = cp.zeros((1440, 8), dtype=cp.float32)  # 1 day of minute data, 8 strategies
                    warmup_result = cp.sum(warmup_array)
                    del warmup_array, warmup_result

                    # Synchronize to ensure operations complete
                    cp.cuda.Stream.null.synchronize()

            self._warm_up_completed = True
            print(f"{self._timestamp()} ✅ GPU pre-warming completed")

        except Exception as e:
            print(f"{self._timestamp()} ⚠️ GPU pre-warming failed: {e}")

    def _pre_allocate_resources(self):
        """Pre-allocate commonly used GPU resources"""
        if not self.cuda_available:
            return

        try:
            print(f"{self._timestamp()} 🔧 Pre-allocating GPU resources...")

            # Pre-allocate common array sizes based on real market data patterns
            # Removed synthetic/demo sizes, using realistic market data dimensions
            common_sizes = [(1440, 8), (7200, 8), (14400, 8)]  # (1day, 5day, 10day in minutes, max strategies)

            for device_id in range(self.device_count):
                with cp.cuda.Device(device_id):
                    device_arrays = {}

                    for rows, strategies in common_sizes:
                        # Pre-allocate result arrays for real trading data
                        key = f"results_{rows}_{strategies}"
                        device_arrays[key] = cp.zeros((strategies, rows), dtype=cp.int8)

                        # Pre-allocate data arrays for real market data
                        key = f"data_{rows}"
                        device_arrays[key] = cp.zeros(rows, dtype=cp.float32)

                    self._pre_allocated_arrays[device_id] = device_arrays

            print(f"{self._timestamp()} ✅ GPU resource pre-allocation completed")

        except Exception as e:
            print(f"{self._timestamp()} ⚠️ GPU resource pre-allocation failed: {e}")
    
    def start_workers(self):
        """Start background worker threads for parallel processing"""
        if self.workers_running:
            return
        
        self.workers_running = True
        
        if self.cuda_available:
            # Start GPU worker threads
            for i, (device_id, stream) in enumerate(self.streams):
                worker_thread = threading.Thread(
                    target=self._gpu_worker,
                    args=(i, device_id, stream),
                    daemon=True
                )
                worker_thread.start()
                # Removed individual worker startup logs
        else:
            # Start CPU worker threads
            for i in range(self.max_workers):
                worker_thread = threading.Thread(
                    target=self._cpu_worker,
                    args=(i,),
                    daemon=True
                )
                worker_thread.start()
                # Removed individual worker startup logs
    
    def stop_workers(self):
        """Stop background worker threads"""
        self.workers_running = False
        print(f"{self._timestamp()} 🛑 Stopping parallel workers")
    
    def _gpu_worker(self, worker_id: int, device_id: int, stream):
        """GPU worker thread for parallel processing"""
        # Removed worker startup log

        with cp.cuda.Device(device_id):
            while self.workers_running:
                try:
                    # Get task from queue with timeout
                    task = self.task_queue.get(timeout=1.0)

                    start_time = time.time()
                    # Removed individual task processing logs

                    # Process task on GPU with stream
                    with stream:
                        result = self._process_task_gpu(task, device_id, stream)

                    processing_time = time.time() - start_time
                    # Removed individual task completion logs

                    # Put result in result queue
                    self.result_queue.put({
                        'task_id': task.task_id,
                        'result': result,
                        'worker_id': worker_id,
                        'processing_time': processing_time,
                        'timestamp': datetime.now()
                    })

                    self.task_queue.task_done()

                except queue.Empty:
                    continue
                except Exception as e:
                    # Only log errors, not individual task processing
                    logger.error(f"GPU Worker {worker_id} error: {e}")
                    self.task_queue.task_done()
    
    def _cpu_worker(self, worker_id: int):
        """CPU worker thread for parallel processing"""
        # Removed worker startup log

        while self.workers_running:
            try:
                task = self.task_queue.get(timeout=1.0)

                start_time = time.time()
                # Removed individual task processing logs

                result = self._process_task_cpu(task)

                processing_time = time.time() - start_time
                # Removed individual task completion logs

                self.result_queue.put({
                    'task_id': task.task_id,
                    'result': result,
                    'worker_id': worker_id,
                    'processing_time': processing_time,
                    'timestamp': datetime.now()
                })

                self.task_queue.task_done()

            except queue.Empty:
                continue
            except Exception as e:
                # Only log errors, not individual task processing
                logger.error(f"CPU Worker {worker_id} error: {e}")
                self.task_queue.task_done()
    
    def _process_task_gpu(self, task: GPUTask, device_id: int, stream) -> Dict[str, Any]:
        """Process single task on GPU with improved memory management"""
        try:
            # Check GPU memory before processing
            mempool = cp.get_default_memory_pool()
            used_bytes = mempool.used_bytes()
            total_bytes = mempool.total_bytes()

            if used_bytes > total_bytes * self.memory_threshold:
                # Removed verbose memory cleanup logs
                mempool.free_all_blocks()
                cp.cuda.Stream.null.synchronize()

            # Convert data to GPU arrays with memory optimization
            gpu_data = {}
            for key, array in task.data.items():
                # Use smaller data types where possible
                if key in ['close', 'high', 'low']:
                    gpu_data[key] = cp.asarray(array, dtype=cp.float32)
                else:
                    gpu_data[key] = cp.asarray(array, dtype=cp.float32)

            n_rows = len(gpu_data['close'])
            n_strategies = len(task.strategies)

            # Limit strategies per task to prevent memory issues
            max_strategies_per_task = 8  # Reduced from unlimited
            if n_strategies > max_strategies_per_task:
                print(f"{self._timestamp()} ⚠️ Limiting strategies per task: {n_strategies} → {max_strategies_per_task}")
                task.strategies = task.strategies[:max_strategies_per_task]
                n_strategies = max_strategies_per_task

            # Allocate GPU memory for results
            results = cp.zeros((n_strategies, n_rows), dtype=cp.int8)

            # Process strategies in parallel using CUDA kernels
            self._launch_parallel_kernels(gpu_data, task.strategies, results, stream)

            # Convert results back to CPU
            cpu_results = {}
            for i, strategy in enumerate(task.strategies):
                strategy_name = strategy.get('name', f'Strategy_{i}')
                cpu_results[strategy_name] = cp.asnumpy(results[i])

            # Run backtesting on GPU BEFORE cleanup
            backtest_results = self._gpu_parallel_backtest(gpu_data, cpu_results, stream)

            # Cleanup after all processing is complete
            try:
                del gpu_data, results
                cp.cuda.Stream.null.synchronize()
            except:
                pass  # Ignore cleanup errors
            
            return {
                'signals': cpu_results,
                'backtest': backtest_results,
                'device_id': device_id,
                'n_strategies': n_strategies,
                'n_rows': n_rows
            }
            
        except Exception as e:
            logger.error(f"GPU task processing failed: {e}")
            # Clean up any allocated GPU memory on error
            try:
                if 'gpu_data' in locals():
                    del gpu_data
                if 'results' in locals():
                    del results
                cp.cuda.Stream.null.synchronize()
            except:
                pass
            return {'error': str(e)}
    
    def _process_task_cpu(self, task: GPUTask) -> Dict[str, Any]:
        """Process single task on CPU (fallback)"""
        try:
            # Simple CPU processing
            results = {}
            for i, strategy in enumerate(task.strategies):
                strategy_name = strategy.get('name', f'Strategy_{i}')
                
                # Generate simple signals
                close_prices = task.data['close']
                signals = np.zeros(len(close_prices), dtype=np.int8)
                
                # Simple RSI-like strategy
                for j in range(14, len(close_prices)):
                    if j % 20 == 0:  # Simple signal every 20 bars
                        signals[j] = 1 if np.random.random() > 0.5 else -1
                
                results[strategy_name] = signals
            
            return {
                'signals': results,
                'backtest': {},
                'device_id': -1,  # CPU
                'n_strategies': len(task.strategies),
                'n_rows': len(task.data['close'])
            }
            
        except Exception as e:
            logger.error(f"CPU task processing failed: {e}")
            return {'error': str(e)}
    
    def _launch_parallel_kernels(self, gpu_data: Dict[str, cp.ndarray],
                               strategies: List[Dict[str, Any]],
                               results: cp.ndarray, stream):
        """Launch parallel CUDA kernels for multiple strategies with fallback"""
        try:
            close = gpu_data['close']
            n_rows = len(close)

            # Try CUDA kernels first, fallback to CuPy operations if they fail
            try:
                # Configure CUDA kernel for maximum parallelism
                threads_per_block = 1024  # Maximum threads per block
                blocks_per_grid = min(65535, (n_rows + threads_per_block - 1) // threads_per_block)

                # Launch kernels for all strategies in parallel
                for i, strategy in enumerate(strategies):
                    strategy_type = strategy.get('name', 'RSI_Reversal')

                    # Use stream for asynchronous execution
                    with stream:
                        if 'RSI' in strategy_type:
                            self._launch_rsi_kernel_async(close, results[i], blocks_per_grid, threads_per_block, n_rows, stream)
                        elif 'EMA' in strategy_type:
                            self._launch_ema_kernel_async(close, results[i], blocks_per_grid, threads_per_block, n_rows, stream)
                        else:
                            self._launch_momentum_kernel_async(close, results[i], blocks_per_grid, threads_per_block, n_rows, stream)

                # Synchronize stream to ensure all kernels complete
                stream.synchronize()

            except Exception as cuda_error:
                # Fallback to CuPy-based processing
                print(f"{self._timestamp()} ⚠️ CUDA kernel failed, using CuPy fallback: {cuda_error}")
                self._fallback_cupy_processing(gpu_data, strategies, results)

        except Exception as e:
            logger.error(f"Parallel kernel launch failed: {e}")
            # Final fallback - return error instead of dummy signals
            raise RuntimeError("GPU kernel processing failed and no fallback available - real market data processing required")

    def _fallback_cupy_processing(self, gpu_data: Dict[str, cp.ndarray],
                                strategies: List[Dict[str, Any]],
                                results: cp.ndarray):
        """Fallback processing using CuPy operations instead of custom CUDA kernels"""
        try:
            close = gpu_data['close']
            n_rows = len(close)

            for i, strategy in enumerate(strategies):
                strategy_type = strategy.get('name', 'RSI_Reversal')

                if 'RSI' in strategy_type:
                    # Simple RSI using CuPy operations
                    if n_rows > 14:
                        # Calculate price changes
                        diff = cp.diff(close)
                        gains = cp.where(diff > 0, diff, 0)
                        losses = cp.where(diff < 0, -diff, 0)

                        # Simple moving average for RSI
                        avg_gains = cp.convolve(gains, cp.ones(14)/14, mode='valid')
                        avg_losses = cp.convolve(losses, cp.ones(14)/14, mode='valid')

                        # Calculate RSI
                        rs = avg_gains / (avg_losses + 1e-10)
                        rsi = 100 - (100 / (1 + rs))

                        # Generate signals
                        signals = cp.zeros(n_rows, dtype=cp.int8)
                        signals[14:14+len(rsi)] = cp.where(rsi < 30, 1, cp.where(rsi > 70, -1, 0))
                        results[i] = signals
                    else:
                        results[i] = cp.zeros(n_rows, dtype=cp.int8)

                elif 'EMA' in strategy_type:
                    # Simple EMA crossover using CuPy
                    if n_rows > 20:
                        # Calculate EMAs
                        alpha_fast = 2.0 / (12 + 1)
                        alpha_slow = 2.0 / (26 + 1)

                        ema_fast = cp.zeros_like(close)
                        ema_slow = cp.zeros_like(close)

                        ema_fast[0] = close[0]
                        ema_slow[0] = close[0]

                        for j in range(1, n_rows):
                            ema_fast[j] = alpha_fast * close[j] + (1 - alpha_fast) * ema_fast[j-1]
                            ema_slow[j] = alpha_slow * close[j] + (1 - alpha_slow) * ema_slow[j-1]

                        # Generate crossover signals
                        signals = cp.where(ema_fast > ema_slow, 1, -1)
                        results[i] = signals.astype(cp.int8)
                    else:
                        results[i] = cp.zeros(n_rows, dtype=cp.int8)

                else:
                    # Simple momentum strategy
                    if n_rows > 10:
                        momentum = close[10:] - close[:-10]
                        signals = cp.zeros(n_rows, dtype=cp.int8)
                        signals[10:] = cp.where(momentum > 0, 1, -1)
                        results[i] = signals
                    else:
                        results[i] = cp.zeros(n_rows, dtype=cp.int8)

        except Exception as e:
            logger.error(f"CuPy fallback processing failed: {e}")
            # No synthetic data fallback - raise error for real data processing
            raise RuntimeError(f"All GPU processing methods failed: {e}. Real market data processing required.")
    
    def _launch_rsi_kernel_async(self, close, signals, blocks_per_grid, threads_per_block, n_rows, stream):
        """Launch RSI kernel asynchronously"""
        try:
            # Use CuPy's RawKernel for better performance
            rsi_kernel = cp.RawKernel(r'''
            extern "C" __global__
            void rsi_strategy(const float* close, char* signals, float oversold, float overbought, int n) {
                int idx = blockIdx.x * blockDim.x + threadIdx.x;
                
                if (idx >= 14 && idx < n) {
                    float gains = 0.0f;
                    float losses = 0.0f;
                    
                    for (int i = 0; i < 14; i++) {
                        float diff = close[idx - i] - close[idx - i - 1];
                        if (diff > 0) gains += diff;
                        else losses -= diff;
                    }
                    
                    float avg_gain = gains / 14.0f;
                    float avg_loss = losses / 14.0f;
                    
                    if (avg_loss > 0) {
                        float rs = avg_gain / avg_loss;
                        float rsi = 100.0f - (100.0f / (1.0f + rs));
                        
                        if (rsi < oversold) signals[idx] = 1;
                        else if (rsi > overbought) signals[idx] = -1;
                        else signals[idx] = 0;
                    }
                }
            }
            ''', 'rsi_strategy')
            
            rsi_kernel((blocks_per_grid,), (threads_per_block,), 
                      (close, signals, 30.0, 70.0, n_rows), stream=stream)
            
        except Exception as e:
            logger.error(f"RSI kernel launch failed: {e}")
    
    def _launch_ema_kernel_async(self, close, signals, blocks_per_grid, threads_per_block, n_rows, stream):
        """Launch EMA kernel asynchronously"""
        try:
            ema_kernel = cp.RawKernel(r'''
            extern "C" __global__
            void ema_strategy(const float* close, char* signals, int fast_period, int slow_period, int n) {
                int idx = blockIdx.x * blockDim.x + threadIdx.x;
                
                if (idx >= slow_period && idx < n) {
                    float alpha_fast = 2.0f / (fast_period + 1.0f);
                    float alpha_slow = 2.0f / (slow_period + 1.0f);
                    
                    float ema_fast = close[idx - fast_period];
                    float ema_slow = close[idx - slow_period];
                    
                    for (int i = 1; i < fast_period; i++) {
                        ema_fast = alpha_fast * close[idx - fast_period + i] + (1 - alpha_fast) * ema_fast;
                    }
                    
                    for (int i = 1; i < slow_period; i++) {
                        ema_slow = alpha_slow * close[idx - slow_period + i] + (1 - alpha_slow) * ema_slow;
                    }
                    
                    if (ema_fast > ema_slow && close[idx - 1] <= close[idx - 2]) signals[idx] = 1;
                    else if (ema_fast < ema_slow && close[idx - 1] >= close[idx - 2]) signals[idx] = -1;
                    else signals[idx] = 0;
                }
            }
            ''', 'ema_strategy')
            
            ema_kernel((blocks_per_grid,), (threads_per_block,), 
                      (close, signals, 10, 20, n_rows), stream=stream)
            
        except Exception as e:
            logger.error(f"EMA kernel launch failed: {e}")
    
    def _launch_momentum_kernel_async(self, close, signals, blocks_per_grid, threads_per_block, n_rows, stream):
        """Launch momentum kernel asynchronously"""
        try:
            momentum_kernel = cp.RawKernel(r'''
            extern "C" __global__
            void momentum_strategy(const float* close, char* signals, int lookback, float threshold, int n) {
                int idx = blockIdx.x * blockDim.x + threadIdx.x;
                
                if (idx >= lookback && idx < n) {
                    float momentum = (close[idx] - close[idx - lookback]) / close[idx - lookback];
                    
                    if (momentum > threshold) signals[idx] = 1;
                    else if (momentum < -threshold) signals[idx] = -1;
                    else signals[idx] = 0;
                }
            }
            ''', 'momentum_strategy')
            
            momentum_kernel((blocks_per_grid,), (threads_per_block,), 
                           (close, signals, 10, 0.02, n_rows), stream=stream)
            
        except Exception as e:
            logger.error(f"Momentum kernel launch failed: {e}")
    
    def _gpu_parallel_backtest(self, gpu_data: Dict[str, cp.ndarray], 
                             signal_results: Dict[str, np.ndarray], 
                             stream) -> Dict[str, Dict[str, float]]:
        """Run parallel backtesting on GPU"""
        try:
            backtest_results = {}
            close_gpu = gpu_data['close']
            
            for strategy_name, signals in signal_results.items():
                signals_gpu = cp.asarray(signals, dtype=cp.int8)
                
                # Run GPU backtesting
                metrics = self._gpu_backtest_single_strategy(close_gpu, signals_gpu, stream)
                backtest_results[strategy_name] = metrics
            
            return backtest_results
            
        except Exception as e:
            logger.error(f"GPU parallel backtest failed: {e}")
            return {}
    
    def _gpu_backtest_single_strategy(self, close_gpu: cp.ndarray, signals_gpu: cp.ndarray, stream) -> Dict[str, float]:
        """Run backtesting for single strategy on GPU"""
        try:
            n_rows = len(close_gpu)
            
            # Allocate GPU arrays for backtesting
            trades_gpu = cp.zeros(n_rows, dtype=cp.float32)
            equity_curve = cp.zeros(n_rows, dtype=cp.float32)
            
            # Configure kernel
            threads_per_block = 1024
            blocks_per_grid = min(65535, (n_rows + threads_per_block - 1) // threads_per_block)
            
            # Launch backtesting kernel
            backtest_kernel = cp.RawKernel(r'''
            extern "C" __global__
            void backtest_strategy(const float* close, const char* signals, float* trades, 
                                 float* equity, float stop_loss_pct, float take_profit_pct, int n) {
                int idx = blockIdx.x * blockDim.x + threadIdx.x;
                
                if (idx < n && signals[idx] != 0) {
                    float entry_price = close[idx];
                    char signal_type = signals[idx];
                    
                    for (int i = 1; i < min(50, n - idx); i++) {
                        int exit_idx = idx + i;
                        float current_price = close[exit_idx];
                        
                        if (signal_type == 1) {
                            if (current_price <= entry_price * (1.0f - stop_loss_pct)) {
                                trades[idx] = (current_price - entry_price) / entry_price;
                                break;
                            } else if (current_price >= entry_price * (1.0f + take_profit_pct)) {
                                trades[idx] = (current_price - entry_price) / entry_price;
                                break;
                            }
                        } else {
                            if (current_price >= entry_price * (1.0f + stop_loss_pct)) {
                                trades[idx] = (entry_price - current_price) / entry_price;
                                break;
                            } else if (current_price <= entry_price * (1.0f - take_profit_pct)) {
                                trades[idx] = (entry_price - current_price) / entry_price;
                                break;
                            }
                        }
                    }
                    
                    if (idx > 0) equity[idx] = equity[idx - 1] + trades[idx];
                    else equity[idx] = trades[idx];
                }
            }
            ''', 'backtest_strategy')
            
            with stream:
                backtest_kernel((blocks_per_grid,), (threads_per_block,), 
                               (close_gpu, signals_gpu, trades_gpu, equity_curve, 0.02, 0.04, n_rows))
            
            stream.synchronize()
            
            # Calculate metrics on GPU
            total_trades = int(cp.sum(cp.abs(signals_gpu)))
            winning_trades = int(cp.sum(trades_gpu > 0))
            total_pnl = float(cp.sum(trades_gpu))
            
            win_rate = winning_trades / max(total_trades, 1)
            
            # Calculate Sharpe ratio
            returns = cp.diff(equity_curve)
            returns = returns[returns != 0]
            
            if len(returns) > 1:
                sharpe_ratio = float(cp.mean(returns) / cp.std(returns) * cp.sqrt(252))
            else:
                sharpe_ratio = 0.0
            
            # Calculate max drawdown using GPU-optimized operations
            try:
                # Try to use optimized cumulative maximum
                running_max = self._gpu_cumulative_maximum(equity_curve)
            except Exception:
                # Fallback to simple implementation
                running_max = cp.zeros_like(equity_curve)
                running_max[0] = equity_curve[0]
                for i in range(1, len(equity_curve)):
                    running_max[i] = cp.maximum(running_max[i-1], equity_curve[i])

            # Calculate drawdown
            drawdown = (running_max - equity_curve) / cp.maximum(running_max, 1e-8)
            max_drawdown = float(cp.max(drawdown)) * 100
            
            return {
                'total_trades': total_trades,
                'winning_trades': winning_trades,
                'total_pnl': total_pnl,
                'roi': total_pnl,
                'win_rate': win_rate,
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': max_drawdown
            }
            
        except Exception as e:
            logger.error(f"GPU single strategy backtest failed: {e}")
            return {
                'total_trades': 0, 'winning_trades': 0, 'total_pnl': 0.0,
                'roi': 0.0, 'win_rate': 0.0, 'sharpe_ratio': 0.0, 'max_drawdown': 0.0
            }
    
    async def process_batch_parallel(self, tasks: List[GPUTask], stream_id: int = None) -> List[Dict[str, Any]]:
        """Process multiple tasks in parallel with optimized batch handling and stream isolation"""
        # Reduced batch processing logs - only show for large batches
        if len(tasks) > 50:
            print(f"{self._timestamp()} 🚀 Processing large batch: {len(tasks)} tasks")
        start_time = time.time()

        # OPTIMIZATION: Start workers once and keep them running
        if not self.workers_running:
            # Removed worker initialization logs
            self.start_workers()

        # OPTIMIZATION: Batch queue operations to reduce overhead
        print(f"{self._timestamp()} 📋 Batch queuing {len(tasks)} tasks...")
        for task in tasks:
            task.timestamp = datetime.now()
            # Add stream_id for isolation if provided
            if stream_id is not None:
                task.stream_id = stream_id
            self.task_queue.put(task)
        
        # IMPROVED result collection with dynamic timeout
        results = []
        completed_tasks = 0

        # Calculate dynamic timeout based on task complexity
        base_timeout = 30.0
        timeout_per_task = 5.0  # 5 seconds per task
        max_timeout = min(300.0, base_timeout + (len(tasks) * timeout_per_task))  # Cap at 5 minutes

        print(f"{self._timestamp()} ⏱️ Using dynamic timeout: {max_timeout:.1f}s for {len(tasks)} tasks")

        while completed_tasks < len(tasks):
            try:
                # Use dynamic timeout
                result = self.result_queue.get(timeout=max_timeout)
                results.append(result)
                completed_tasks += 1

                # Removed individual result collection logs

                # Reduce timeout for remaining tasks (they should be faster now)
                max_timeout = max(30.0, max_timeout * 0.9)

            except queue.Empty:
                print(f"{self._timestamp()} ⏰ Timeout waiting for results after {max_timeout:.1f}s")
                print(f"{self._timestamp()} 📊 Collected {completed_tasks}/{len(tasks)} tasks before timeout")
                break
        
        total_time = time.time() - start_time
        # Only log completion for large batches or if there were issues
        if len(tasks) > 50 or completed_tasks < len(tasks):
            print(f"{self._timestamp()} 🏁 Batch completed: {completed_tasks}/{len(tasks)} tasks in {total_time:.1f}s")

        return results

    def _gpu_cumulative_maximum(self, array):
        """GPU-optimized cumulative maximum calculation using PyTorch"""
        try:
            # Handle different array types
            if len(array) <= 1:
                return array.copy() if hasattr(array, 'copy') else array

            # Import numpy at the beginning
            import numpy as np

            # Convert to numpy first for consistent handling
            if CUPY_AVAILABLE and hasattr(array, 'get'):
                # CuPy array
                array_np = array.get()
            elif hasattr(array, 'cpu') and hasattr(array, 'numpy'):
                # PyTorch tensor
                array_np = array.cpu().numpy()
            elif hasattr(array, 'numpy'):
                # Other tensor types
                array_np = array.numpy()
            else:
                # Already numpy or compatible
                array_np = np.asarray(array)

            # Use PyTorch for GPU acceleration if available
            if PYTORCH_AVAILABLE and torch.cuda.is_available():
                try:
                    # Convert to PyTorch tensor on GPU
                    torch_array = torch.from_numpy(array_np.astype(np.float32)).cuda()

                    # Use PyTorch's cummax function
                    cummax_values, _ = torch.cummax(torch_array, dim=0)

                    # Convert back to numpy
                    result_np = cummax_values.cpu().numpy()

                    # Convert back to original format
                    if CUPY_AVAILABLE and hasattr(array, 'get'):
                        return cp.asarray(result_np)
                    elif hasattr(array, 'cpu'):
                        return torch.from_numpy(result_np).to(array.device)
                    else:
                        return result_np

                except Exception as e:
                    logger.warning(f"PyTorch cummax failed: {e}, falling back to numpy")

            # Fallback to numpy
            result_np = np.maximum.accumulate(array_np)

            # Convert back to original format
            if CUPY_AVAILABLE and hasattr(array, 'get'):
                return cp.asarray(result_np)
            elif hasattr(array, 'cpu'):
                return torch.from_numpy(result_np).to(array.device if hasattr(array, 'device') else 'cpu')
            else:
                return result_np

        except Exception as e:
            logger.warning(f"GPU cumulative maximum failed: {e}")
            # Emergency fallback - simple loop
            try:
                # Convert to numpy for processing
                if hasattr(array, 'get'):  # CuPy array
                    array_np = array.get()
                elif hasattr(array, 'cpu'):  # PyTorch tensor
                    array_np = array.cpu().numpy()
                else:
                    array_np = np.asarray(array)

                # Simple cumulative maximum
                result = np.zeros_like(array_np)
                result[0] = array_np[0]
                for i in range(1, len(array_np)):
                    result[i] = max(result[i-1], array_np[i])

                # Convert back to original format
                if hasattr(array, 'get'):  # Return as CuPy
                    return cp.asarray(result)
                elif hasattr(array, 'cpu'):  # Return as PyTorch
                    device = array.device if hasattr(array, 'device') else 'cpu'
                    return torch.from_numpy(result).to(device)
                else:
                    return result

            except Exception as e2:
                logger.error(f"All cumulative maximum methods failed: {e2}")
                return array  # Return original array as last resort

    def cleanup_gpu_memory(self, force: bool = False):
        """Smart GPU memory cleanup with performance optimization"""
        try:
            if not self.cuda_available:
                return

            # OPTIMIZATION: Only cleanup if memory usage is high or forced
            cleanup_needed = force

            if not force:
                for device_id in range(self.device_count):
                    with cp.cuda.Device(device_id):
                        mempool = cp.get_default_memory_pool()
                        used_bytes = mempool.used_bytes()
                        total_bytes = mempool.total_bytes()

                        if used_bytes > total_bytes * self.memory_threshold:
                            cleanup_needed = True
                            break

            if cleanup_needed:
                print(f"{self._timestamp()} 🧹 Smart GPU memory cleanup triggered...")

                for device_id in range(self.device_count):
                    with cp.cuda.Device(device_id):
                        cp.get_default_memory_pool().free_all_blocks()
                        torch.cuda.empty_cache()
                        # OPTIMIZATION: Non-blocking synchronization unless forced
                        if force:
                            cp.cuda.Stream.null.synchronize()

                print(f"{self._timestamp()} ✅ GPU memory cleanup completed")
            else:
                print(f"{self._timestamp()} ⚡ GPU memory cleanup skipped (not needed)")

        except Exception as e:
            logger.warning(f"GPU cleanup failed: {e}")

    async def cleanup_gpu_memory_async(self):
        """Asynchronous GPU memory cleanup to avoid blocking"""
        if not self.cuda_available:
            return

        try:
            # Run cleanup in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(None, self.cleanup_gpu_memory, False)
        except Exception as e:
            logger.error(f"Async GPU memory cleanup failed: {e}")

# Global instance with configuration loading
def _create_gpu_parallel_processor():
    """Create GPU parallel processor with configuration"""
    try:
        import yaml
        config_path = "agents/config/strategy_evolution_config.yaml"

        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)

        gpu_config = config.get('gpu', {})
        print(f"🔧 Loading GPU config: parallel_workers={gpu_config.get('parallel_workers', 2)}, max_workers={gpu_config.get('max_workers', 4)}")
        return GPUParallelProcessor(gpu_config)

    except Exception as e:
        print(f"⚠️ Failed to load GPU config: {e}, using defaults")
        return GPUParallelProcessor()

gpu_parallel_processor = _create_gpu_parallel_processor()
