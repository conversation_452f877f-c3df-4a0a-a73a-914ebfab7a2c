#!/usr/bin/env python3
"""
🏢 NSE 500 Stock Universe Manager
Comprehensive NSE 500 stock universe with sector mapping and market cap classification

Features:
- Complete NSE 500 stock list with tokens
- Detailed sector and industry classification
- Market cap categorization (Large/Mid/Small)
- Index membership tracking (Nifty 50, Nifty 100, etc.)
- Liquidity and trading volume metrics
"""

import json
import logging
import pandas as pd
import polars as pl
from typing import Dict, List, Optional, Set, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path
import requests
from datetime import datetime

logger = logging.getLogger(__name__)

@dataclass
class EnhancedStockInfo:
    """Enhanced stock information with comprehensive details"""
    symbol: str
    token: str
    exchange: str
    company_name: str
    sector: str = ""
    industry: str = ""
    market_cap: str = ""  # Large/Mid/Small
    market_cap_value: float = 0.0  # Actual market cap in crores
    is_active: bool = True
    
    # Index memberships
    nifty_50: bool = False
    nifty_100: bool = False
    nifty_200: bool = False
    nifty_500: bool = True
    
    # Trading metrics
    avg_volume: float = 0.0
    liquidity_score: float = 0.0
    beta: float = 1.0
    
    # Fundamental metrics
    pe_ratio: float = 0.0
    pb_ratio: float = 0.0
    dividend_yield: float = 0.0
    
    # Last updated
    last_updated: str = ""

class NSE500Universe:
    """
    Comprehensive NSE 500 Stock Universe Manager
    """
    
    def __init__(self, config_path: str = "agents/config/nse_500_universe.json"):
        self.config_path = config_path
        self.stocks: Dict[str, EnhancedStockInfo] = {}
        self.sectors: Dict[str, List[str]] = {}
        self.industries: Dict[str, List[str]] = {}
        self.market_caps: Dict[str, List[str]] = {}
        self.indices: Dict[str, List[str]] = {}
        
        # Sector mapping for comprehensive classification
        self.sector_mapping = {
            "Banking": ["Private Banks", "Public Banks", "Small Finance Banks"],
            "Financial Services": ["NBFCs", "Insurance", "AMC", "Housing Finance", "Microfinance"],
            "Information Technology": ["Software", "IT Services", "Hardware", "Semiconductors"],
            "Pharmaceuticals": ["Pharmaceuticals", "Biotechnology", "Healthcare Services"],
            "Automobile": ["Passenger Cars", "Commercial Vehicles", "Two Wheelers", "Auto Components"],
            "Oil & Gas": ["Refineries", "Oil Exploration", "Gas Distribution", "Petrochemicals"],
            "Metals & Mining": ["Steel", "Aluminum", "Copper", "Iron Ore", "Coal"],
            "FMCG": ["Personal Care", "Food Products", "Beverages", "Household Products"],
            "Telecom": ["Telecom Services", "Tower Infrastructure"],
            "Power": ["Power Generation", "Power Transmission", "Renewable Energy"],
            "Infrastructure": ["Construction", "Engineering", "Real Estate"],
            "Chemicals": ["Specialty Chemicals", "Agrochemicals", "Paints", "Fertilizers"],
            "Textiles": ["Textiles", "Apparel", "Home Textiles"],
            "Media & Entertainment": ["Broadcasting", "Print Media", "Digital Media"],
            "Retail": ["Retail", "E-commerce", "Consumer Durables"],
            "Capital Goods": ["Industrial Equipment", "Electrical Equipment", "Defense"]
        }
        
        # Market cap thresholds (in crores)
        self.market_cap_thresholds = {
            "Large": 20000,  # > 20,000 crores
            "Mid": 5000,     # 5,000 - 20,000 crores
            "Small": 0       # < 5,000 crores
        }
    
    def load_nse_500_universe(self) -> bool:
        """Load comprehensive NSE 500 universe"""
        try:
            # Try to load from existing config first
            if Path(self.config_path).exists():
                success = self._load_from_config()
                if success and len(self.stocks) >= 400:  # Minimum threshold
                    logger.info(f"[SUCCESS] Loaded {len(self.stocks)} stocks from existing config")
                    return True
            
            # Generate/update the universe
            return self._generate_nse_500_universe()
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to load NSE 500 universe: {e}")
            return False
    
    def _load_from_config(self) -> bool:
        """Load from existing configuration"""
        try:
            with open(self.config_path, 'r') as f:
                data = json.load(f)
            
            for stock_data in data.get('stocks', []):
                stock = EnhancedStockInfo(**stock_data)
                self.stocks[stock.symbol] = stock
                self._categorize_stock(stock)
            
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to load from config: {e}")
            return False
    
    def _generate_nse_500_universe(self) -> bool:
        """Generate comprehensive NSE 500 universe"""
        try:
            logger.info("[INIT] Generating comprehensive NSE 500 universe...")
            
            # Load from symbols_full.json if available
            symbols_full_path = "agents/config/symbols_full.json"
            if Path(symbols_full_path).exists():
                success = self._load_from_symbols_full(symbols_full_path)
                if success:
                    # Enhance with sector and market cap data
                    self._enhance_with_sector_data()
                    self._enhance_with_market_cap_data()
                    self._enhance_with_index_membership()
                    
                    # Save enhanced universe
                    self._save_to_config()
                    
                    logger.info(f"[SUCCESS] Generated NSE 500 universe with {len(self.stocks)} stocks")
                    return True
            
            # Fallback to manual generation
            return self._generate_manual_universe()
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to generate NSE 500 universe: {e}")
            return False
    
    def _load_from_symbols_full(self, file_path: str) -> bool:
        """Load symbols from symbols_full.json"""
        try:
            with open(file_path, 'r') as f:
                symbols_data = json.load(f)
            
            for symbol_data in symbols_data:
                stock = EnhancedStockInfo(
                    symbol=symbol_data["symbol"],
                    token=symbol_data["token"],
                    exchange=symbol_data["exchange"],
                    company_name=symbol_data.get("company_name", symbol_data["symbol"]),
                    last_updated=datetime.now().isoformat()
                )
                self.stocks[stock.symbol] = stock
            
            logger.info(f"[SUCCESS] Loaded {len(self.stocks)} symbols from {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to load from symbols_full.json: {e}")
            return False
    
    def _enhance_with_sector_data(self):
        """Enhance stocks with sector and industry classification"""
        try:
            # Sector mapping based on common NSE classifications
            sector_assignments = {
                # Banking
                "HDFCBANK": ("Banking", "Private Banks"),
                "ICICIBANK": ("Banking", "Private Banks"),
                "KOTAKBANK": ("Banking", "Private Banks"),
                "AXISBANK": ("Banking", "Private Banks"),
                "SBIN": ("Banking", "Public Banks"),
                "INDUSINDBK": ("Banking", "Private Banks"),
                "BANDHANBNK": ("Banking", "Small Finance Banks"),
                "FEDERALBNK": ("Banking", "Private Banks"),
                "IDFCFIRSTB": ("Banking", "Private Banks"),
                "PNB": ("Banking", "Public Banks"),
                
                # IT
                "TCS": ("Information Technology", "Software"),
                "INFY": ("Information Technology", "Software"),
                "WIPRO": ("Information Technology", "IT Services"),
                "HCLTECH": ("Information Technology", "Software"),
                "TECHM": ("Information Technology", "Software"),
                "LTI": ("Information Technology", "IT Services"),
                "MINDTREE": ("Information Technology", "IT Services"),
                "MPHASIS": ("Information Technology", "IT Services"),
                
                # Pharmaceuticals
                "SUNPHARMA": ("Pharmaceuticals", "Pharmaceuticals"),
                "DRREDDY": ("Pharmaceuticals", "Pharmaceuticals"),
                "CIPLA": ("Pharmaceuticals", "Pharmaceuticals"),
                "DIVISLAB": ("Pharmaceuticals", "Pharmaceuticals"),
                "BIOCON": ("Pharmaceuticals", "Biotechnology"),
                "LUPIN": ("Pharmaceuticals", "Pharmaceuticals"),
                "AUROPHARMA": ("Pharmaceuticals", "Pharmaceuticals"),
                
                # Auto
                "MARUTI": ("Automobile", "Passenger Cars"),
                "TATAMOTORS": ("Automobile", "Commercial Vehicles"),
                "M&M": ("Automobile", "Commercial Vehicles"),
                "BAJAJ-AUTO": ("Automobile", "Two Wheelers"),
                "EICHERMOT": ("Automobile", "Two Wheelers"),
                "HEROMOTOCO": ("Automobile", "Two Wheelers"),
                "TVSMOTOR": ("Automobile", "Two Wheelers"),
                
                # Oil & Gas
                "RELIANCE": ("Oil & Gas", "Refineries"),
                "ONGC": ("Oil & Gas", "Oil Exploration"),
                "IOC": ("Oil & Gas", "Refineries"),
                "BPCL": ("Oil & Gas", "Refineries"),
                "HPCL": ("Oil & Gas", "Refineries"),
                "GAIL": ("Oil & Gas", "Gas Distribution"),
                
                # Metals
                "TATASTEEL": ("Metals & Mining", "Steel"),
                "JSWSTEEL": ("Metals & Mining", "Steel"),
                "HINDALCO": ("Metals & Mining", "Aluminum"),
                "VEDL": ("Metals & Mining", "Iron Ore"),
                "COALINDIA": ("Metals & Mining", "Coal"),
                "SAIL": ("Metals & Mining", "Steel"),
                
                # FMCG
                "HINDUNILVR": ("FMCG", "Personal Care"),
                "ITC": ("FMCG", "Beverages"),
                "NESTLEIND": ("FMCG", "Food Products"),
                "BRITANNIA": ("FMCG", "Food Products"),
                "DABUR": ("FMCG", "Personal Care"),
                "MARICO": ("FMCG", "Personal Care"),
                "GODREJCP": ("FMCG", "Personal Care"),
                
                # Telecom
                "BHARTIARTL": ("Telecom", "Telecom Services"),
                "IDEA": ("Telecom", "Telecom Services"),
                "INDUSINDBK": ("Telecom", "Tower Infrastructure"),
                
                # Add more mappings as needed...
            }
            
            # Apply sector assignments
            for symbol, stock in self.stocks.items():
                if symbol in sector_assignments:
                    sector, industry = sector_assignments[symbol]
                    stock.sector = sector
                    stock.industry = industry
                else:
                    # Default classification based on symbol patterns or company name
                    stock.sector = self._infer_sector_from_name(stock.company_name, symbol)
                    stock.industry = stock.sector  # Default to sector name
                
                self._categorize_stock(stock)
            
            logger.info("[SUCCESS] Enhanced stocks with sector data")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to enhance with sector data: {e}")
    
    def _infer_sector_from_name(self, company_name: str, symbol: str) -> str:
        """Infer sector from company name or symbol"""
        name_lower = company_name.lower()
        symbol_lower = symbol.lower()
        
        # Banking keywords
        if any(keyword in name_lower for keyword in ["bank", "banking"]):
            return "Banking"
        
        # IT keywords
        if any(keyword in name_lower for keyword in ["tech", "software", "systems", "info"]):
            return "Information Technology"
        
        # Pharma keywords
        if any(keyword in name_lower for keyword in ["pharma", "drug", "medicine", "healthcare"]):
            return "Pharmaceuticals"
        
        # Auto keywords
        if any(keyword in name_lower for keyword in ["motor", "auto", "vehicle"]):
            return "Automobile"
        
        # Default
        return "Others"
    
    def _enhance_with_market_cap_data(self):
        """Enhance stocks with market cap classification"""
        try:
            # Market cap assignments for major stocks (in crores)
            market_cap_data = {
                # Large Cap (>20,000 crores)
                "RELIANCE": 1500000, "TCS": 1200000, "HDFCBANK": 800000,
                "INFY": 600000, "ICICIBANK": 500000, "HINDUNILVR": 450000,
                "SBIN": 400000, "BHARTIARTL": 350000, "ITC": 300000,
                "KOTAKBANK": 280000, "LT": 250000, "ASIANPAINT": 240000,
                
                # Add more as needed...
            }
            
            # Classify stocks
            for symbol, stock in self.stocks.items():
                if symbol in market_cap_data:
                    stock.market_cap_value = market_cap_data[symbol]
                else:
                    # Estimate based on position in NSE 500
                    # This is a rough estimation - in production, use real market cap data
                    stock.market_cap_value = max(1000, 50000 - (len(self.stocks) * 100))
                
                # Classify market cap
                if stock.market_cap_value >= self.market_cap_thresholds["Large"]:
                    stock.market_cap = "Large"
                elif stock.market_cap_value >= self.market_cap_thresholds["Mid"]:
                    stock.market_cap = "Mid"
                else:
                    stock.market_cap = "Small"
                
                self._categorize_stock(stock)
            
            logger.info("[SUCCESS] Enhanced stocks with market cap data")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to enhance with market cap data: {e}")
    
    def _enhance_with_index_membership(self):
        """Enhance stocks with index membership information"""
        try:
            # Nifty 50 stocks
            nifty_50_stocks = [
                "RELIANCE", "TCS", "HDFCBANK", "INFY", "ICICIBANK", "HINDUNILVR",
                "SBIN", "BHARTIARTL", "ITC", "KOTAKBANK", "LT", "ASIANPAINT",
                "AXISBANK", "MARUTI", "SUNPHARMA", "ULTRACEMCO", "TITAN", "WIPRO",
                "NESTLEIND", "POWERGRID", "NTPC", "TATAMOTORS", "TECHM", "ONGC",
                "IOC", "TATASTEEL", "HCLTECH", "BAJFINANCE", "COALINDIA", "GRASIM",
                "HINDALCO", "DRREDDY", "EICHERMOT", "INDUSINDBK", "BAJAJFINSV",
                "CIPLA", "BRITANNIA", "HEROMOTOCO", "SHREECEM", "UPL", "DIVISLAB",
                "TATACONSUM", "APOLLOHOSP", "BAJAJ-AUTO", "ADANIPORTS", "JSWSTEEL",
                "M&M", "SBILIFE", "HDFCLIFE", "BPCL"
            ]
            
            # Apply index memberships
            for symbol, stock in self.stocks.items():
                if symbol in nifty_50_stocks:
                    stock.nifty_50 = True
                    stock.nifty_100 = True
                    stock.nifty_200 = True
                    stock.nifty_500 = True
                
                # All stocks in our universe are Nifty 500
                stock.nifty_500 = True
                
                self._categorize_stock(stock)
            
            logger.info("[SUCCESS] Enhanced stocks with index membership data")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to enhance with index membership: {e}")
    
    def _categorize_stock(self, stock: EnhancedStockInfo):
        """Categorize stock into various groups"""
        # Sector categorization
        if stock.sector:
            if stock.sector not in self.sectors:
                self.sectors[stock.sector] = []
            if stock.symbol not in self.sectors[stock.sector]:
                self.sectors[stock.sector].append(stock.symbol)
        
        # Industry categorization
        if stock.industry:
            if stock.industry not in self.industries:
                self.industries[stock.industry] = []
            if stock.symbol not in self.industries[stock.industry]:
                self.industries[stock.industry].append(stock.symbol)
        
        # Market cap categorization
        if stock.market_cap:
            if stock.market_cap not in self.market_caps:
                self.market_caps[stock.market_cap] = []
            if stock.symbol not in self.market_caps[stock.market_cap]:
                self.market_caps[stock.market_cap].append(stock.symbol)
        
        # Index categorization
        if stock.nifty_50:
            if "Nifty50" not in self.indices:
                self.indices["Nifty50"] = []
            if stock.symbol not in self.indices["Nifty50"]:
                self.indices["Nifty50"].append(stock.symbol)
    
    def _generate_manual_universe(self) -> bool:
        """Generate manual universe as fallback"""
        # This would contain a comprehensive manual list
        # For brevity, implementing a basic version
        logger.warning("[WARN] Using manual universe generation - limited stock list")
        return False
    
    def _save_to_config(self):
        """Save enhanced universe to config file"""
        try:
            Path(self.config_path).parent.mkdir(parents=True, exist_ok=True)
            
            data = {
                "metadata": {
                    "total_stocks": len(self.stocks),
                    "last_updated": datetime.now().isoformat(),
                    "sectors": len(self.sectors),
                    "industries": len(self.industries)
                },
                "stocks": [asdict(stock) for stock in self.stocks.values()]
            }
            
            with open(self.config_path, 'w') as f:
                json.dump(data, f, indent=2)
            
            logger.info(f"[SUCCESS] Saved NSE 500 universe to {self.config_path}")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to save config: {e}")
    
    # Getter methods (similar to original StockUniverse but enhanced)
    def get_all_stocks(self) -> List[EnhancedStockInfo]:
        """Get all stocks in universe"""
        return list(self.stocks.values())
    
    def get_stocks_by_sector(self, sector: str) -> List[EnhancedStockInfo]:
        """Get stocks by sector"""
        symbols = self.sectors.get(sector, [])
        return [self.stocks[symbol] for symbol in symbols if symbol in self.stocks]
    
    def get_stocks_by_market_cap(self, market_cap: str) -> List[EnhancedStockInfo]:
        """Get stocks by market cap"""
        symbols = self.market_caps.get(market_cap, [])
        return [self.stocks[symbol] for symbol in symbols if symbol in self.stocks]
    
    def get_nifty_50_stocks(self) -> List[EnhancedStockInfo]:
        """Get Nifty 50 stocks"""
        return [stock for stock in self.stocks.values() if stock.nifty_50]
    
    def get_universe_stats(self) -> Dict[str, any]:
        """Get comprehensive universe statistics"""
        return {
            "total_stocks": len(self.stocks),
            "sectors": len(self.sectors),
            "industries": len(self.industries),
            "sector_breakdown": {sector: len(symbols) for sector, symbols in self.sectors.items()},
            "market_cap_breakdown": {cap: len(symbols) for cap, symbols in self.market_caps.items()},
            "index_breakdown": {index: len(symbols) for index, symbols in self.indices.items()},
            "nifty_50_count": len([s for s in self.stocks.values() if s.nifty_50]),
            "large_cap_count": len(self.market_caps.get("Large", [])),
            "mid_cap_count": len(self.market_caps.get("Mid", [])),
            "small_cap_count": len(self.market_caps.get("Small", []))
        }
