#!/usr/bin/env python3
"""
Optimized Feature Engineering for Historical Data
Uses polars and pyarrow for high-performance feature generation
Works with timestamp, OHLCV data from the Fixed Historical Data Downloader
"""

import os
import sys
import asyncio
import logging
import time
import polars as pl
import pyarrow as pa

from pathlib import Path
from typing import List, Dict, Any, Optional
from concurrent.futures import ThreadPoolExecutor
import warnings
warnings.filterwarnings('ignore')
import numpy as np # Added for Numba CUDA integration

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# GPU acceleration libraries (removed as GPU is not effectively utilized)
NUMBA_CUDA_AVAILABLE = False

# Technical analysis libraries
try:
    import polars_talib as plta
    POLARS_TALIB_AVAILABLE = True
except ImportError:
    POLARS_TALIB_AVAILABLE = False
    print("Warning: polars-talib not available. Install with: pip install polars-talib")

try:
    import talib
    TALIB_AVAILABLE = True
except ImportError:
    TALIB_AVAILABLE = False

try:
    import vectorbt as vbt
    VECTORBT_AVAILABLE = True
except ImportError:
    VECTORBT_AVAILABLE = False
    print("Warning: vectorbt not available. Install with: pip install vectorbt")

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Configure Polars for CPU processing (GPU engine removed as it's not effectively utilized)
try:
    pl.Config.set_engine("cpu")
    logger.info("Polars CPU engine enabled successfully")
except Exception as e:
    logger.warning(f"Could not explicitly set Polars CPU engine: {e}. Default engine will be used.")


class OptimizedFeatureEngineering:
    """
    Optimized Feature Engineering using Polars with GPU acceleration
    Works with historical data files containing: timestamp, open, high, low, close, volume
    """

    def __init__(self, input_dir: str, output_dir: str, chunk_size: int = 100000):
        """Initialize the feature engineering processor"""
        self.input_dir = Path(input_dir)
        self.output_dir = Path(output_dir)
        self.chunk_size = chunk_size

        # GPU optimization settings removed as GPU is not effectively utilized
        self.use_gpu = False

        # Create output directory
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # Check library availability
        if POLARS_TALIB_AVAILABLE:
            logger.info("Using polars-talib for technical indicators")
        else:
            logger.info("Using pure Polars implementations for technical indicators")
        
        logger.info("GPU acceleration explicitly disabled based on performance analysis.")

        # Optimize chunk size for CPU processing
        # For CPU processing, a moderate chunk size is generally good.
        # Keeping the default or slightly increasing it for Polars efficiency.
        self.chunk_size = max(self.chunk_size, 100000) # Ensure a reasonable minimum chunk size for Polars
        logger.info(f"CPU processing enabled - optimized chunk size to {self.chunk_size:,}")

        logger.info(f"Feature Engineering initialized - Chunk size: {self.chunk_size:,}")
        logger.info(f"GPU acceleration enabled: {self.use_gpu}")


    
    def _parse_timestamp_and_create_metadata(self, df: Any, filename: str) -> Any:
        """Parse timestamp and create date, time, stock_name, symboltoken columns"""
        try:
            stock_name = filename.replace('.parquet', '').split('_')[0]

            if isinstance(df, pl.DataFrame):
                # Handle timezone-aware timestamp parsing for Polars
                # Format: 2015-02-02T09:15:00+0530
                # Polars-only: keep IST local clock times; do NOT convert to UTC
                # Strip timezone offset like +0530 and parse as naive local datetime
                df = df.with_columns([
                    pl.col("timestamp")
                      .str.replace(r"\+\d{4}$", "")
                      .str.strptime(pl.Datetime, format="%Y-%m-%dT%H:%M:%S", strict=False)
                      .alias("datetime")
                ])

                # Create metadata columns
                df = df.with_columns([
                    pl.col("datetime").dt.date().cast(pl.String).alias("date"),
                    pl.col("datetime").dt.time().cast(pl.String).alias("time"),
                    pl.col("datetime").dt.hour().alias("hour"),
                    pl.lit(stock_name).alias("stock_name"),
                    pl.lit(0).alias("symboltoken")
                ])



            return df
        except Exception as e:
            logger.error(f"Error parsing timestamp and creating metadata: {e}")
            # Return original dataframe with basic metadata if parsing fails
            try:
                if isinstance(df, pl.DataFrame):
                    df = df.with_columns([
                        pl.lit(stock_name).alias("stock_name"),
                        pl.lit(0).alias("symboltoken")
                    ])

            except Exception:
                pass
            return df
    
    def _add_moving_averages(self, df: Any) -> Any:
        """Add moving average indicators using polars-talib or pure polars"""
        try:
            if isinstance(df, pl.DataFrame):
                if POLARS_TALIB_AVAILABLE:
                    df = df.with_columns([
                        pl.col("close").ta.ema(5).alias("EMA_5"),
                        pl.col("close").ta.ema(9).alias("EMA_9"),
                        pl.col("close").ta.ema(10).alias("EMA_10"),
                        pl.col("close").ta.ema(20).alias("EMA_20"),
                        pl.col("close").ta.ema(50).alias("EMA_50")
                    ])
                else:
                    df = df.with_columns([
                        pl.col("close").ewm_mean(span=5).alias("EMA_5"),
                        pl.col("close").ewm_mean(span=9).alias("EMA_9"),
                        pl.col("close").ewm_mean(span=10).alias("EMA_10"),
                        pl.col("close").ewm_mean(span=20).alias("EMA_20"),
                        pl.col("close").ewm_mean(span=50).alias("EMA_50")
                    ])
            return df
        except Exception as e:
            logger.error(f"Error adding moving averages: {e}")
            return df
    
    def _add_momentum_indicators(self, df: Any) -> Any:
        """Add momentum indicators using polars-talib or pure polars"""
        try:
                if POLARS_TALIB_AVAILABLE:
                    df = df.with_columns([
                        pl.col("close").ta.rsi(14).alias("RSI_14"),
                        pl.col("close").ta.macd(12, 26, 9).struct.field("macd").alias("MACD_Line"),
                        pl.col("close").ta.macd(12, 26, 9).struct.field("macdsignal").alias("Signal_Line"),
                        plta.stoch(pl.col("high"), pl.col("low"), pl.col("close"),
                              fastk_period=14, slowk_period=3, slowd_period=3).struct.field("slowk").alias("Stochastic_K"),
                        plta.stoch(pl.col("high"), pl.col("low"), pl.col("close"),
                              fastk_period=14, slowk_period=3, slowd_period=3).struct.field("slowd").alias("Stochastic_D"),
                        plta.adx(pl.col("high"), pl.col("low"), pl.col("close"), timeperiod=14).alias("ADX_14"),
                        plta.plus_di(pl.col("high"), pl.col("low"), pl.col("close"), timeperiod=14).alias("PlusDI_14"),
                        plta.minus_di(pl.col("high"), pl.col("low"), pl.col("close"), timeperiod=14).alias("MinusDI_14")
                    ])
                else:
                    logger.info("Using pure Polars for momentum indicators (polars-talib not available)")
                    # RSI_14
                    delta = pl.col("close").diff()
                    gain = delta.clip_min(0)
                    loss = (-delta).clip_min(0)
                    alpha = 1.0 / 14
                    avg_gain = gain.ewm_mean(alpha=alpha)
                    avg_loss = loss.ewm_mean(alpha=alpha)
                    rs = avg_gain / avg_loss
                    rsi = 100 - (100 / (1 + rs))
                    df = df.with_columns([rsi.alias("RSI_14")])

                    # MACD_Line, Signal_Line
                    ema_12 = pl.col("close").ewm_mean(span=12)
                    ema_26 = pl.col("close").ewm_mean(span=26)
                    macd_line = ema_12 - ema_26
                    macd_signal = macd_line.ewm_mean(span=9)
                    df = df.with_columns([macd_line.alias("MACD_Line"), macd_signal.alias("Signal_Line")])

                    # Stochastic_K, Stochastic_D
                    high_14 = pl.col("high").rolling_max(window_size=14)
                    low_14 = pl.col("low").rolling_min(window_size=14)
                    stoch_k = 100 * (pl.col("close") - low_14) / (high_14 - low_14)
                    stoch_d = stoch_k.rolling_mean(window_size=3)
                    df = df.with_columns([stoch_k.alias("Stochastic_K"), stoch_d.alias("Stochastic_D")])

                    # ADX_14, PlusDI_14, MinusDI_14
                    high_minus_prev_high = pl.col("high") - pl.col("high").shift(1)
                    prev_low_minus_low = pl.col("low").shift(1) - pl.col("low")

                    plus_dm = pl.when(high_minus_prev_high > prev_low_minus_low) \
                                .then(pl.max_horizontal([high_minus_prev_high, pl.lit(0)])) \
                                .otherwise(pl.lit(0))

                    minus_dm = pl.when(prev_low_minus_low > high_minus_prev_high) \
                                 .then(pl.max_horizontal([prev_low_minus_low, pl.lit(0)])) \
                                 .otherwise(pl.lit(0))

                    tr1 = pl.col("high") - pl.col("low")
                    tr2 = (pl.col("high") - pl.col("close").shift(1)).abs()
                    tr3 = (pl.col("low") - pl.col("close").shift(1)).abs()
                    tr = pl.max_horizontal([tr1, tr2, tr3])

                    atr = tr.ewm_mean(span=14)
                    
                    plus_di = 100 * (plus_dm.ewm_mean(span=14) / atr)
                    minus_di = 100 * (minus_dm.ewm_mean(span=14) / atr)
                    
                    dx = 100 * (plus_di - minus_di).abs() / (plus_di + minus_di)
                    adx = dx.ewm_mean(span=14)

                    df = df.with_columns([
                        adx.alias("ADX_14"),
                        plus_di.alias("PlusDI_14"),
                        minus_di.alias("MinusDI_14")
                    ])
            
                # MACD_Hist
                df = df.with_columns([
                    (pl.col("MACD_Line") - pl.col("Signal_Line")).alias("MACD_Hist")
                ])

            return df
        except Exception as e:
            logger.error(f"Error adding momentum indicators: {e}")
            return df
    
    def _add_volatility_indicators(self, df: Any) -> Any:
        """Add volatility indicators using polars-talib or pure polars"""
        try:
            if isinstance(df, pl.DataFrame):
                if POLARS_TALIB_AVAILABLE:
                    bb_result = plta.bbands(pl.col("close"), timeperiod=20, nbdevup=2, nbdevdn=2)
                    df = df.with_columns([
                        bb_result.struct.field("upperband").alias("Bollinger_Upper"),
                        bb_result.struct.field("middleband").alias("Bollinger_Middle"),
                        bb_result.struct.field("lowerband").alias("Bollinger_Lower")
                    ])
                else:
                    sma_20 = pl.col("close").rolling_mean(window_size=20)
                    std_20 = pl.col("close").rolling_std(window_size=20)
                    df = df.with_columns([
                        (sma_20 + 2 * std_20).alias("Bollinger_Upper"),
                        sma_20.alias("Bollinger_Middle"),
                        (sma_20 - 2 * std_20).alias("Bollinger_Lower")
                    ])

            return df
        except Exception as e:
            logger.error(f"Error adding volatility indicators: {e}")
            return df
    
    def _add_volume_indicators(self, df: Any) -> Any:
        """Add volume-based indicators"""
        try:
            if isinstance(df, pl.DataFrame):
                typical_price = (pl.col("high") + pl.col("low") + pl.col("close")) / 3
                vwap = (typical_price * pl.col("volume")).rolling_sum(window_size=20) / pl.col("volume").rolling_sum(window_size=20)
                df = df.with_columns([vwap.alias("VWAP")])

            return df
        except Exception as e:
            logger.error(f"Error adding volume indicators: {e}")
            return df
    
    def _add_custom_indicators(self, df: Any) -> Any:
        """Add custom indicators: SAR, STC, Fibonacci levels, Entry"""
        try:
            if isinstance(df, pl.DataFrame):
                # SAR Indicator (Parabolic SAR)
                if POLARS_TALIB_AVAILABLE:
                    df = df.with_columns([
                        plta.sar(pl.col("high"), pl.col("low"), acceleration=0.02, maximum=0.2).alias("SAR_Indicator")
                    ])
                else:
                    # Pure Polars implementation for SAR
                    # This is a simplified version and might not be perfectly accurate to TA-Lib's SAR
                    # A full Polars implementation of SAR is quite complex due to stateful nature
                    # For simplicity, we'll use a placeholder or a basic approximation if a full implementation is too much.
                    # For now, let's add a placeholder.
                    logger.info("Using placeholder for SAR_Indicator (pure Polars implementation is complex)")
                    df = df.with_columns([pl.lit(None).alias("SAR_Indicator")])

                # STC_Line (Schaff Trend Cycle)
                # This is a complex indicator. For now, add a placeholder.
                logger.info("Using placeholder for STC_Line (pure Polars implementation is complex)")
                df = df.with_columns([pl.lit(None).alias("STC_Line")])

                # Fibonacci Retracement Levels (based on current High and Low)
                # These are typically calculated from a swing high and swing low,
                # but for a per-candle indicator, we can use the current candle's range.
                # This might not be the standard interpretation of Fibonacci retracement levels
                # as a time-series indicator, but it matches the request for columns.
                price_range = pl.col("high") - pl.col("low")
                df = df.with_columns([
                    (pl.col("high") - (price_range * 0.236)).alias("Fibonacci_23_6"),
                    (pl.col("high") - (price_range * 0.382)).alias("Fibonacci_38_2"),
                    (pl.col("high") - (price_range * 0.618)).alias("Fibonacci_61_8"),
                    (pl.col("high") - (price_range * 0.786)).alias("Fibonacci_78_6")
                ])

                # Entry (placeholder for now)
                df = df.with_columns([pl.lit(None).alias("Entry")])

            return df
        except Exception as e:
            logger.error(f"Error adding custom indicators: {e}")
            return df
    
    def _process_single_file(self, df: pl.DataFrame, filename: str) -> pl.DataFrame:
        """Process a single file's data with all technical indicators, potentially using GPU"""
        try:
            # Force Polars-only path per requirement
            processed_df = df

            # Parse timestamp and create metadata columns
            processed_df = self._parse_timestamp_and_create_metadata(processed_df, filename)
            
            # Sort by datetime for proper technical indicator calculation
            # cuDF sort_values is equivalent to Polars sort
            processed_df = processed_df.sort("datetime")

            # Add all features
            processed_df = self._add_moving_averages(processed_df)
            processed_df = self._add_momentum_indicators(processed_df)
            processed_df = self._add_volatility_indicators(processed_df)
            processed_df = self._add_volume_indicators(processed_df)
            processed_df = self._add_custom_indicators(processed_df)

            return processed_df

        except Exception as e:
            logger.error(f"Error processing file {filename}: {e}")
            # Return original dataframe with null columns for missing features
            return self._add_missing_columns(df, filename)

    def _add_missing_columns(self, df: pl.DataFrame, filename: str) -> pl.DataFrame:
        """Add missing feature columns with null values"""
        try:
            # First add metadata if missing
            if "stock_name" not in df.columns:
                df = self._parse_timestamp_and_create_metadata(df, filename)
            
            expected_columns = [
                'date', 'time', 'hour', 'stock_name', 'symboltoken', 'open', 'high', 'low', 'close', 'volume', 'datetime',
                'EMA_5', 'EMA_9', 'EMA_10', 'EMA_20', 'EMA_50',
                'RSI_14', 'MACD_Line', 'Signal_Line', 'Stochastic_K', 'Stochastic_D', 'ADX_14', 'PlusDI_14', 'MinusDI_14', 'MACD_Hist',
                'Bollinger_Upper', 'Bollinger_Middle', 'Bollinger_Lower',
                'VWAP',
                'SAR_Indicator', 'STC_Line', 'Fibonacci_23_6', 'Fibonacci_38_2', 'Fibonacci_61_8', 'Fibonacci_78_6', 'Entry'
            ]

            # Add missing columns with null values
            for col in expected_columns:
                if col not in df.columns:
                    df = df.with_columns([pl.lit(None).alias(col)])

            return df
        except Exception as e:
            logger.error(f"Error adding missing columns: {e}")
            return df
    
    def process_file_with_delay(self, input_file: Path, worker_id: int, delay: float = 0.02) -> bool:
        """Process a single file with worker delay"""
        # Add delay between workers for resource management
        time.sleep(worker_id * delay)
        return self.process_file(input_file)
    
    def process_file(self, input_file: Path) -> bool:
        """Process a single file"""
        try:
            logger.info(f"Processing file: {input_file.name}")

            # Read file
            df = pl.read_parquet(input_file)
            logger.info(f"Loaded {len(df):,} rows from {input_file.name}")

            # Check if file has required columns
            required_cols = ["timestamp", "open", "high", "low", "close", "volume"]
            missing_cols = [col for col in required_cols if col not in df.columns]
            if missing_cols:
                logger.error(f"Missing required columns: {missing_cols}")
                return False

            # Process the file
            logger.info("Processing features...")
            processed_df = self._process_single_file(df, input_file.name)

            # Rename original OHLCV columns to match the requested output format
            processed_df = processed_df.rename({"open": "Open", "high": "High", "low": "Low", "close": "Close", "volume": "Volume"})

            # Define the final list of columns to keep
            final_output_columns = [
                'timestamp', 'Open', 'High', 'Low', 'Close', 'Volume',
                'ADX_14', 'Bollinger_Lower', 'Bollinger_Middle', 'Bollinger_Upper',
                'EMA_10', 'EMA_20', 'EMA_5', 'EMA_50', 'EMA_9', 'Entry',
                'Fibonacci_23_6', 'Fibonacci_38_2', 'Fibonacci_61_8', 'Fibonacci_78_6',
                'MACD_Hist', 'MACD_Line', 'MinusDI_14', 'PlusDI_14', 'RSI_14',
                'SAR_Indicator', 'STC_Line', 'Signal_Line', 'Stochastic_D', 'Stochastic_K', 'VWAP'
            ]

            # Add missing columns with null values if any are still missing after processing
            for col in final_output_columns:
                if col not in processed_df.columns:
                    processed_df = processed_df.with_columns([pl.lit(None).alias(col)])

            # Select only the final desired columns in the specified order
            final_df = processed_df.select(final_output_columns)

            # Save output with features_ prefix
            output_file = self.output_dir / f"features_{input_file.name}"
            final_df.write_parquet(output_file, compression="zstd", compression_level=15)

            logger.info(f"[SUCCESS] Saved {len(final_df):,} rows with {len(final_df.columns)} features to {output_file}")
            return True

        except Exception as e:
            logger.error(f"[ERROR] Error processing file {input_file}: {e}")
            return False
    
    def process_all_files(self) -> bool:
        """Process all parquet files using ThreadPoolExecutor with 6 workers"""
        try:
            # Find all parquet files that don't start with "features_"
            parquet_files = [f for f in self.input_dir.glob("*.parquet") if not f.name.startswith("features_")]

            if not parquet_files:
                logger.error(f"No historical parquet files found in {self.input_dir}")
                return False

            logger.info(f"Found {len(parquet_files)} files to process")

            # Optimize worker count for CPU processing
            # For CPU processing, use more workers, typically based on CPU core count.
            # Using 20 workers as a good balance for general CPU workloads.
            max_workers = 20
            delay = 0.02
            logger.info("Using CPU-optimized ThreadPoolExecutor with 20 workers and 0.02s delay")

            # Process files using ThreadPoolExecutor
            success_count = 0
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # Submit tasks with worker delays
                futures = []
                for i, file_path in enumerate(parquet_files):
                    future = executor.submit(self.process_file_with_delay, file_path, i, delay)
                    futures.append(future)
                
                # Collect results
                for future in futures:
                    try:
                        success = future.result()
                        if success:
                            success_count += 1
                    except Exception as e:
                        logger.error(f"Worker failed: {e}")

            logger.info(f"[SUCCESS] Successfully processed {success_count}/{len(parquet_files)} files")
            return success_count > 0

        except Exception as e:
            logger.error(f"[ERROR] Error processing files: {e}")
            return False

def main():
    """Main function"""
    import argparse

    parser = argparse.ArgumentParser(
        description='Optimized Feature Engineering using Polars',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python optimized_feature_engineering.py --input-dir data/historical --output-dir data/historical
  python optimized_feature_engineering.py --input-dir data/historical --output-dir data/historical --chunk-size 200000
        """
    )
    parser.add_argument('--input-dir', type=str, default='data/historical',
                       help='Input directory containing parquet files (default: data/historical)')
    parser.add_argument('--output-dir', type=str, default='data/features',
                       help='Output directory for feature files (default: data/historical)')
    parser.add_argument('--chunk-size', type=int, default=500000,
                       help='Chunk size for processing large files (default: 500000)')

    args = parser.parse_args()

    logger.info("[INIT] Starting Optimized Feature Engineering")
    logger.info(f"[FOLDER] Input directory: {args.input_dir}")
    logger.info(f"[FOLDER] Output directory: {args.output_dir}")
    logger.info(f"[STATUS] Chunk size: {args.chunk_size:,}")
    logger.info(f"[CONFIG] Polars-TAlib available: {POLARS_TALIB_AVAILABLE}")

    # Create processor and run
    processor = OptimizedFeatureEngineering(args.input_dir, args.output_dir, args.chunk_size)
    success = processor.process_all_files()

    if success:
        logger.info("[SUCCESS] Feature engineering completed successfully!")
        logger.info("[METRICS] Features generated with comprehensive technical indicators")
    else:
        logger.error("[ERROR] Feature engineering failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
