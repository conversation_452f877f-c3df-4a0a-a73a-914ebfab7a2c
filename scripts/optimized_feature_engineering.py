#!/usr/bin/env python3
"""
Optimized Feature Engineering for Historical Data
Uses polars and numba for high-performance feature generation
Works with timestamp, OHLCV data from the Fixed Historical Data Downloader
"""

import os
import sys
import logging
import time

# Configure threading to avoid oversubscription when using process-level parallelism
# Override via env: FEATURE_INNER_THREADS, FEATURE_RAYON_THREADS, FEATURE_NUMEXPR_THREADS
_inner_threads = os.getenv("FEATURE_INNER_THREADS") or "1"
os.environ.setdefault("RAYON_NUM_THREADS", os.getenv("FEATURE_RAYON_THREADS", _inner_threads))
os.environ.setdefault("POLARS_MAX_THREADS", os.getenv("FEATURE_RAYON_THREADS", _inner_threads))
os.environ.setdefault("NUMEXPR_NUM_THREADS", os.getenv("FEATURE_NUMEXPR_THREADS", _inner_threads))
os.environ.setdefault("NUMEXPR_MAX_THREADS", os.getenv("FEATURE_NUMEXPR_THREADS", _inner_threads))
os.environ.setdefault("OMP_NUM_THREADS", _inner_threads)
os.environ.setdefault("OPENBLAS_NUM_THREADS", _inner_threads)
os.environ.setdefault("MKL_NUM_THREADS", _inner_threads)
os.environ.setdefault("NUMBA_NUM_THREADS", _inner_threads)

import polars as pl
import numpy as np
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from concurrent.futures import ProcessPoolExecutor, as_completed
import warnings
warnings.filterwarnings('ignore')

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Performance optimization libraries
try:
    from numba import jit, prange
    NUMBA_AVAILABLE = True
except ImportError:
    NUMBA_AVAILABLE = False
    print("Warning: Numba not available. Install with: pip install numba")

# Technical analysis libraries
try:
    import talib
    TALIB_AVAILABLE = True
except ImportError:
    TALIB_AVAILABLE = False
    print("Warning: TA-Lib not available. Using pure implementations.")

try:
    import polars_talib as pl_talib
    POLARS_TALIB_AVAILABLE = True
except ImportError:
    POLARS_TALIB_AVAILABLE = False
    print("Warning: polars-talib not available. Install with: pip install polars-talib")



# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)





class OptimizedFeatureEngineering:
    """
    Optimized Feature Engineering using Polars with Numba acceleration
    """

    def __init__(self, input_dir: str, output_dir: str, chunk_size: int = 100000):
        """Initialize the feature engineering processor"""
        self.input_dir = Path(input_dir)
        self.output_dir = Path(output_dir)
        self.chunk_size = chunk_size

        # Create output directory
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # Log configuration
        logger.info(f"Feature Engineering initialized")
        logger.info(f"Input directory: {self.input_dir}")
        logger.info(f"Output directory: {self.output_dir}")
        logger.info(f"Chunk size: {self.chunk_size:,}")
        logger.info(f"Numba available: {NUMBA_AVAILABLE}")
        logger.info(f"TA-Lib available: {TALIB_AVAILABLE}")
        logger.info(f"Polars-TA-Lib available: {POLARS_TALIB_AVAILABLE}")

        # Output compression configuration (tuneable via env vars)
        self.compression = os.getenv("FEATURE_COMPRESSION", "zstd")
        try:
            self.compression_level = int(os.getenv("FEATURE_COMPRESSION_LEVEL", "15"))
        except Exception:
            self.compression_level = 15
        logger.info(f"Parquet compression: {self.compression} (level={self.compression_level})")

    def _calculate_technical_indicators(self, df: pl.DataFrame) -> pl.DataFrame:
        """Calculate all technical indicators efficiently"""

        # Numba-accelerated functions for custom indicators
        # Defined here to be JIT-compiled on first use, not at script startup
        if NUMBA_AVAILABLE:
            @jit(nopython=True, parallel=True, cache=True)
            def calculate_parabolic_sar(high: np.ndarray, low: np.ndarray,
                                        acceleration: float = 0.02,
                                        maximum: float = 0.2) -> np.ndarray:
                """
                Calculate Parabolic SAR using Numba for acceleration
                """
                n = len(high)
                sar = np.zeros(n)
                ep = np.zeros(n)
                af = np.zeros(n)
                trend = np.ones(n)  # 1 for uptrend, -1 for downtrend

                # Initialize
                sar[0] = low[0]
                ep[0] = high[0]
                af[0] = acceleration

                for i in range(1, n):
                    # Previous values
                    prev_sar = sar[i-1]
                    prev_ep = ep[i-1]
                    prev_af = af[i-1]
                    prev_trend = trend[i-1]

                    # Calculate SAR
                    sar[i] = prev_sar + prev_af * (prev_ep - prev_sar)

                    # Check for reversal
                    if prev_trend == 1:  # Uptrend
                        if low[i] <= sar[i]:
                            # Reversal to downtrend
                            trend[i] = -1
                            sar[i] = prev_ep
                            ep[i] = low[i]
                            af[i] = acceleration
                        else:
                            trend[i] = 1
                            if high[i] > prev_ep:
                                ep[i] = high[i]
                                af[i] = min(prev_af + acceleration, maximum)
                            else:
                                ep[i] = prev_ep
                                af[i] = prev_af
                            # Make sure SAR is below the last two lows
                            sar[i] = min(sar[i], low[i-1])
                            if i > 1:
                                sar[i] = min(sar[i], low[i-2])
                    else:  # Downtrend
                        if high[i] >= sar[i]:
                            # Reversal to uptrend
                            trend[i] = 1
                            sar[i] = prev_ep
                            ep[i] = high[i]
                            af[i] = acceleration
                        else:
                            trend[i] = -1
                            if low[i] < prev_ep:
                                ep[i] = low[i]
                                af[i] = min(prev_af + acceleration, maximum)
                            else:
                                ep[i] = prev_ep
                                af[i] = prev_af
                            # Make sure SAR is above the last two highs
                            sar[i] = max(sar[i], high[i-1])
                            if i > 1:
                                sar[i] = max(sar[i], high[i-2])

                return sar

            @jit(nopython=True, parallel=True, cache=True)
            def calculate_stc(close: np.ndarray, fast_period: int = 23,
                             slow_period: int = 50, cycle: int = 10,
                             smooth1: int = 3, smooth2: int = 3) -> np.ndarray:
                """
                Calculate Schaff Trend Cycle (STC) indicator using Numba
                """
                n = len(close)
                stc = np.zeros(n)

                # Calculate EMAs
                ema_fast = np.zeros(n)
                ema_slow = np.zeros(n)

                # Initialize EMAs
                ema_fast[0] = close[0]
                ema_slow[0] = close[0]

                # EMA multipliers
                mult_fast = 2.0 / (fast_period + 1)
                mult_slow = 2.0 / (slow_period + 1)

                # Calculate EMAs
                for i in range(1, n):
                    ema_fast[i] = close[i] * mult_fast + ema_fast[i-1] * (1 - mult_fast)
                    ema_slow[i] = close[i] * mult_slow + ema_slow[i-1] * (1 - mult_slow)

                # Calculate MACD
                macd = ema_fast - ema_slow

                # Calculate first stochastic of MACD
                stoch_k1 = np.zeros(n)
                for i in range(cycle - 1, n):
                    min_val = np.min(macd[i - cycle + 1:i + 1])
                    max_val = np.max(macd[i - cycle + 1:i + 1])
                    if max_val != min_val:
                        stoch_k1[i] = 100 * (macd[i] - min_val) / (max_val - min_val)
                    else:
                        stoch_k1[i] = 50

                # Smooth first stochastic (first %D)
                stoch_d1 = np.zeros(n)
                for i in range(smooth1 - 1, n):
                    stoch_d1[i] = np.mean(stoch_k1[i - smooth1 + 1:i + 1])

                # Calculate second stochastic of first %D
                stoch_k2 = np.zeros(n)
                for i in range(cycle - 1, n):
                    min_val = np.min(stoch_d1[i - cycle + 1:i + 1])
                    max_val = np.max(stoch_d1[i - cycle + 1:i + 1])
                    if max_val != min_val:
                        stoch_k2[i] = 100 * (stoch_d1[i] - min_val) / (max_val - min_val)
                    else:
                        stoch_k2[i] = 50

                # Smooth second stochastic (STC)
                for i in range(smooth2 - 1, n):
                    stc[i] = np.mean(stoch_k2[i - smooth2 + 1:i + 1])

                return stc

            @jit(nopython=True, parallel=True, cache=True)
            def calculate_fibonacci_levels(high: np.ndarray, low: np.ndarray,
                                          lookback: int = 20) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
                """
                Calculate Fibonacci retracement levels based on rolling high/low
                """
                n = len(high)
                fib_236 = np.zeros(n)
                fib_382 = np.zeros(n)
                fib_618 = np.zeros(n)
                fib_786 = np.zeros(n)

                for i in range(lookback - 1, n):
                    period_high = np.max(high[i - lookback + 1:i + 1])
                    period_low = np.min(low[i - lookback + 1:i + 1])
                    price_range = period_high - period_low

                    fib_236[i] = period_high - (price_range * 0.236)
                    fib_382[i] = period_high - (price_range * 0.382)
                    fib_618[i] = period_high - (price_range * 0.618)
                    fib_786[i] = period_high - (price_range * 0.786)

                return fib_236, fib_382, fib_618, fib_786

            @jit(nopython=True, parallel=True, cache=True)
            def calculate_entry_signal(close: np.ndarray, ema_5: np.ndarray, ema_20: np.ndarray,
                                      rsi: np.ndarray, macd_hist: np.ndarray) -> np.ndarray:
                """
                Calculate entry signal based on multiple conditions
                1 for bullish, -1 for bearish, 0 for neutral
                """
                n = len(close)
                entry = np.zeros(n)

                for i in range(1, n):
                    bullish_score = 0
                    bearish_score = 0

                    # EMA crossover
                    if ema_5[i] > ema_20[i] and ema_5[i-1] <= ema_20[i-1]:
                        bullish_score += 2
                    elif ema_5[i] < ema_20[i] and ema_5[i-1] >= ema_20[i-1]:
                        bearish_score += 2

                    # RSI conditions
                    if rsi[i] > 30 and rsi[i] < 70:
                        if rsi[i] > rsi[i-1]:
                            bullish_score += 1
                        else:
                            bearish_score += 1
                    elif rsi[i] <= 30:
                        bullish_score += 1  # Oversold
                    elif rsi[i] >= 70:
                        bearish_score += 1  # Overbought

                    # MACD histogram
                    if macd_hist[i] > 0 and macd_hist[i] > macd_hist[i-1]:
                        bullish_score += 1
                    elif macd_hist[i] < 0 and macd_hist[i] < macd_hist[i-1]:
                        bearish_score += 1

                    # Determine entry signal
                    if bullish_score > bearish_score and bullish_score >= 2:
                        entry[i] = 1
                    elif bearish_score > bullish_score and bearish_score >= 2:
                        entry[i] = -1
                    else:
                        entry[i] = 0

                return entry

        if not POLARS_TALIB_AVAILABLE:
            logger.error("Critical dependency not found: 'polars-talib' is not installed.")
            logger.error("This script relies on 'polars-talib' for efficient feature calculation.")
            logger.error("Please install it by running: pip install polars-talib")
            logger.error("Note: You may also need to install the underlying 'ta-lib' C library first.")
            raise ImportError("Missing required library: polars-talib. Please see the logs for installation instructions.")

        if POLARS_TALIB_AVAILABLE:
            # Use polars-talib for all indicators if available
            df = df.with_columns([
                pl_talib.ema(pl.col("close"), period).alias(f'ema_{period}') for period in [5, 9, 10, 20, 50]
            ])
            df = df.with_columns(pl_talib.rsi(pl.col("close"), 14).alias('rsi_14'))

            macd_line = pl_talib.ema(pl.col("close"), 12) - pl_talib.ema(pl.col("close"), 26)
            signal_line = pl_talib.ema(macd_line, 9)
            macd_hist = macd_line - signal_line

            df = df.with_columns([
                macd_line.alias('macd_line'),
                signal_line.alias('signal_line'),
                macd_hist.alias('macd_hist')
            ])

            stoch_output = pl_talib.stoch(pl.col("high"), pl.col("low"), pl.col("close"),
                                          fastk_period=14, slowk_period=3, slowk_matype=0,
                                          slowd_period=3, slowd_matype=0)
            df = df.with_columns([
                stoch_output.struct.field("slowk").alias('stochastic_k'),
                stoch_output.struct.field("slowd").alias('stochastic_d')
            ])

            df = df.with_columns(pl_talib.adx(pl.col("high"), pl.col("low"), pl.col("close"), 14).alias('adx_14'))
            df = df.with_columns(pl_talib.plus_di(pl.col("high"), pl.col("low"), pl.col("close"), 14).alias('plusdi_14'))
            df = df.with_columns(pl_talib.minus_di(pl.col("high"), pl.col("low"), pl.col("close"), 14).alias('minusdi_14'))

            sma_20 = pl.col("close").rolling_mean(window_size=20)
            std_20 = pl.col("close").rolling_std(window_size=20)

            df = df.with_columns([
                (sma_20 + 2 * std_20).alias('bollinger_upper'),
                sma_20.alias('bollinger_middle'),
                (sma_20 - 2 * std_20).alias('bollinger_lower')
            ])

            # VWAP
            typical_price = (pl.col("high") + pl.col("low") + pl.col("close")) / 3
            cumulative_tpv = (typical_price * pl.col("volume")).cum_sum()
            cumulative_volume = pl.col("volume").cum_sum()
            df = df.with_columns(
                (cumulative_tpv / cumulative_volume).alias('vwap')
            )

            # Parabolic SAR
            if NUMBA_AVAILABLE:
                sar_arr = calculate_parabolic_sar(df['high'].to_numpy(), df['low'].to_numpy())
                df = df.with_columns(pl.Series("sar_indicator", sar_arr))
            else:
                # Fallback to talib or simplified if Numba not available
                if TALIB_AVAILABLE:
                    sar_arr = talib.SAR(df['high'].to_numpy(), df['low'].to_numpy(), acceleration=0.02, maximum=0.2)
                    df = df.with_columns(pl.Series("sar_indicator", sar_arr))
                else:
                    # Simplified SAR without Numba or TA-Lib
                    sar = np.zeros_like(df['close'].to_numpy())
                    sar[0] = df['low'].to_numpy()[0]
                    af = 0.02
                    ep = df['high'].to_numpy()[0]
                    trend = 1

                    for i in range(1, len(df['close'].to_numpy())):
                        if trend == 1:
                            sar[i] = sar[i-1] + af * (ep - sar[i-1])
                            if df['low'].to_numpy()[i] <= sar[i]:
                                trend = -1
                                sar[i] = ep
                                ep = df['low'].to_numpy()[i]
                                af = 0.02
                            else:
                                if df['high'].to_numpy()[i] > ep:
                                    ep = df['high'].to_numpy()[i]
                                    af = min(af + 0.02, 0.2)
                                sar[i] = min(sar[i], df['low'].to_numpy()[i-1])
                        else:
                            sar[i] = sar[i-1] + af * (ep - sar[i-1])
                            if df['high'].to_numpy()[i] >= sar[i]:
                                trend = 1
                                sar[i] = ep
                                ep = df['high'].to_numpy()[i]
                                af = 0.02
                            else:
                                if df['low'].to_numpy()[i] < ep:
                                    ep = df['low'].to_numpy()[i]
                                    af = min(af + 0.02, 0.2)
                                sar[i] = max(sar[i], df['high'].to_numpy()[i-1])
                    df = df.with_columns(pl.Series("sar_indicator", sar))

            # Schaff Trend Cycle
            if NUMBA_AVAILABLE:
                stc_arr = calculate_stc(df['close'].to_numpy())
                df = df.with_columns(pl.Series("stc_line", stc_arr))
            else:
                # Simplified STC calculation
                ema_23 = df.with_columns(pl_talib.ema(pl.col("close"), 23).alias("ema_23"))["ema_23"].to_numpy()
                ema_50 = df.with_columns(pl_talib.ema(pl.col("close"), 50).alias("ema_50"))["ema_50"].to_numpy()
                macd_stc = ema_23 - ema_50

                stc_k1 = np.zeros_like(macd_stc)
                for i in range(9, len(macd_stc)):
                    min_val = np.min(macd_stc[i-9:i+1])
                    max_val = np.max(macd_stc[i-9:i+1])
                    if max_val != min_val:
                        stc_k1[i] = 100 * (macd_stc[i] - min_val) / (max_val - min_val)
                    else:
                        stc_k1[i] = 50
                stc_d1 = np.convolve(stc_k1, np.ones(3)/3, mode='same')

                stc_k2 = np.zeros_like(stc_d1)
                for i in range(9, len(stc_d1)):
                    min_val = np.min(stc_d1[i-9:i+1])
                    max_val = np.max(stc_d1[i-9:i+1])
                    if max_val != min_val:
                        stc_k2[i] = 100 * (stc_d1[i] - min_val) / (max_val - min_val)
                    else:
                        stc_k2[i] = 50
                results['stc_line'] = np.convolve(stc_k2, np.ones(3)/3, mode='same')
                df = df.with_columns(pl.Series("stc_line", results['stc_line']))

            # Fibonacci Levels
            if NUMBA_AVAILABLE:
                fib_236, fib_382, fib_618, fib_786 = calculate_fibonacci_levels(df['high'].to_numpy(), df['low'].to_numpy(), lookback=20)
                df = df.with_columns([
                    pl.Series("fibonacci_23_6", fib_236),
                    pl.Series("fibonacci_38_2", fib_382),
                    pl.Series("fibonacci_61_8", fib_618),
                    pl.Series("fibonacci_78_6", fib_786)
                ])
            else:
                # Calculate Fibonacci levels using rolling window
                lookback = 20
                fib_236 = np.zeros_like(df['close'].to_numpy())
                fib_382 = np.zeros_like(df['close'].to_numpy())
                fib_618 = np.zeros_like(df['close'].to_numpy())
                fib_786 = np.zeros_like(df['close'].to_numpy())

                for i in range(lookback-1, len(df['close'].to_numpy())):
                    period_high = np.max(df['high'].to_numpy()[i-lookback+1:i+1])
                    period_low = np.min(df['low'].to_numpy()[i-lookback+1:i+1])
                    price_range = period_high - period_low

                    fib_236[i] = period_high - (price_range * 0.236)
                    fib_382[i] = period_high - (price_range * 0.382)
                    fib_618[i] = period_high - (price_range * 0.618)
                    fib_786[i] = period_high - (price_range * 0.786)

                df = df.with_columns([
                    pl.Series("fibonacci_23_6", fib_236),
                    pl.Series("fibonacci_38_2", fib_382),
                    pl.Series("fibonacci_61_8", fib_618),
                    pl.Series("fibonacci_78_6", fib_786)
                ])

            # Entry Signal
            if NUMBA_AVAILABLE:
                entry_arr = calculate_entry_signal(
                    df['close'].to_numpy(),
                    df['ema_5'].to_numpy(),
                    df['ema_20'].to_numpy(),
                    df['rsi_14'].to_numpy(),
                    df['macd_hist'].to_numpy()
                )
                df = df.with_columns(pl.Series("entry", entry_arr))
            else:
                # Simple entry signal without Numba
                entry = np.zeros_like(df['close'].to_numpy())
                for i in range(1, len(df['close'].to_numpy())):
                    bullish_score = 0
                    bearish_score = 0

                    # EMA crossover
                    if df['ema_5'][i] > df['ema_20'][i] and df['ema_5'][i-1] <= df['ema_20'][i-1]:
                        bullish_score += 2
                    elif df['ema_5'][i] < df['ema_20'][i] and df['ema_5'][i-1] >= df['ema_20'][i-1]:
                        bearish_score += 2

                    # RSI conditions
                    if df['rsi_14'][i] > 30 and df['rsi_14'][i] < 70:
                        if df['rsi_14'][i] > df['rsi_14'][i-1]:
                            bullish_score += 1
                        else:
                            bearish_score += 1
                    elif df['rsi_14'][i] <= 30:
                        bullish_score += 1
                    elif df['rsi_14'][i] >= 70:
                        bearish_score += 1

                    # MACD histogram
                    if df['macd_hist'][i] > 0 and df['macd_hist'][i] > df['macd_hist'][i-1]:
                        bullish_score += 1
                    elif df['macd_hist'][i] < 0 and df['macd_hist'][i] < df['macd_hist'][i-1]:
                        bearish_score += 1

                    # Determine entry signal
                    if bullish_score > bearish_score and bullish_score >= 2:
                        entry[i] = 1
                    elif bearish_score > bullish_score and bearish_score >= 2:
                        entry[i] = -1
                    else:
                        entry[i] = 0
                df = df.with_columns(pl.Series("entry", entry))

            return df

        else:
            # Fallback to original numpy/talib implementation if polars-talib is not available
            open_arr = df['open'].to_numpy()
            high_arr = df['high'].to_numpy()
            low_arr = df['low'].to_numpy()
            close_arr = df['close'].to_numpy()
            volume_arr = df['volume'].to_numpy()

            results = {}

            # EMAs - using vectorized operations
            for period in [5, 9, 10, 20, 50]:
                if TALIB_AVAILABLE:
                    results[f'ema_{period}'] = talib.EMA(close_arr, timeperiod=period)
                else:
                    alpha = 2.0 / (period + 1)
                    ema = np.zeros_like(close_arr)
                    ema[0] = close_arr[0]
                    for i in range(1, len(close_arr)):
                        ema[i] = close_arr[i] * alpha + ema[i-1] * (1 - alpha)
                    results[f'ema_{period}'] = ema

            # RSI
            if TALIB_AVAILABLE:
                results['rsi_14'] = talib.RSI(close_arr, timeperiod=14)
            else:
                delta = np.diff(close_arr, prepend=close_arr[0])
                gain = np.where(delta > 0, delta, 0)
                loss = np.where(delta < 0, -delta, 0)

                avg_gain = np.zeros_like(close_arr)
                avg_loss = np.zeros_like(close_arr)
                avg_gain[14] = np.mean(gain[1:15])
                avg_loss[14] = np.mean(loss[1:15])

                for i in range(15, len(close_arr)):
                    avg_gain[i] = (avg_gain[i-1] * 13 + gain[i]) / 14
                    avg_loss[i] = (avg_loss[i-1] * 13 + loss[i]) / 14

                rs = np.divide(avg_gain, avg_loss, out=np.zeros_like(avg_gain), where=avg_loss!=0)
                results['rsi_14'] = 100 - (100 / (1 + rs))

            # MACD
            if TALIB_AVAILABLE:
                macd, signal, hist = talib.MACD(close_arr, fastperiod=12, slowperiod=26, signalperiod=9)
                results['macd_line'] = macd
                results['signal_line'] = signal
                results['macd_hist'] = hist
            else:
                ema_12 = results['ema_10']  # We'll recalculate
                ema_26 = results['ema_20']  # We'll recalculate

                # Calculate proper EMAs for MACD
                alpha_12 = 2.0 / 13
                alpha_26 = 2.0 / 27
                ema_12 = np.zeros_like(close_arr)
                ema_26 = np.zeros_like(close_arr)
                ema_12[0] = close_arr[0]
                ema_26[0] = close_arr[0]

                for i in range(1, len(close_arr)):
                    ema_12[i] = close_arr[i] * alpha_12 + ema_12[i-1] * (1 - alpha_12)
                    ema_26[i] = close_arr[i] * alpha_26 + ema_26[i-1] * (1 - alpha_26)

                macd = ema_12 - ema_26

                alpha_9 = 2.0 / 10
                signal = np.zeros_like(macd)
                signal[0] = macd[0]
                for i in range(1, len(macd)):
                    signal[i] = macd[i] * alpha_9 + signal[i-1] * (1 - alpha_9)

                results['macd_line'] = macd
                results['signal_line'] = signal
                results['macd_hist'] = macd - signal

            # Stochastic
            if TALIB_AVAILABLE:
                slowk, slowd = talib.STOCH(high_arr, low_arr, close_arr,
                                           fastk_period=14, slowk_period=3,
                                           slowk_matype=0, slowd_period=3, slowd_matype=0)
                results['stochastic_k'] = slowk
                results['stochastic_d'] = slowd
            else:
                stoch_k = np.zeros_like(close_arr)
                for i in range(13, len(close_arr)):
                    high_14 = np.max(high_arr[i-13:i+1])
                    low_14 = np.min(low_arr[i-13:i+1])
                    if high_14 != low_14:
                        stoch_k[i] = 100 * (close_arr[i] - low_14) / (high_14 - low_14)
                    else:
                        stoch_k[i] = 50

                # Smooth K to get slow K
                slow_k = np.zeros_like(stoch_k)
                for i in range(2, len(stoch_k)):
                    slow_k[i] = np.mean(stoch_k[i-2:i+1])

                # Smooth slow K to get slow D
                slow_d = np.zeros_like(slow_k)
                for i in range(2, len(slow_k)):
                    slow_d[i] = np.mean(slow_k[i-2:i+1])

                results['stochastic_k'] = slow_k
                results['stochastic_d'] = slow_d

            # ADX, +DI, -DI
            if TALIB_AVAILABLE:
                results['adx_14'] = talib.ADX(high_arr, low_arr, close_arr, timeperiod=14)
                results['plusdi_14'] = talib.PLUS_DI(high_arr, low_arr, close_arr, timeperiod=14)
                results['minusdi_14'] = talib.MINUS_DI(high_arr, low_arr, close_arr, timeperiod=14)
            else:
                # Calculate True Range
                tr1 = high_arr - low_arr
                tr2 = np.abs(high_arr - np.roll(close_arr, 1))
                tr3 = np.abs(low_arr - np.roll(close_arr, 1))
                tr = np.maximum(tr1, np.maximum(tr2, tr3))
                tr[0] = high_arr[0] - low_arr[0]

                # Calculate directional movements
                high_diff = high_arr - np.roll(high_arr, 1)
                low_diff = np.roll(low_arr, 1) - low_arr

                plus_dm = np.where((high_diff > low_diff) & (high_diff > 0), high_diff, 0)
                minus_dm = np.where((low_diff > high_diff) & (low_diff > 0), low_diff, 0)

                # Smooth TR and DMs
                atr = np.zeros_like(tr)
                plus_di = np.zeros_like(tr)
                minus_di = np.zeros_like(tr)

                atr[13] = np.mean(tr[:14])
                plus_di[13] = np.mean(plus_dm[:14])
                minus_di[13] = np.mean(minus_dm[:14])

                for i in range(14, len(tr)):
                    atr[i] = (atr[i-1] * 13 + tr[i]) / 14
                    plus_di[i] = (plus_di[i-1] * 13 + plus_dm[i]) / 14
                    minus_di[i] = (minus_di[i-1] * 13 + minus_dm[i]) / 14

                plus_di = 100 * np.divide(plus_di, atr, out=np.zeros_like(plus_di), where=atr!=0)
                minus_di = 100 * np.divide(minus_di, atr, out=np.zeros_like(minus_di), where=atr!=0)

                dx = 100 * np.divide(np.abs(plus_di - minus_di), (plus_di + minus_di),
                                    out=np.zeros_like(plus_di), where=(plus_di + minus_di)!=0)

                adx = np.zeros_like(dx)
                adx[27] = np.mean(dx[14:28])
                for i in range(28, len(dx)):
                    adx[i] = (adx[i-1] * 13 + dx[i]) / 14

                results['adx_14'] = adx
                results['plusdi_14'] = plus_di
                results['minusdi_14'] = minus_di

            # Bollinger Bands
            if TALIB_AVAILABLE:
                upper, middle, lower = talib.BBANDS(close_arr, timeperiod=20, nbdevup=2, nbdevdn=2)
                results['bollinger_upper'] = upper
                results['bollinger_middle'] = middle
                results['bollinger_lower'] = lower
            else:
                sma_20 = np.convolve(close_arr, np.ones(20)/20, mode='same')
                std_20 = np.zeros_like(close_arr)
                for i in range(19, len(close_arr)):
                    std_20[i] = np.std(close_arr[i-19:i+1])

                results['bollinger_upper'] = sma_20 + 2 * std_20
                results['bollinger_middle'] = sma_20
                results['bollinger_lower'] = sma_20 - 2 * std_20

            # VWAP
            typical_price = (high_arr + low_arr + close_arr) / 3
            cumulative_tpv = np.cumsum(typical_price * volume_arr)
            cumulative_volume = np.cumsum(volume_arr)
            results['vwap'] = np.divide(cumulative_tpv, cumulative_volume,
                                        out=np.zeros_like(cumulative_tpv),
                                        where=cumulative_volume!=0)

            # Parabolic SAR
            if NUMBA_AVAILABLE:
                results['sar_indicator'] = calculate_parabolic_sar(high_arr, low_arr)
            elif TALIB_AVAILABLE:
                results['sar_indicator'] = talib.SAR(high_arr, low_arr, acceleration=0.02, maximum=0.2)
            else:
                # Simplified SAR without Numba
                sar = np.zeros_like(close_arr)
                sar[0] = low_arr[0]
                af = 0.02
                ep = high_arr[0]
                trend = 1

                for i in range(1, len(close_arr)):
                    if trend == 1:
                        sar[i] = sar[i-1] + af * (ep - sar[i-1])
                        if low_arr[i] <= sar[i]:
                            trend = -1
                            sar[i] = ep
                            ep = low_arr[i]
                            af = 0.02
                        else:
                            if high_arr[i] > ep:
                                ep = high_arr[i]
                                af = min(af + 0.02, 0.2)
                            sar[i] = min(sar[i], low_arr[i-1])
                    else:
                        sar[i] = sar[i-1] + af * (ep - sar[i-1])
                        if high_arr[i] >= sar[i]:
                            trend = 1
                            sar[i] = ep
                            ep = high_arr[i]
                            af = 0.02
                        else:
                            if low_arr[i] < ep:
                                ep = low_arr[i]
                                af = min(af + 0.02, 0.2)
                            sar[i] = max(sar[i], high_arr[i-1])

                results['sar_indicator'] = sar

            # Schaff Trend Cycle
            if NUMBA_AVAILABLE:
                results['stc_line'] = calculate_stc(close_arr)
            else:
                # Simplified STC calculation
                # Calculate MACD for STC
                ema_23 = np.zeros_like(close_arr)
                ema_50 = np.zeros_like(close_arr)
                ema_23[0] = close_arr[0]
                ema_50[0] = close_arr[0]

                alpha_23 = 2.0 / 24
                alpha_50 = 2.0 / 51

                for i in range(1, len(close_arr)):
                    ema_23[i] = close_arr[i] * alpha_23 + ema_23[i-1] * (1 - alpha_23)
                    ema_50[i] = close_arr[i] * alpha_50 + ema_50[i-1] * (1 - alpha_50)

                macd_stc = ema_23 - ema_50

                # First Stochastic
                stc_k1 = np.zeros_like(macd_stc)
                for i in range(9, len(macd_stc)):
                    min_val = np.min(macd_stc[i-9:i+1])
                    max_val = np.max(macd_stc[i-9:i+1])
                    if max_val != min_val:
                        stc_k1[i] = 100 * (macd_stc[i] - min_val) / (max_val - min_val)
                    else:
                        stc_k1[i] = 50

                # Smooth to get first %D
                stc_d1 = np.convolve(stc_k1, np.ones(3)/3, mode='same')

                # Second Stochastic
                stc_k2 = np.zeros_like(stc_d1)
                for i in range(9, len(stc_d1)):
                    min_val = np.min(stc_d1[i-9:i+1])
                    max_val = np.max(stc_d1[i-9:i+1])
                    if max_val != min_val:
                        stc_k2[i] = 100 * (stc_d1[i] - min_val) / (max_val - min_val)
                    else:
                        stc_k2[i] = 50

                # Final smoothing for STC
                results['stc_line'] = np.convolve(stc_k2, np.ones(3)/3, mode='same')

            # Fibonacci Levels
            if NUMBA_AVAILABLE:
                fib_236, fib_382, fib_618, fib_786 = calculate_fibonacci_levels(high_arr, low_arr, lookback=20)
                results['fibonacci_23_6'] = fib_236
                results['fibonacci_38_2'] = fib_382
                results['fibonacci_61_8'] = fib_618
                results['fibonacci_78_6'] = fib_786
            else:
                # Calculate Fibonacci levels using rolling window
                lookback = 20
                fib_236 = np.zeros_like(close_arr)
                fib_382 = np.zeros_like(close_arr)
                fib_618 = np.zeros_like(close_arr)
                fib_786 = np.zeros_like(close_arr)

                for i in range(lookback-1, len(close_arr)):
                    period_high = np.max(high_arr[i-lookback+1:i+1])
                    period_low = np.min(low_arr[i-lookback+1:i+1])
                    price_range = period_high - period_low

                    fib_236[i] = period_high - (price_range * 0.236)
                    fib_382[i] = period_high - (price_range * 0.382)
                    fib_618[i] = period_high - (price_range * 0.618)
                    fib_786[i] = period_high - (price_range * 0.786)

                results['fibonacci_23_6'] = fib_236
                results['fibonacci_38_2'] = fib_382
                results['fibonacci_61_8'] = fib_618
                results['fibonacci_78_6'] = fib_786

            # Entry Signal
            if NUMBA_AVAILABLE and 'ema_5' in results and 'ema_20' in results:
                results['entry'] = calculate_entry_signal(
                    close_arr,
                    results['ema_5'],
                    results['ema_20'],
                    results['rsi_14'],
                    results['macd_hist']
                )
            else:
                # Simple entry signal without Numba
                entry = np.zeros_like(close_arr)
                for i in range(1, len(close_arr)):
                    bullish_score = 0
                    bearish_score = 0

                    # EMA crossover
                    if results['ema_5'][i] > results['ema_20'][i] and results['ema_5'][i-1] <= results['ema_20'][i-1]:
                        bullish_score += 2
                    elif results['ema_5'][i] < results['ema_20'][i] and results['ema_5'][i-1] >= results['ema_20'][i-1]:
                        bearish_score += 2

                    # RSI conditions
                    if results['rsi_14'][i] > 30 and results['rsi_14'][i] < 70:
                        if results['rsi_14'][i] > results['rsi_14'][i-1]:
                            bullish_score += 1
                        else:
                            bearish_score += 1
                    elif results['rsi_14'][i] <= 30:
                        bullish_score += 1
                    elif results['rsi_14'][i] >= 70:
                        bearish_score += 1

                    # MACD histogram
                    if results['macd_hist'][i] > 0 and results['macd_hist'][i] > results['macd_hist'][i-1]:
                        bullish_score += 1
                    elif results['macd_hist'][i] < 0 and results['macd_hist'][i] < results['macd_hist'][i-1]:
                        bearish_score += 1

                    # Determine entry signal
                    if bullish_score > bearish_score and bullish_score >= 2:
                        entry[i] = 1
                    elif bearish_score > bullish_score and bearish_score >= 2:
                        entry[i] = -1
                    else:
                        entry[i] = 0

                results['entry'] = entry

            # Convert results back to Polars DataFrame
            for key, value in results.items():
                df = df.with_columns(pl.Series(key, value))

            return df

    def process_file(self, input_file: Path) -> bool:
        """Process a single file with all technical indicators"""
        try:
            logger.info(f"Processing file: {input_file.name}")

            # Read file
            t0 = time.time()
            df = pl.read_parquet(input_file)
            read_s = time.time() - t0
            logger.info(f"Loaded {len(df):,} rows from {input_file.name} in {read_s:.2f}s")

            # Check required columns
            required_cols = ["timestamp", "open", "high", "low", "close", "volume"]
            missing_cols = [col for col in required_cols if col not in df.columns]
            if missing_cols:
                logger.error(f"Missing required columns: {missing_cols}")
                return False

            # Sort by timestamp
            t_sort0 = time.time()
            df = df.sort("timestamp")
            sort_s = time.time() - t_sort0

            # Calculate all technical indicators
            t_calc0 = time.time()
            logger.info("Calculating technical indicators...")
            df = self._calculate_technical_indicators(df)
            calc_s = time.time() - t_calc0
            logger.info(f"Indicator calc time: {calc_s:.2f}s | sort: {sort_s:.2f}s")

            # Rename OHLCV columns to match output format
            df = df.rename({
                "open": "open",
                "high": "high",
                "low": "low",
                "close": "close",
                "volume": "volume"
            })

            # Select only the required columns in the specified order
            output_columns = [
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'adx_14', 'bollinger_lower', 'bollinger_middle', 'bollinger_upper',
                'ema_10', 'ema_20', 'ema_5', 'ema_50', 'ema_9', 'entry',
                'fibonacci_23_6', 'fibonacci_38_2', 'fibonacci_61_8', 'fibonacci_78_6',
                'macd_hist', 'macd_line', 'minusdi_14', 'plusdi_14', 'rsi_14',
                'sar_indicator', 'stc_line', 'signal_line', 'stochastic_d',
                'stochastic_k', 'vwap'
            ]

            # Ensure all columns exist
            for col in output_columns:
                if col not in df.columns and col != 'timestamp':
                    df = df.with_columns(pl.lit(None).alias(col))

            # Select final columns
            df = df.select(output_columns)

            # Save output with features_ prefix
            output_file = self.output_dir / f"features_{input_file.name}"
            t_write0 = time.time()
            df.write_parquet(output_file, compression=self.compression, compression_level=self.compression_level)
            write_s = time.time() - t_write0

            logger.info(f"✓ Saved {len(df):,} rows to {output_file} in {write_s:.2f}s (compression={self.compression}, level={self.compression_level})")
            return True

        except Exception as e:
            logger.error(f"Error processing file {input_file}: {e}")
            import traceback
            traceback.print_exc()
            return False

    def process_all_files(self, max_workers: int = None) -> bool:
        """Process all parquet files in parallel"""
        try:
            # Find all parquet files that don't start with "features_"
            parquet_files = [f for f in self.input_dir.glob("*.parquet")
                           if not f.name.startswith("features_")]

            if not parquet_files:
                logger.error(f"No parquet files found in {self.input_dir}")
                return False

            logger.info(f"Found {len(parquet_files)} files to process")

            # Determine optimal number of workers
            if max_workers is None:
                # Use CPU count minus 2 for better system responsiveness
                import multiprocessing
                max_workers = max(1, multiprocessing.cpu_count() - 2) # Adjusted for better default utilization

            logger.info(f"Using {max_workers} parallel workers")

            # Process files in parallel
            success_count = 0
            failed_files = []

            with ProcessPoolExecutor(max_workers=max_workers) as executor: # Changed to ProcessPoolExecutor
                futures = {executor.submit(self.process_file, f): f
                          for f in parquet_files}

                for future in as_completed(futures):
                    try:
                        if future.result():
                            success_count += 1
                        else:
                            failed_files.append(futures[future].name)
                    except Exception as e:
                        logger.error(f"Worker failed for {futures[future].name}: {e}")
                        failed_files.append(futures[future].name)

            # Report results
            logger.info(f"Successfully processed {success_count}/{len(parquet_files)} files")
            if failed_files:
                logger.error(f"Failed files: {failed_files}")

            return success_count > 0

        except Exception as e:
            logger.error(f"Error in process_all_files: {e}")
            return False


def main():
    """Main function"""
    import argparse

    parser = argparse.ArgumentParser(
        description='Optimized Feature Engineering with Complete Technical Indicators',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python optimized_feature_engineering.py --input-dir data/historical --output-dir data/features
  python optimized_feature_engineering.py --input-dir data/historical --output-dir data/features --workers 8

This script generates the following technical indicators:
  - Moving Averages: EMA_5, EMA_9, EMA_10, EMA_20, EMA_50
  - Momentum: RSI_14, MACD_Line, Signal_Line, MACD_Hist, Stochastic_K/D, ADX_14, PlusDI_14, MinusDI_14
  - Volatility: Bollinger_Upper/Middle/Lower
  - Volume: VWAP
  - Advanced: SAR_Indicator, STC_Line, Fibonacci levels (23.6%, 38.2%, 61.8%, 78.6%)
  - Signals: Entry (bullish=1, bearish=-1, neutral=0)
        """
    )

    parser.add_argument('--input-dir', type=str, default='data/historical',
                       help='Input directory containing parquet files')
    parser.add_argument('--output-dir', type=str, default='data/features',
                       help='Output directory for feature files')
    parser.add_argument('--chunk-size', type=int, default=100000,
                       help='Chunk size for processing')
    parser.add_argument('--workers', type=int, default=None,
                       help='Number of parallel workers (default: CPU count - 12)')

    args = parser.parse_args()

    # Print configuration
    logger.info("=" * 60)
    logger.info("OPTIMIZED FEATURE ENGINEERING")
    logger.info("=" * 60)
    logger.info(f"Configuration:")
    logger.info(f"  Input directory: {args.input_dir}")
    logger.info(f"  Output directory: {args.output_dir}")
    logger.info(f"  Chunk size: {args.chunk_size:,}")
    logger.info(f"  Workers: {args.workers or 'auto'}")
    logger.info(f"  Numba acceleration: {NUMBA_AVAILABLE}")
    logger.info(f"  TA-Lib available: {TALIB_AVAILABLE}")
    logger.info("=" * 60)

    # Create processor and run
    processor = OptimizedFeatureEngineering(
        args.input_dir,
        args.output_dir,
        args.chunk_size
    )

    success = processor.process_all_files(max_workers=args.workers)

    if success:
        logger.info("=" * 60)
        logger.info("✓ Feature engineering completed successfully!")
        logger.info("=" * 60)
    else:
        logger.error("✗ Feature engineering failed!")
        sys.exit(1)


if __name__ == "__main__":
    import multiprocessing

    # Set the start method before any other multiprocessing-related code
    # 'forkserver' is safer than 'fork' when libraries using OpenMP are involved.
    try:
        multiprocessing.set_start_method("forkserver")
    except RuntimeError:
        # set_start_method can only be called once
        pass

    main()
