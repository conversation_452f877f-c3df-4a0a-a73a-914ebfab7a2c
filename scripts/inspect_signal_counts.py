#!/usr/bin/env python3
import asyncio
from pathlib import Path
import sys
import logging
import polars as pl

# Ensure project root is on sys.path when running from scripts/
PROJECT_ROOT = Path(__file__).resolve().parents[1]
if str(PROJECT_ROOT) not in sys.path:
    sys.path.insert(0, str(PROJECT_ROOT))

from agents.backtesting.kimi.file_io import get_available_feature_files
from agents.signal_generation.signal_agent import SignalAgent

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("inspect_signal_counts")

# Strategies to inspect
STRATEGY_NAMES = [
    "Momentum_Intraday_Base",
    "ADX_Intraday_Base",
    "Volume_Scalping_Base",
    "EMA_Crossover_Intraday_Base",
]

async def main(max_files: int = 3):
    files = get_available_feature_files()
    if not files:
        print("No feature files found under data/features")
        return

    # Pick a few files
    sample = files[:max_files]

    agent = SignalAgent()

    # Build strategy lookup
    strategies = {s.get("name"): s for s in agent.get_strategies(ranking_threshold=0)}

    for file_path, symbol, timeframe in sample:
        print(f"\n=== {symbol} ({timeframe}) ===")
        try:
            df = pl.read_parquet(file_path)
            # Convert timestamp -> datetime if present (as in backtesting path)
            if "timestamp" in df.columns and "datetime" not in df.columns:
                if df["timestamp"].dtype in (pl.Int64, pl.Int32):
                    df = df.with_columns([pl.from_epoch("timestamp", unit="ms").alias("datetime")]).drop("timestamp")
                else:
                    df = df.rename({"timestamp": "datetime"})
            # Ensure sorted
            if "datetime" in df.columns:
                df = df.sort("datetime")

            for name in STRATEGY_NAMES:
                strat = strategies.get(name)
                if not strat:
                    print(f"- Strategy {name} not found in config; skipping")
                    continue
                signals = agent.generate_signals(df, strat, ["entry_long", "entry_short", "exit_long", "exit_short"])
                # Coalesce potential nulls to False for counting
                counts = {k: int(signals[k].fill_null(False).sum()) for k in signals}
                print(f"- {name}: " + ", ".join([f"{k}={v}" for k, v in counts.items()]))
        except Exception as e:
            print(f"Error inspecting {file_path}: {e}")

if __name__ == "__main__":
    asyncio.run(main())

