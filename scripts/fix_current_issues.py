#!/usr/bin/env python3
"""
Fix Current System Issues
========================

Script to fix the critical issues in the current trading system:
1. Reset paper trading account to clean state
2. Fix position sizing and over-leveraging
3. Update risk management parameters
4. Fix API connectivity issues
5. Restart agents with proper health monitoring
6. Initialize recovery system if needed

This script should be run before attempting production deployment.
"""

import asyncio
import json
import logging
import os
import sys
from datetime import datetime
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SystemFixer:
    """Fix critical system issues"""
    
    def __init__(self):
        self.fixes_applied = []
        self.issues_found = []
        
    async def run_all_fixes(self):
        """Run all system fixes"""
        try:
            logger.info("🔧 Starting system fixes...")
            
            # 1. Analyze current issues
            await self._analyze_current_issues()
            
            # 2. Reset paper trading account
            await self._reset_paper_trading_account()
            
            # 3. Fix position sizing configuration
            await self._fix_position_sizing()
            
            # 4. Update risk management parameters
            await self._update_risk_management()
            
            # 5. Fix API configuration
            await self._fix_api_configuration()
            
            # 6. Update agent configurations
            await self._update_agent_configurations()
            
            # 7. Initialize recovery system
            await self._initialize_recovery_system()
            
            # 8. Generate fix report
            await self._generate_fix_report()
            
            logger.info("✅ All system fixes completed!")
            
        except Exception as e:
            logger.error(f"❌ System fix failed: {e}")
            raise
    
    async def _analyze_current_issues(self):
        """Analyze current system issues"""
        try:
            logger.info("🔍 Analyzing current system issues...")
            
            # Check paper trading account
            account_file = Path("data/paper_trading_account.json")
            if account_file.exists():
                with open(account_file, 'r') as f:
                    account_data = json.load(f)
                
                balance = account_data.get('balance', 0)
                used_margin = account_data.get('used_margin', 0)
                available_margin = account_data.get('available_margin', 0)
                
                # Calculate drawdown
                initial_balance = 100000  # Assuming initial balance
                current_value = balance + used_margin
                drawdown = (initial_balance - current_value) / initial_balance
                
                if drawdown > 0.1:  # 10% drawdown
                    self.issues_found.append(f"High drawdown: {drawdown:.1%}")
                
                if used_margin > balance:
                    self.issues_found.append(f"Over-leveraging: Used margin (₹{used_margin:,.2f}) > Balance (₹{balance:,.2f})")
                
                logger.info(f"Current balance: ₹{balance:,.2f}")
                logger.info(f"Used margin: ₹{used_margin:,.2f}")
                logger.info(f"Drawdown: {drawdown:.1%}")
            
            # Check for API issues in logs
            log_file = Path(f"logs/{datetime.now().strftime('%Y-%m-%d')}/app.log")
            if log_file.exists():
                with open(log_file, 'r') as f:
                    log_content = f.read()
                
                if "Something Went Wrong" in log_content:
                    self.issues_found.append("Angel One API errors detected")
                
                if "agent crashed" in log_content.lower():
                    self.issues_found.append("Agent crashes detected")
            
            logger.info(f"Found {len(self.issues_found)} issues:")
            for issue in self.issues_found:
                logger.warning(f"  - {issue}")
                
        except Exception as e:
            logger.error(f"Failed to analyze issues: {e}")
    
    async def _reset_paper_trading_account(self):
        """Reset paper trading account to clean state"""
        try:
            logger.info("🔄 Resetting paper trading account...")
            
            # Create clean account data
            clean_account = {
                "account_id": "PAPER_TRADER",
                "balance": 100000.0,
                "available_margin": 100000.0,
                "used_margin": 0.0,
                "unrealized_pnl": 0.0,
                "realized_pnl": 0.0,
                "total_pnl": 0.0,
                "positions": [],
                "orders": [],
                "trades": [],
                "last_updated": datetime.now().isoformat(),
                "created_at": datetime.now().isoformat(),
                "reset_count": 1,
                "reset_reason": "System fix - high drawdown recovery"
            }
            
            # Backup existing account if it exists
            account_file = Path("data/paper_trading_account.json")
            if account_file.exists():
                backup_file = Path(f"data/paper_trading_account_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
                account_file.rename(backup_file)
                logger.info(f"Backed up existing account to {backup_file}")
            
            # Create data directory if it doesn't exist
            account_file.parent.mkdir(parents=True, exist_ok=True)
            
            # Write clean account
            with open(account_file, 'w') as f:
                json.dump(clean_account, f, indent=2)
            
            self.fixes_applied.append("Reset paper trading account to clean state")
            logger.info("✅ Paper trading account reset successfully")
            
        except Exception as e:
            logger.error(f"Failed to reset paper trading account: {e}")
    
    async def _fix_position_sizing(self):
        """Fix position sizing configuration"""
        try:
            logger.info("📏 Fixing position sizing configuration...")
            
            # Update risk management config
            risk_config_file = Path("agents/config/risk_management_config.yaml")

            if risk_config_file.exists():
                # Backup existing config
                backup_file = Path(f"agents/config/risk_management_config_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.yaml")
                risk_config_file.rename(backup_file)
                logger.info(f"Backed up existing config to {backup_file}")
            
            # Create conservative risk configuration
            conservative_config = """# Conservative Risk Management Configuration
# Updated by system fix script

risk_management:
  enabled: true
  
  # Capital allocation
  max_capital_per_trade: 0.02  # 2% max per trade (reduced from previous)
  max_portfolio_risk: 0.05     # 5% max portfolio risk
  max_daily_risk: 0.03         # 3% max daily risk
  
  # Position sizing
  position_sizing_method: "fixed_percentage"
  max_position_size: 0.02      # 2% max position size (CRITICAL FIX)
  min_position_size: 0.001     # 0.1% min position size
  
  # Leverage limits
  max_leverage: 1.5            # 1.5x max leverage (reduced)
  margin_safety_factor: 0.8    # Use only 80% of available margin
  
  # Risk limits
  max_drawdown: 0.08           # 8% max drawdown (reduced)
  daily_loss_limit: 0.03       # 3% daily loss limit
  consecutive_loss_limit: 5    # Stop after 5 consecutive losses
  
  # Position limits
  max_positions: 5             # Max 5 positions (reduced)
  max_positions_per_symbol: 1  # Max 1 position per symbol
  
  # Stop loss requirements
  mandatory_stop_loss: true
  max_stop_loss_distance: 0.03 # 3% max stop loss distance
  min_risk_reward_ratio: 1.5   # Minimum 1.5:1 risk-reward

# Circuit breakers
circuit_breakers:
  enabled: true
  
  daily_loss_breaker:
    threshold: 0.025  # 2.5% daily loss
    action: "stop_new_trades"
    
  drawdown_breaker:
    threshold: 0.06   # 6% drawdown
    action: "stop_all_trading"
    
  leverage_breaker:
    threshold: 1.4    # 1.4x leverage
    action: "reduce_positions"
"""
            
            # Write conservative config
            with open(risk_config_file, 'w') as f:
                f.write(conservative_config)
            
            self.fixes_applied.append("Updated position sizing to conservative limits")
            logger.info("✅ Position sizing configuration fixed")
            
        except Exception as e:
            logger.error(f"Failed to fix position sizing: {e}")
    
    async def _update_risk_management(self):
        """Update risk management parameters"""
        try:
            logger.info("🛡️ Updating risk management parameters...")
            
            # Copy production risk config if it exists
            prod_config = Path("agents/config/production_risk_config.yaml")
            if prod_config.exists():
                # Use production config as base
                import shutil
                shutil.copy(prod_config, "agents/config/risk_management_config.yaml")
                logger.info("Applied production risk configuration")
            
            self.fixes_applied.append("Updated risk management parameters")
            logger.info("✅ Risk management parameters updated")
            
        except Exception as e:
            logger.error(f"Failed to update risk management: {e}")
    
    async def _fix_api_configuration(self):
        """Fix API configuration issues"""
        try:
            logger.info("🔌 Fixing API configuration...")
            
            # Check Angel One API config
            api_config_file = Path("agents/config/angel_one_config.yaml")

            if api_config_file.exists():
                with open(api_config_file, 'r') as f:
                    content = f.read()
                
                # Add retry and timeout configurations
                if "retry_attempts" not in content:
                    additional_config = """
# API reliability improvements (added by system fix)
api_reliability:
  retry_attempts: 3
  retry_delay: 2.0
  timeout_seconds: 30
  circuit_breaker_enabled: true
  circuit_breaker_threshold: 0.5
  
# Connection pooling
connection_pool:
  max_connections: 10
  keep_alive: true
  
# Error handling
error_handling:
  log_all_errors: true
  alert_on_failures: true
  fallback_enabled: true
"""
                    
                    with open(api_config_file, 'a') as f:
                        f.write(additional_config)
                    
                    logger.info("Added API reliability configurations")
            
            self.fixes_applied.append("Enhanced API configuration for reliability")
            logger.info("✅ API configuration fixed")
            
        except Exception as e:
            logger.error(f"Failed to fix API configuration: {e}")
    
    async def _update_agent_configurations(self):
        """Update agent configurations for better reliability"""
        try:
            logger.info("🤖 Updating agent configurations...")
            
            # Create agent health monitoring config
            health_config = {
                "health_monitoring": {
                    "enabled": True,
                    "check_interval": 30,
                    "restart_on_failure": True,
                    "max_restart_attempts": 3,
                    "alert_on_failure": True
                },
                "agent_timeouts": {
                    "heartbeat_timeout": 60,
                    "response_timeout": 30,
                    "startup_timeout": 120
                },
                "error_handling": {
                    "log_all_errors": True,
                    "error_threshold": 10,
                    "circuit_breaker_enabled": True
                }
            }
            
            # Write health monitoring config
            health_config_file = Path("agents/config/agent_health_config.yaml")
            with open(health_config_file, 'w') as f:
                import yaml
                yaml.dump(health_config, f, default_flow_style=False)
            
            self.fixes_applied.append("Added agent health monitoring configuration")
            logger.info("✅ Agent configurations updated")
            
        except Exception as e:
            logger.error(f"Failed to update agent configurations: {e}")
    
    async def _initialize_recovery_system(self):
        """Initialize recovery system if needed"""
        try:
            logger.info("🏥 Initializing recovery system...")
            
            # Check if recovery is needed based on current state
            account_file = Path("data/paper_trading_account.json")
            if account_file.exists():
                with open(account_file, 'r') as f:
                    account_data = json.load(f)
                
                # Since we reset the account, no recovery needed
                logger.info("Account reset - no recovery needed")
            
            # Create recovery configuration
            recovery_config = {
                "recovery_system": {
                    "enabled": True,
                    "auto_recovery": True,
                    "conservative_mode": True,
                    "gradual_scaling": True,
                    "max_recovery_time_days": 30,
                    "recovery_thresholds": {
                        "start_recovery_at": 0.05,  # 5% drawdown
                        "emergency_stop_at": 0.10,  # 10% drawdown
                        "full_recovery_target": 0.02  # 2% profit
                    }
                }
            }
            
            # Write recovery config
            recovery_config_file = Path("agents/config/recovery_config.yaml")
            with open(recovery_config_file, 'w') as f:
                import yaml
                yaml.dump(recovery_config, f, default_flow_style=False)
            
            self.fixes_applied.append("Initialized recovery system configuration")
            logger.info("✅ Recovery system initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize recovery system: {e}")
    
    async def _generate_fix_report(self):
        """Generate comprehensive fix report"""
        try:
            logger.info("📊 Generating fix report...")
            
            report = {
                "fix_timestamp": datetime.now().isoformat(),
                "issues_found": self.issues_found,
                "fixes_applied": self.fixes_applied,
                "system_status": "FIXED" if len(self.fixes_applied) > 0 else "NO_FIXES_NEEDED",
                "next_steps": [
                    "Run system tests: python tests/production_system_tests.py",
                    "Start paper trading with new configuration",
                    "Monitor system for 24 hours before considering live trading",
                    "Review performance metrics daily",
                    "Ensure all agents are healthy and communicating"
                ],
                "recommendations": [
                    "Keep position sizes small (2% max) until system proves stable",
                    "Monitor drawdown closely - stop trading if it exceeds 8%",
                    "Test all strategies in paper mode for at least 1 week",
                    "Verify API connectivity is stable before live trading",
                    "Set up proper monitoring and alerting"
                ]
            }
            
            # Write report
            report_file = Path(f"reports/system_fix_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
            report_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(report_file, 'w') as f:
                json.dump(report, f, indent=2)
            
            logger.info(f"📄 Fix report saved to: {report_file}")
            
            # Print summary
            print("\n" + "="*60)
            print("🔧 SYSTEM FIX SUMMARY")
            print("="*60)
            print(f"Issues Found: {len(self.issues_found)}")
            for issue in self.issues_found:
                print(f"  ❌ {issue}")
            
            print(f"\nFixes Applied: {len(self.fixes_applied)}")
            for fix in self.fixes_applied:
                print(f"  ✅ {fix}")
            
            print(f"\nSystem Status: {report['system_status']}")
            print("\nNext Steps:")
            for step in report['next_steps']:
                print(f"  📋 {step}")
            
            print("="*60)
            
        except Exception as e:
            logger.error(f"Failed to generate fix report: {e}")

async def main():
    """Main function to run system fixes"""
    try:
        print("🚀 Starting Trading System Fix Process...")
        print("This will fix critical issues in the current system.")
        print()
        
        # Confirm with user
        response = input("Do you want to proceed with system fixes? (y/N): ")
        if response.lower() != 'y':
            print("❌ Fix process cancelled by user")
            return
        
        # Run fixes
        fixer = SystemFixer()
        await fixer.run_all_fixes()
        
        print("\n🎉 System fixes completed successfully!")
        print("You can now run tests and restart the trading system.")
        
    except Exception as e:
        print(f"❌ Fix process failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
