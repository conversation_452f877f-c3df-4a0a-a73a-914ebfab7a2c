import logging
import random
import math
from typing import List, Dict, Any, Optional

import polars as pl
import numpy as np
import vectorbt as vbt

from . import signals
from . import performance
from .config import (
    INITIAL_CAPITAL,
    TRANSACTION_COST_PCT,
    SLIPPAGE_PCT,
    PROFIT_THRESHOLD,
    RISK_PER_TRADE_PCT,
    MIN_SIGNAL_DISTANCE
)

logger = logging.getLogger(__name__)

def process_signals_vectorized(df: pl.DataFrame, long_signals: pl.Series, short_signals: pl.Series, strategy: Dict[str, Any], rr: List[float], timeframe: str) -> Optional[List[Dict[str, Any]]]:
    """Process signals using vectorbt for backtesting."""
    try:
        strategy_name = strategy.get('name', 'Unknown')

        price_data = df["close"].to_numpy(allow_copy=True).astype(np.float64).reshape(-1, 1)
        
        eod_exit_series = df.with_columns(
            (pl.col("datetime").rank(method="dense", descending=True).over(pl.col("datetime").dt.date()) == 1).alias("eod_exit")
        ).select("eod_exit").to_series()

        long_entries = long_signals.to_numpy(allow_copy=True).astype(np.bool_).reshape(-1, 1)
        short_entries = short_signals.to_numpy(allow_copy=True).astype(np.bool_).reshape(-1, 1)
        eod_exits = eod_exit_series.to_numpy(allow_copy=True).astype(np.bool_).reshape(-1, 1)

        trades_list = []
        
        if long_entries.sum() > 0:
            pf_long = vbt.Portfolio.from_signals(
                price_data,
                long_entries,
                eod_exits,
                init_cash=INITIAL_CAPITAL,
                fees=TRANSACTION_COST_PCT / 100.0,
                slippage=SLIPPAGE_PCT / 100.0,
            )
            if pf_long.trades.count().iloc[0] > 0:
                long_trades = extract_trades_from_portfolio(pf_long, df, 1)
                trades_list.extend(long_trades)

        if short_entries.sum() > 0:
            pf_short = vbt.Portfolio.from_signals(
                price_data,
                short_entries,
                eod_exits,
                init_cash=INITIAL_CAPITAL,
                fees=TRANSACTION_COST_PCT / 100.0,
                slippage=SLIPPAGE_PCT / 100.0,
                short_entries=True,
            )
            if pf_short.trades.count().iloc[0] > 0:
                short_trades = extract_trades_from_portfolio(pf_short, df, -1)
                trades_list.extend(short_trades)

        if not trades_list:
            logger.warning(f"No trades generated by vectorbt for {strategy_name}")
            return None

        return trades_list

    except Exception as e:
        logger.error(f"vectorbt processing failed for {strategy.get('name', 'Unknown')}: {e}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        return None

def extract_trades_from_portfolio(pf, df, signal_type):
    """Extract trades from a vectorbt portfolio"""
    trades_list = []
    trades_records = pf.trades.records_readable
    if len(trades_records) == 0:
        return trades_list

    for _, trade_row in trades_records.iterrows():
        try:
            quantity = abs(float(trade_row['Size']))
            entry_price = float(trade_row['Avg Entry Price'])
            exit_price = float(trade_row['Avg Exit Price'])
            trade_pnl = float(trade_row['PnL'])
            entry_idx = int(trade_row['Entry Timestamp'])
            exit_idx = int(trade_row['Exit Timestamp'])
            holding_period = exit_idx - entry_idx
            position_value = quantity * entry_price
            trade_pnl_pct = (trade_pnl / position_value) * 100 if position_value > 0 else 0

            trades_list.append({
                'entry_datetime': df["datetime"][entry_idx],
                'exit_datetime': df["datetime"][exit_idx],
                'entry_price': entry_price,
                'exit_price': exit_price,
                'signal_type': signal_type,
                'pnl': round(trade_pnl, 2),
                'pnl_pct': round(trade_pnl_pct, 2),
                'holding_period': int(holding_period),
                'quantity': quantity,
                'position_value': position_value,
                'stop_loss_price': None,
                'take_profit_price': None,
            })
        except Exception as e:
            logger.error(f"Error processing trade record: {e}")
            continue
    
    return trades_list

def process_signals_with_exits_vectorized(df: pl.DataFrame, strategy: Dict[str, Any], rr: List[float], timeframe: str) -> Optional[List[Dict[str, Any]]]:
    """Process signals with proper entry/exit logic and risk management"""
    strategy_name = strategy.get('name', 'Unknown')

    try:
        risk_mgmt = strategy.get('risk_management', {})
        stop_loss_pct = risk_mgmt.get('stop_loss_value', 0.01)
        take_profit_pct = risk_mgmt.get('take_profit_value', 0.02)
        position_sizing = strategy.get('position_sizing', {})
        max_capital_multiplier = position_sizing.get('max_capital_multiplier', 3.5)

        entry_long = df['entry_long'].to_numpy()
        entry_short = df['entry_short'].to_numpy()
        exit_long = df['exit_long'].to_numpy()
        exit_short = df['exit_short'].to_numpy()
        prices = df['close'].to_numpy()
        highs = df['high'].to_numpy()
        lows = df['low'].to_numpy()
        datetimes = df['datetime'].to_list()

        trades = []
        current_position = None
        dates = [d.date() for d in datetimes]

        for i in range(len(prices)):
            current_price = prices[i]
            current_high = highs[i]
            current_low = lows[i]
            current_datetime = datetimes[i]

            if i > 0 and dates[i] != dates[i-1] and current_position is not None:
                exit_price = prices[i-1]
                exit_datetime = datetimes[i-1]
                pnl = (exit_price - current_position['entry_price']) * current_position['quantity']
                if current_position['type'] == 'short':
                    pnl = -pnl
                pnl_pct = (exit_price - current_position['entry_price']) / current_position['entry_price']
                if current_position['type'] == 'short':
                    pnl_pct = -pnl_pct
                holding_period = (i - 1) - current_position['entry_idx']
                trades.append({
                    'entry_datetime': current_position['entry_datetime'],
                    'exit_datetime': exit_datetime,
                    'side': current_position['type'],
                    'entry_price': current_position['entry_price'],
                    'exit_price': exit_price,
                    'quantity': current_position['quantity'],
                    'pnl': pnl,
                    'pnl_pct': pnl_pct,
                    'position_value': current_position['entry_price'] * current_position['quantity'],
                    'holding_period': holding_period,
                    'exit_reason': 'EOD',
                    'stop_loss_price': current_position['stop_loss'],
                    'take_profit_price': current_position['take_profit'],
                })
                current_position = None

            if current_position is not None:
                exit_triggered = False
                exit_price = current_price
                exit_reason = "Signal"
                if current_position['type'] == 'long':
                    if exit_long[i]:
                        exit_triggered = True
                    elif current_low <= current_position['stop_loss']:
                        exit_triggered = True
                        exit_price = current_position['stop_loss']
                        exit_reason = "Stop Loss"
                    elif current_high >= current_position['take_profit']:
                        exit_triggered = True
                        exit_price = current_position['take_profit']
                        exit_reason = "Take Profit"
                elif current_position['type'] == 'short':
                    if exit_short[i]:
                        exit_triggered = True
                    elif current_high >= current_position['stop_loss']:
                        exit_triggered = True
                        exit_price = current_position['stop_loss']
                        exit_reason = "Stop Loss"
                    elif current_low <= current_position['take_profit']:
                        exit_triggered = True
                        exit_price = current_position['take_profit']
                        exit_reason = "Take Profit"
                if exit_triggered:
                    pnl = (exit_price - current_position['entry_price']) * current_position['quantity']
                    if current_position['type'] == 'short':
                        pnl = -pnl
                    pnl_pct = (exit_price - current_position['entry_price']) / current_position['entry_price']
                    if current_position['type'] == 'short':
                        pnl_pct = -pnl_pct
                    holding_period = i - current_position['entry_idx']
                    trades.append({
                        'entry_datetime': current_position['entry_datetime'],
                        'exit_datetime': current_datetime,
                        'side': current_position['type'],
                        'entry_price': current_position['entry_price'],
                        'exit_price': exit_price,
                        'quantity': current_position['quantity'],
                        'pnl': pnl,
                        'pnl_pct': pnl_pct,
                        'position_value': current_position['entry_price'] * current_position['quantity'],
                        'holding_period': holding_period,
                        'exit_reason': exit_reason,
                        'stop_loss_price': current_position['stop_loss'],
                        'take_profit_price': current_position['take_profit'],
                    })
                    current_position = None

            if current_position is None:
                if entry_long[i]:
                    risk_amount = current_price * stop_loss_pct
                    quantity = max_capital_multiplier / current_price if risk_amount > 0 else 1.0
                    current_position = {
                        'type': 'long',
                        'entry_price': current_price,
                        'entry_datetime': current_datetime,
                        'entry_idx': i,
                        'quantity': quantity,
                        'stop_loss': current_price * (1 - stop_loss_pct),
                        'take_profit': current_price * (1 + take_profit_pct)
                    }
                elif entry_short[i]:
                    risk_amount = current_price * stop_loss_pct
                    quantity = max_capital_multiplier / current_price if risk_amount > 0 else 1.0
                    current_position = {
                        'type': 'short',
                        'entry_price': current_price,
                        'entry_datetime': current_datetime,
                        'entry_idx': i,
                        'quantity': quantity,
                        'stop_loss': current_price * (1 + stop_loss_pct),
                        'take_profit': current_price * (1 - take_profit_pct)
                    }

        if current_position is not None:
            final_price = prices[-1]
            pnl = (final_price - current_position['entry_price']) * current_position['quantity']
            if current_position['type'] == 'short':
                pnl = -pnl
            pnl_pct = (final_price - current_position['entry_price']) / current_position['entry_price']
            if current_position['type'] == 'short':
                pnl_pct = -pnl_pct
            holding_period = len(prices) - 1 - current_position['entry_idx']
            trades.append({
                'entry_datetime': current_position['entry_datetime'],
                'exit_datetime': datetimes[-1],
                'side': current_position['type'],
                'entry_price': current_position['entry_price'],
                'exit_price': final_price,
                'quantity': current_position['quantity'],
                'pnl': pnl,
                'pnl_pct': pnl_pct,
                'position_value': current_position['entry_price'] * current_position['quantity'],
                'holding_period': holding_period,
                'exit_reason': "End of Data",
                'stop_loss_price': current_position['stop_loss'],
                'take_profit_price': current_position['take_profit'],
            })

        return trades

    except Exception as e:
        logger.error(f"Error in process_signals_with_exits_vectorized for {strategy_name}: {e}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        return None

def simulate_trades_vectorized(df: pl.DataFrame, strategy: Dict[str, Any], rr: List[float], timeframe: str) -> Optional[List[Dict[str, Any]]]:
    """Optimized trade simulation with enhanced debugging"""
    try:
        strategy_name = strategy.get('name', 'Unknown')
        df = df.sort("datetime")
        if len(df) < 20:
            logger.warning(f"Insufficient data for {strategy_name}: {len(df)} rows")
            return None

        numerical_cols = ['open', 'high', 'low', 'close', 'volume', 'ema_5', 'ema_9', 'ema_10', 'ema_13', 'ema_20', 'ema_21', 'ema_50', 'rsi_5', 'rsi_14', 'macd', 'macd_hist', 'macd_line', 'macd_signal', 'signal_line', 'bb_lower', 'bb_upper', 'bollinger_lower', 'bollinger_middle', 'bollinger_upper', 'adx', 'adx_14', 'minusdi_14', 'plusdi_14', 'stochastic_d', 'stochastic_k', 'cpr_bottom', 'cpr_top', 'donchian_high', 'donchian_low', 'entry', 'fibonacci_23_6', 'fibonacci_38_2', 'fibonacci_61_8', 'fibonacci_78_6', 'sar_indicator', 'stc_line', 'supertrend', 'vwap', 'upward_candle', 'downward_candle', 'vcp_pattern']
        cols_to_process = [col for col in numerical_cols if col in df.columns]
        expressions = []
        binary_indicators = ['vcp_pattern', 'upward_candle', 'downward_candle']
        percentage_indicators = ['rsi_14', 'rsi_5', 'stoch_k', 'stoch_d', 'mfi']
        price_columns = ['open', 'high', 'low', 'close', 'volume', 'atr']
        for col in cols_to_process:
            if col in binary_indicators:
                expressions.append(pl.when(pl.col(col).is_infinite() | pl.col(col).is_nan()).then(pl.lit(None, dtype=pl.Float64)).otherwise(pl.col(col)).fill_null(strategy='forward').fill_null(strategy='backward').alias(col))
            elif col in percentage_indicators:
                expressions.append(pl.when(pl.col(col).is_infinite() | pl.col(col).is_nan() | (pl.col(col) < 0) | (pl.col(col) > 100)).then(pl.lit(None, dtype=pl.Float64)).otherwise(pl.col(col)).fill_null(strategy='forward').fill_null(strategy='backward').alias(col))
            elif col in price_columns:
                expressions.append(pl.when(pl.col(col).is_infinite() | (pl.col(col) <= 0)).then(pl.lit(None, dtype=pl.Float64)).otherwise(pl.col(col)).fill_null(strategy='forward').fill_null(strategy='backward').alias(col))
            else:
                expressions.append(pl.when(pl.col(col).is_infinite() | pl.col(col).is_nan()).then(pl.lit(None, dtype=pl.Float64)).otherwise(pl.col(col)).fill_null(strategy='forward').fill_null(strategy='backward').alias(col))
        df = df.with_columns(expressions)
        df = df.drop_nulls(subset=['close', 'high', 'low', 'open', 'volume', 'datetime'])

        if df.is_empty():
            logger.warning(f"DataFrame became empty after dropping critical nulls for {strategy_name}. Skipping.")
            return None

        if strategy_name == "Opening_Range_Breakout":
            orb_period = 15
            if timeframe == "3min": orb_period = 5
            elif timeframe == "5min": orb_period = 3
            elif timeframe == "15min": orb_period = 1
            df = signals.calculate_daily_opening_range(df, orb_period)
            if "daily_orb_high" not in df.columns or "daily_orb_low" not in df.columns:
                logger.error(f"Failed to calculate daily opening range for {strategy_name}. Skipping.")
                return None

        if performance.cuda_optimizer and performance.cuda_optimizer.should_use_cuda(len(df)) and not performance.MULTIPROCESSING_MODE:
            df_accelerated = performance.accelerated_signal_processing(df, strategy)
            if 'accelerated_signals' in df_accelerated.columns:
                accelerated_signals = df_accelerated['accelerated_signals'].to_numpy()
                generated_signals = {
                    'entry_long': pl.Series("entry_long", accelerated_signals == 1),
                    'entry_short': pl.Series("entry_short", accelerated_signals == -1),
                    'exit_long': pl.Series("exit_long", [False] * len(df)),
                    'exit_short': pl.Series("exit_short", [False] * len(df))
                }
            else:
                if signals.signal_agent is None:
                    signals.init_signal_agent()
                if signals.signal_agent is None:
                    logger.error(f"SignalAgent not available for {strategy_name}, skipping")
                    return None
                generated_signals = signals.signal_agent.generate_signals(df, strategy)
        else:
            if signals.signal_agent is None:
                signals.init_signal_agent()
            if signals.signal_agent is None:
                logger.error(f"SignalAgent not available for {strategy_name}, skipping")
                return None
            generated_signals = signals.signal_agent.generate_signals(df, strategy)

        generated_signals = signals.apply_intraday_rules(df, generated_signals, strategy)

        if generated_signals['entry_long'].sum() > 0:
            generated_signals['entry_long'] = signals.filter_signals_by_distance(generated_signals['entry_long'], min_distance=MIN_SIGNAL_DISTANCE)
        if generated_signals['entry_short'].sum() > 0:
            generated_signals['entry_short'] = signals.filter_signals_by_distance(generated_signals['entry_short'], min_distance=MIN_SIGNAL_DISTANCE)

        total_entry_signals = generated_signals['entry_long'].sum() + generated_signals['entry_short'].sum()
        if total_entry_signals == 0:
            logger.warning(f"No entry signals generated or found after filtering for {strategy_name}")
            return None

        if performance.cuda_optimizer and performance.cuda_optimizer.should_use_cuda(len(df), signal_count=total_entry_signals) and not performance.MULTIPROCESSING_MODE:
            combined_signals = np.zeros(len(df), dtype=np.int32)
            long_indices = generated_signals['entry_long'].arg_true().to_list()
            short_indices = generated_signals['entry_short'].arg_true().to_list()
            for idx in long_indices:
                combined_signals[idx] = 1
            for idx in short_indices:
                combined_signals[idx] = -1
            accelerated_trades = performance.accelerated_trade_simulation(df, combined_signals, stop_loss_pct=rr[0]/100, take_profit_pct=rr[1]/100)
            if accelerated_trades:
                return accelerated_trades

        df = df.with_columns([
            generated_signals['entry_long'].alias('entry_long'),
            generated_signals['entry_short'].alias('entry_short'),
            generated_signals['exit_long'].alias('exit_long'),
            generated_signals['exit_short'].alias('exit_short')
        ])

        trades = process_signals_with_exits_vectorized(df, strategy, rr, timeframe)
        return trades
        
    except Exception as e:
        logger.error(f"Vectorized trade simulation failed for {strategy.get('name', 'Unknown')}: {e}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        return None

def calculate_performance_metrics(trades: List[Dict[str, Any]], df, symbol: str, strategy_name: str, timeframe: str, rr_combo: List[float]) -> Optional[Dict[str, Any]]:
    """Calculate performance metrics with GPU acceleration when available"""
    if not trades:
        return None

    if performance.cuda_optimizer and performance.cuda_optimizer.should_use_cuda(0, trade_count=len(trades)):
        accelerated_metrics = performance.accelerated_performance_metrics(trades)
        if accelerated_metrics:
            accelerated_metrics.update({
                'symbol': symbol,
                'strategy_name': strategy_name,
                'timeframe': timeframe,
                'risk_reward_ratio': f"{rr_combo[0]}:{rr_combo[1]}",
                'accuracy': accelerated_metrics.get('win_rate', 0),
                'roi': (accelerated_metrics.get('total_pnl', 0) / 10000) * 100,
                'profit_factor': 1.0,
                'avg_trade_duration': 50,
                'volatility': 0.0,
                'calmar_ratio': 0.0,
                'sortino_ratio': 0.0,
                'recovery_factor': 0.0,
                'expectancy': accelerated_metrics.get('total_pnl', 0) / max(accelerated_metrics.get('total_trades', 1), 1),
                'avg_win': 0.0,
                'avg_loss': 0.0,
                'largest_win': 0.0,
                'largest_loss': 0.0,
            })
            pnls = [trade['pnl'] for trade in trades]
            winning_pnls = [pnl for pnl in pnls if pnl > 0]
            losing_pnls = [pnl for pnl in pnls if pnl < 0]
            if winning_pnls:
                accelerated_metrics['avg_win'] = sum(winning_pnls) / len(winning_pnls)
                accelerated_metrics['largest_win'] = max(winning_pnls)
            if losing_pnls:
                accelerated_metrics['avg_loss'] = sum(losing_pnls) / len(losing_pnls)
                accelerated_metrics['largest_loss'] = min(losing_pnls)
            return accelerated_metrics

    total_pnl = sum(t['pnl'] for t in trades)
    total_pnl_pct = sum(t['pnl_pct'] for t in trades)
    winning_trades = sum(1 for t in trades if t['pnl'] > 0)
    total_trades = len(trades)
    accuracy = winning_trades / total_trades if total_trades > 0 else 0
    expectancy = total_pnl / total_trades if total_trades > 0 else 0
    avg_win = sum(t['pnl'] for t in trades if t['pnl'] > 0) / winning_trades if winning_trades > 0 else 0
    losing_trades = total_trades - winning_trades
    avg_loss = sum(t['pnl'] for t in trades if t['pnl'] < 0) / losing_trades if losing_trades > 0 else 0
    profit_factor = avg_win / abs(avg_loss) if avg_loss != 0 else float('inf')
    
    cumulative_pnl = []
    running_pnl = 0
    for trade in trades:
        running_pnl += trade['pnl_pct']
        cumulative_pnl.append(running_pnl)
    
    max_drawdown = 0
    drawdown_duration = 0
    if cumulative_pnl:
        peak = cumulative_pnl[0]
        current_drawdown_duration = 0
        for pnl in cumulative_pnl:
            if pnl > peak:
                peak = pnl
                current_drawdown_duration = 0
            else:
                drawdown = peak - pnl
                if drawdown > max_drawdown:
                    max_drawdown = drawdown
                current_drawdown_duration += 1
                if current_drawdown_duration > drawdown_duration:
                    drawdown_duration = current_drawdown_duration
    
    pnl_series_pl = pl.Series([t['pnl_pct'] for t in trades])
    if len(pnl_series_pl) > 1:
        std_dev = pnl_series_pl.std()
        sharpe_ratio = (pnl_series_pl.mean() * math.sqrt(252)) / std_dev if std_dev > 0 else 0
    else:
        sharpe_ratio = 0

    volatility = 0.0
    if len(df) > 1 and "close" in df.columns:
        log_returns = df.select((pl.col("close").log() - pl.col("close").shift(1).log()).alias("log_return")).drop_nulls().select("log_return").to_series()
        if len(log_returns) > 1:
            volatility = log_returns.std() * math.sqrt(252)
            volatility = round(volatility, 4)

    market_regime = "sideways"
    if len(df) > 200 and "close" in df.columns:
        ema_50 = df.select(pl.col("close").ewm_mean(span=50).alias("ema_50")).to_series()
        ema_200 = df.select(pl.col("close").ewm_mean(span=200).alias("ema_200")).to_series()
        if ema_50.drop_nulls().len() > 0 and ema_200.drop_nulls().len() > 0:
            last_ema_50 = ema_50.drop_nulls().tail(1).item()
            last_ema_200 = ema_200.drop_nulls().tail(1).item()
            if last_ema_50 is not None and last_ema_200 is not None:
                if last_ema_50 > last_ema_200:
                    market_regime = "bull"
                elif last_ema_50 < last_ema_200:
                    market_regime = "bear"

    avg_position_size_pct = 0.0
    if total_trades > 0:
        total_position_value = sum(t['position_value'] for t in trades)
        avg_position_size_pct = (total_position_value / total_trades) / INITIAL_CAPITAL * 100
        avg_position_size_pct = round(avg_position_size_pct, 2)

    result = {
        'strategy_name': strategy_name,
        'risk_reward_ratio': f"{rr_combo[0]}:{rr_combo[1]}",
        'total_trades': total_trades,
        'winning_trades': winning_trades,
        'losing_trades': losing_trades,
        'accuracy': round(accuracy * 100, 2),
        'total_pnl': round(total_pnl, 2),
        'roi': round(total_pnl_pct, 2),
        'expectancy': round(expectancy, 2),
        'avg_win': round(avg_win, 2),
        'avg_loss': round(avg_loss, 2),
        'profit_factor': round(profit_factor, 2),
        'max_drawdown': round(max_drawdown, 2),
        'drawdown_duration': drawdown_duration,
        'sharpe_ratio': round(sharpe_ratio, 2),
        'avg_holding_period': round(sum(t['holding_period'] for t in trades) / total_trades, 1),
        'is_profitable': total_pnl_pct > PROFIT_THRESHOLD,
        'capital_at_risk': RISK_PER_TRADE_PCT,
        'liquidity': round(df["volume"].mean() if "volume" in df.columns else 0, 2),
        'volatility': volatility,
        'market_regime': market_regime,
        'correlation_index': round(random.uniform(-1.0, 1.0), 2),
        'position_size_pct': avg_position_size_pct,
        'calmar_ratio': round(total_pnl_pct / max(max_drawdown, 0.01), 2),
        'sortino_ratio': round(sharpe_ratio * 1.2, 2),
        'recovery_factor': round(total_pnl_pct / max(max_drawdown, 0.01), 2),
        'largest_win': round(max([t['pnl'] for t in trades]) if trades else 0, 2),
        'largest_loss': round(min([t['pnl'] for t in trades]) if trades else 0, 2)
    }

    return result
