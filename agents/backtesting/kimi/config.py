from pathlib import Path

# Configuration paths
GPU_OPTIMIZATION_CONFIG_FILE = Path(__file__).parent.parent.parent / "config" / "gpu_optimization_config.yaml"
STRATEGIES_FILE = Path(__file__).parent.parent.parent / "config" / "strategies.yaml"

# Data directories
DATA_DIR = "data/features"
OUTPUT_DIR = "data/backtest"
TEMP_DIR = "data/backtest/temp"

# Output format
OUTPUT_FORMAT = "parquet"
COMPRESSION = "zstd"
COMPRESSION_LEVEL = 15

# Risk and capital
INITIAL_CAPITAL = 100000
RISK_PER_TRADE_PCT = 1.0
INTRADAY_MARGIN_MULTIPLIER = 3.5

# Trading parameters
TRANSACTION_COST_PCT = 0.05
SLIPPAGE_PCT = 0.02
PROFIT_THRESHOLD = 1.0

# Signal and trade filters
MIN_SIGNAL_DISTANCE = 5  # Minimum bars between signals
MIN_VOLUME_THRESHOLD = 100  # Minimum volume for valid signals
MAX_HOLDING_PERIOD = 100  # Maximum bars to hold position
MIN_PRICE_MOVE = 0.0005  # Minimum price movement

# Concurrency and timeframes
CONCURRENT_FILES = 40
TIMEFRAMES = ["1min", "3min", "5min", "15min"]
