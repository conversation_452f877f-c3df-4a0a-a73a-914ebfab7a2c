import logging
from typing import List, Dict, Any

import numpy as np
import polars as pl

from . import file_io
from . import performance

logger = logging.getLogger(__name__)

async def process_strategies_parallel_async(df, strategies: List[Dict[str, Any]], cuda_optimizer=None) -> Dict[str, np.n<PERSON>ray]:
    """
    GPU-accelerated parallel strategy processing for evolution agent
    Uses the same GPU models as the backtesting agent
    """
    try:
        if cuda_optimizer is None:
            if not performance.CUDA_AVAILABLE:
                performance.init_cuda_safely()
            cuda_optimizer = performance.cuda_optimizer

        if not cuda_optimizer or not cuda_optimizer.cuda_available:
            logger.warning("CUDA not available for parallel processing")
            return {}

        data_arrays = {
            'close': df['close'].to_numpy().astype(np.float32),
            'high': df['high'].to_numpy().astype(np.float32),
            'low': df['low'].to_numpy().astype(np.float32),
            'volume': df['volume'].to_numpy().astype(np.float32)
        }

        from utils.real_gpu_accelerator import real_gpu_accelerator
        gpu_results = real_gpu_accelerator.process_strategies_parallel_gpu(data_arrays, strategies)

        if gpu_results:
            logger.info(f"🚀 GPU parallel processing completed - {len(gpu_results)} strategy results")
            return gpu_results
        else:
            logger.warning("GPU parallel processing returned no results")
            return {}

    except Exception as e:
        logger.error(f"GPU parallel processing failed: {e}")
        return {}

def _validate_real_market_data(df: pl.DataFrame, symbol: str) -> bool:
    """Validate that we're using real market data, not synthetic/mock data"""
    try:
        if df is None or df.is_empty():
            logger.error(f"❌ DATA VALIDATION FAILED: No data for {symbol}")
            return False

        if 'close' not in df.columns:
            logger.error(f"❌ DATA VALIDATION FAILED: Missing 'close' column for {symbol}")
            return False

        try:
            close_col = df['close']
            if close_col.dtype in [pl.Float64, pl.Float32, pl.Int64, pl.Int32]:
                close_prices = close_col.to_numpy()
            else:
                close_prices = close_col.cast(pl.Float64).to_numpy()
            if not isinstance(close_prices, np.ndarray):
                raise ValueError("Could not convert to numpy array")
        except Exception as cast_error:
            logger.error(f"❌ DATA VALIDATION FAILED: Could not cast 'close' column to float for {symbol}: {cast_error}")
            return False

        if len(close_prices) < 100:
            logger.warning(f"⚠️ DATA VALIDATION: Very small dataset for {symbol} ({len(close_prices)} rows)")

        try:
            if len(close_prices) > 1:
                valid_prices = close_prices[np.isfinite(close_prices)]
                if len(valid_prices) > 1 and np.std(valid_prices) < 0.01:
                    logger.error(f"❌ DATA VALIDATION FAILED: Constant prices detected for {symbol} (likely mock data)")
                    return False
            else:
                logger.warning(f"⚠️ DATA VALIDATION: Only {len(close_prices)} close price(s) for {symbol}, cannot check for constant prices.")

            valid_prices = close_prices[np.isfinite(close_prices)]
            if len(valid_prices) == 0:
                logger.error(f"❌ DATA VALIDATION FAILED: No valid prices found for {symbol}")
                return False

            min_price = np.min(valid_prices)
            max_price = np.max(valid_prices)
        except Exception as e:
            logger.error(f"❌ DATA VALIDATION FAILED: Error processing prices for {symbol}: {e}")
            return False

        if min_price <= 0:
            logger.error(f"❌ DATA VALIDATION FAILED: Invalid prices (≤0) for {symbol}")
            return False

        if max_price / min_price > 100:
            logger.warning(f"⚠️ DATA VALIDATION: Extreme price range for {symbol} ({min_price:.2f} to {max_price:.2f})")

        if 'timestamp' in df.columns:
            try:
                df = df.with_columns(
                    pl.col("timestamp").str.replace_all(r"\+0530", "+05:30").str.to_datetime(time_unit="ms", time_zone="Asia/Calcutta").alias("datetime")
                )
                df = df.drop("timestamp")
                datetimes = df['datetime'].to_list()
                if len(datetimes) > 1:
                    parsed_timestamps = [dt.timestamp() for dt in datetimes]
                    time_diffs = np.diff(parsed_timestamps)
                    avg_time_diff = np.mean(time_diffs)
                    if np.std(time_diffs) < 0.1 and avg_time_diff == 60:
                        logger.warning(f"⚠️ DATA VALIDATION: Suspiciously regular time intervals for {symbol}")
            except Exception as e:
                logger.warning(f"⚠️ DATA VALIDATION: Could not validate timestamp patterns for {symbol}: {e}")
                return False
        elif 'datetime' not in df.columns:
            logger.error(f"❌ DATA VALIDATION FAILED: Neither 'timestamp' nor 'datetime' column found for {symbol}")
            return False

        logger.info(f"✅ DATA VALIDATION PASSED: {symbol} ({len(valid_prices)} rows, price range: {min_price:.2f}-{max_price:.2f})")
        return True

    except Exception as e:
        logger.error(f"❌ DATA VALIDATION ERROR for {symbol}: {e}")
        return False

async def run_backtesting_for_evolution(strategies: List[Dict[str, Any]], max_symbols: int = None, max_files: int = None, ranking_threshold: int = 0) -> Dict[str, Any]:
    """
    Interface for Evolution Agent to run backtesting with custom parameters
    """
    try:
        if ranking_threshold > 0:
            strategies = [s for s in strategies if s.get('ranking', 0) >= ranking_threshold]
            logger.info(f"[EVOLUTION] Filtered to {len(strategies)} strategies above ranking {ranking_threshold}")

        feature_files = file_io.get_available_feature_files()
        if max_symbols:
            symbol_files = {}
            for file_info in feature_files:
                symbol = file_info[1]
                if symbol not in symbol_files:
                    symbol_files[symbol] = []
                symbol_files[symbol].append(file_info)

            limited_symbols = list(symbol_files.keys())[:max_symbols]
            feature_files = []
            for symbol in limited_symbols:
                feature_files.extend(symbol_files[symbol])

        if max_files:
            feature_files = feature_files[:max_files]

        logger.info(f"[EVOLUTION] Testing {len(strategies)} strategies on {len(feature_files)} files")

        all_results = []
        for file_info in feature_files:
            file_results = await file_io.process_single_file_sync(file_info, strategies)
            all_results.extend(file_results)

        strategy_performance = {}
        for result in all_results:
            strategy_name = result.get('strategy_name', 'Unknown')
            if strategy_name not in strategy_performance:
                strategy_performance[strategy_name] = {
                    'total_trades': 0,
                    'winning_trades': 0,
                    'total_pnl': 0.0,
                    'total_roi': 0.0,
                    'results_count': 0,
                    'avg_accuracy': 0.0,
                    'avg_sharpe': 0.0,
                    'max_drawdown': 0.0
                }

            perf = strategy_performance[strategy_name]
            perf['total_trades'] += result.get('total_trades', 0)
            perf['winning_trades'] += result.get('winning_trades', 0)
            perf['total_pnl'] += result.get('total_pnl', 0.0)
            perf['total_roi'] += result.get('roi', 0.0)
            perf['results_count'] += 1
            perf['avg_accuracy'] += result.get('accuracy', 0.0)
            perf['avg_sharpe'] += result.get('sharpe_ratio', 0.0)
            perf['max_drawdown'] = max(perf['max_drawdown'], result.get('max_drawdown', 0.0))

        for strategy_name, perf in strategy_performance.items():
            if perf['results_count'] > 0:
                perf['avg_accuracy'] /= perf['results_count']
                perf['avg_sharpe'] /= perf['results_count']
                perf['avg_roi'] = perf['total_roi'] / perf['results_count']
                perf['win_rate'] = perf['winning_trades'] / max(perf['total_trades'], 1)

        return {
            'success': True,
            'total_results': len(all_results),
            'strategies_tested': len(strategies),
            'files_processed': len(feature_files),
            'strategy_performance': strategy_performance,
            'detailed_results': all_results
        }

    except Exception as e:
        logger.error(f"[EVOLUTION] Error in backtesting: {e}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        return {
            'success': False,
            'error': str(e),
            'strategy_performance': {},
            'detailed_results': []
        }
