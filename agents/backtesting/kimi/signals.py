import polars as pl
import numpy as np
from numba import jit
from typing import Dict, Any

from agents.signal_generation.signal_agent import SignalAgent
from .config import MIN_SIGNAL_DISTANCE, STRATEGIES_FILE

signal_agent = None

def init_signal_agent():
    global signal_agent
    if signal_agent is None:
        try:
            signal_agent = SignalAgent(str(STRATEGIES_FILE))
        except Exception as e:
            print(f"Failed to initialize SignalAgent: {e}")
            signal_agent = None

def generate_strategy_signals(df, signal_type: str, strategy: dict):
    """Generate strategy signals using SignalAgent"""
    init_signal_agent()
    signals = signal_agent.generate_signals(df, strategy, [signal_type])
    return signals.get(signal_type, pl.Series("mask", [False] * df.height))

@jit(nopython=True, cache=True)
def _filter_signals_numba(signals_array, min_distance: int):
    if signals_array.sum() == 0:
        return signals_array
    
    signal_indices = np.where(signals_array)[0]
    if len(signal_indices) <= 1:
        return signals_array

    filtered_indices = [signal_indices[0]]

    for i in range(1, len(signal_indices)):
        idx = signal_indices[i]
        if idx - filtered_indices[-1] >= min_distance:
            filtered_indices.append(idx)
        elif len(filtered_indices) < 10:
            filtered_indices.append(idx)

    filtered_signals_array = np.zeros_like(signals_array, dtype=np.bool_)
    for idx in filtered_indices:
        filtered_signals_array[idx] = True
    
    return filtered_signals_array

def filter_signals_by_distance(signals: pl.Series, min_distance: int = MIN_SIGNAL_DISTANCE) -> pl.Series:
    """Filter signals to maintain minimum distance between them - RELAXED"""
    signals_np = signals.to_numpy(allow_copy=True).astype(np.bool_)
    filtered_np = _filter_signals_numba(signals_np, min_distance)
    return pl.Series(signals.name, filtered_np)

def apply_intraday_rules(df: pl.DataFrame, signals: Dict[str, pl.Series], strategy: dict) -> Dict[str, pl.Series]:
    """Apply intraday trading rules using SignalAgent"""
    init_signal_agent()
    if signal_agent is None:
        return signals
    return signal_agent.apply_intraday_rules(df, signals, strategy)

def calculate_daily_opening_range(df: pl.DataFrame, orb_period: int) -> pl.DataFrame:
    """
    Calculates the Opening Range High and Low for each day.
    The opening range is defined by the high and low of the first `orb_period` bars of each day.
    """
    if "datetime" not in df.columns:
        return df

    df = df.sort("datetime")
    df = df.with_columns(
        pl.col("high").over(pl.col("datetime").dt.date()).head(orb_period).max().alias("daily_orb_high"),
        pl.col("low").over(pl.col("datetime").dt.date()).head(orb_period).min().alias("daily_orb_low")
    )
    return df
