import os
import sys
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional

import numpy as np
import polars as pl
from numba import jit, cuda
import torch
from utils.real_gpu_accelerator import real_gpu_accelerator # Moved import to top

from .config import GPU_OPTIMIZATION_CONFIG_FILE

logger = logging.getLogger(__name__)

# High-Performance Computing Configuration
CUDA_AVAILABLE = False
NUMBA_AVAILABLE = False
POLARS_OPTIMIZED = False
MULTIPROCESSING_MODE = False
USE_PROCESS_POOL_EXECUTOR = False
# Removed cuda_optimizer and CUDA_OPTIMIZATIONS as they are no longer used

def _lazy_imports():
    global vbt, numexpr
    import vectorbt as vbt
    import numexpr

_imports_loaded = False

def ensure_imports():
    global _imports_loaded
    if not _imports_loaded:
        _lazy_imports()
        _imports_loaded = True

def init_cuda_safely():
    """Initialize CUDA safely with proper error handling"""
    global CUDA_AVAILABLE, USE_PROCESS_POOL_EXECUTOR

    try:
        if real_gpu_accelerator.cuda_available:
            CUDA_AVAILABLE = True
            logger.info(f"🚀 CUDA acceleration enabled: {torch.cuda.get_device_name(0)}")
            USE_PROCESS_POOL_EXECUTOR = False
            logger.info("⚠️ Multiprocessing disabled because CUDA is available.")
        else:
            logger.info("⚠️ CUDA not available, enabling multiprocessing for CPU parallelization.")
            USE_PROCESS_POOL_EXECUTOR = True

    except Exception as e:
        logger.info(f"⚠️ CUDA initialization failed ({e}), enabling multiprocessing.")
        CUDA_AVAILABLE = False
        USE_PROCESS_POOL_EXECUTOR = True

if not MULTIPROCESSING_MODE:
    init_cuda_safely()
else:
    logger.info("⚠️ CUDA detection skipped in multiprocessing worker.")
    USE_PROCESS_POOL_EXECUTOR = False

if not CUDA_AVAILABLE:
    try:
        from numba import jit, prange
        import numba

        @jit(nopython=True)
        def test_numba():
            return sum(range(100))

        _ = test_numba()
        NUMBA_AVAILABLE = True
        logger.info("⚡ Numba JIT acceleration available (CPU mode).")
    except Exception as e:
        logger.warning(f"⚠️ Numba not available: {e}")

if not CUDA_AVAILABLE and not NUMBA_AVAILABLE and not MULTIPROCESSING_MODE:
    USE_PROCESS_POOL_EXECUTOR = True
    logger.info("⚠️ Neither CUDA nor Numba available, ensuring multiprocessing is enabled for CPU performance.")

def configure_polars():
    global POLARS_OPTIMIZED
    if not POLARS_OPTIMIZED:
        try:
            pl.Config.set_streaming_chunk_size(50000)
            pl.Config.set_fmt_str_lengths(100)
            pl.Config.set_tbl_rows(20)
            POLARS_OPTIMIZED = True
            logger.info("✅ Polars optimizations configured")
        except Exception as e:
            logger.warning(f"Could not configure Polars optimizations: {e}")

@jit(nopython=True, cache=True, parallel=True)
def numba_signal_processing(prices, highs, lows, volumes, n):
    signals = np.zeros(n, dtype=np.int32)
    for idx in range(20, n):
        sma = 0.0
        for i in range(20):
            sma += prices[idx - i]
        sma /= 20.0
        if prices[idx] > sma * 1.02:
            signals[idx] = 1
        elif prices[idx] < sma * 0.98:
            signals[idx] = -1
        else:
            signals[idx] = 0
    return signals

@jit(nopython=True, cache=True)
def numba_trade_simulation(prices, signals, n, stop_loss_pct, take_profit_pct):
    trades = []
    for idx in range(n):
        if signals[idx] != 0:
            entry_price = prices[idx]
            signal_type = signals[idx]
            if signal_type == 1:
                stop_loss = entry_price * (1.0 - stop_loss_pct)
                take_profit = entry_price * (1.0 + take_profit_pct)
            else:
                stop_loss = entry_price * (1.0 + stop_loss_pct)
                take_profit = entry_price * (1.0 - take_profit_pct)
            exit_idx = -1
            exit_price = 0.0
            exit_reason = 0
            for i in range(1, min(100, n - idx)):
                current_price = prices[idx + i]
                if signal_type == 1:
                    if current_price <= stop_loss:
                        exit_idx = idx + i
                        exit_price = stop_loss
                        exit_reason = 1
                        break
                    elif current_price >= take_profit:
                        exit_idx = idx + i
                        exit_price = take_profit
                        exit_reason = 2
                        break
                else:
                    if current_price >= stop_loss:
                        exit_idx = idx + i
                        exit_price = stop_loss
                        exit_reason = 1
                        break
                    elif current_price <= take_profit:
                        exit_idx = idx + i
                        exit_price = take_profit
                        exit_reason = 2
                        break
            if exit_idx > 0:
                pnl = (exit_price - entry_price) * signal_type
                trades.append((entry_price, exit_price, pnl, exit_reason, idx, exit_idx))
    return trades

@jit(nopython=True, cache=True)
def numba_performance_metrics(pnls):
    n = len(pnls)
    cumulative_returns = np.zeros(n)
    cumulative_returns[0] = pnls[0]
    for i in range(1, n):
        cumulative_returns[i] = cumulative_returns[i-1] + pnls[i]
    total_pnl = np.sum(pnls)
    winning_trades = np.sum(pnls > 0)
    losing_trades = np.sum(pnls < 0)
    peak = np.zeros(n)
    peak[0] = cumulative_returns[0]
    for i in range(1, n):
        peak[i] = max(peak[i-1], cumulative_returns[i])
    max_drawdown = 0.0
    for i in range(n):
        if peak[i] > 1e-10:
            drawdown = (peak[i] - cumulative_returns[i]) / peak[i]
            if drawdown > max_drawdown:
                max_drawdown = drawdown
    return total_pnl, winning_trades, losing_trades, max_drawdown, cumulative_returns

@cuda.jit
def cuda_signal_processing_kernel(prices, highs, lows, volumes, signals_out, n):
    idx = cuda.grid(1)
    if idx < n and idx >= 20:
        sma = 0.0
        for i in range(20):
            sma += prices[idx - i]
        sma /= 20.0
        if prices[idx] > sma * 1.02:
            signals_out[idx] = 1
        elif prices[idx] < sma * 0.98:
            signals_out[idx] = -1
        else:
            signals_out[idx] = 0

def accelerated_signal_processing(df, strategy: Dict[str, Any]):
    # Updated to use real_gpu_accelerator directly
    if not real_gpu_accelerator.cuda_available: # Simplified check
        return df
    try:
        prices = df['close'].to_numpy().astype(np.float32)
        highs = df['high'].to_numpy().astype(np.float32)
        lows = df['low'].to_numpy().astype(np.float32)
        volumes = df['volume'].to_numpy().astype(np.float32)
        n = len(prices)
        if CUDA_AVAILABLE and not MULTIPROCESSING_MODE and real_gpu_accelerator.cuda_available: # Simplified check
            try:
                # Placeholder values for blocks_per_grid, threads_per_block, batch_size
                # as real_gpu_accelerator does not expose these directly.
                # These should ideally be handled internally by real_gpu_accelerator's methods.
                blocks_per_grid = (n + 16 - 1) // 16
                threads_per_block = 16
                batch_size = 100000
                if n > batch_size * 2:
                    signals = np.zeros(n, dtype=np.int32)
                    for i in range(0, n, batch_size):
                        end_idx = min(i + batch_size, n)
                        batch_prices = prices[i:end_idx]
                        batch_highs = highs[i:end_idx]
                        batch_lows = lows[i:end_idx]
                        batch_volumes = volumes[i:end_idx]
                        batch_n = len(batch_prices)
                        if batch_n < 20:
                            continue
                        d_prices = cuda.to_device(batch_prices)
                        d_highs = cuda.to_device(batch_highs)
                        d_lows = cuda.to_device(batch_lows)
                        d_volumes = cuda.to_device(batch_volumes)
                        d_signals = cuda.device_array(batch_n, dtype=np.int32)
                        batch_blocks = (batch_n + threads_per_block - 1) // threads_per_block
                        if 'cuda_signal_processing_kernel' in globals():
                            cuda_signal_processing_kernel[batch_blocks, threads_per_block](
                                d_prices, d_highs, d_lows, d_volumes, d_signals, batch_n
                            )
                        signals[i:end_idx] = d_signals.copy_to_host()
                        del d_prices, d_highs, d_lows, d_volumes, d_signals
                else:
                    d_prices = cuda.to_device(prices)
                    d_highs = cuda.to_device(highs)
                    d_lows = cuda.to_device(lows)
                    d_volumes = cuda.to_device(volumes)
                    d_signals = cuda.device_array(n, dtype=np.int32)
                    if 'cuda_signal_processing_kernel' in globals():
                        cuda_signal_processing_kernel[blocks_per_grid, threads_per_block](
                            d_prices, d_highs, d_lows, d_volumes, d_signals, n
                        )
                    signals = d_signals.copy_to_host()
            except Exception as e:
                signals = numba_signal_processing(prices, highs, lows, volumes, n)
        elif NUMBA_AVAILABLE and n >= 10000:
            signals = numba_signal_processing(prices, highs, lows, volumes, n)
        else:
            return df
        df = df.with_columns([pl.Series("accelerated_signals", signals)])
        return df
    except Exception as e:
        logger.warning(f"Accelerated signal processing failed: {e}, using standard processing")
        return df

def accelerated_trade_simulation(df, signals: np.ndarray, stop_loss_pct: float = 0.02, take_profit_pct: float = 0.04) -> List[Dict[str, Any]]:
    signal_count = np.sum(np.abs(signals))
    # Updated to use real_gpu_accelerator directly
    if not real_gpu_accelerator.cuda_available: # Simplified check
        return []
    try:
        prices = df['close'].to_numpy().astype(np.float32)
        n = len(prices)
        trade_results = numba_trade_simulation(prices, signals.astype(np.int32), n, stop_loss_pct, take_profit_pct)
        trades = []
        datetimes = df['datetime'].to_list()
        for entry_price, exit_price, pnl, exit_reason, entry_idx, exit_idx in trade_results:
            if entry_price > 0:
                trades.append({
                    'entry_datetime': datetimes[entry_idx],
                    'exit_datetime': datetimes[min(exit_idx, len(datetimes) - 1)],
                    'entry_price': float(entry_price),
                    'exit_price': float(exit_price),
                    'pnl': float(pnl),
                    'pnl_pct': (pnl / entry_price) * 100,
                    'exit_reason': 'stop_loss' if exit_reason == 1 else 'take_profit' if exit_reason == 2 else 'timeout',
                    'side': 'long' if signals[entry_idx] == 1 else 'short',
                    'quantity': 1,
                    'position_value': float(entry_price),
                    'holding_period': exit_idx - entry_idx
                })
        return trades
    except Exception as e:
        logger.warning(f"Accelerated trade simulation failed: {e}")
        return []

def accelerated_performance_metrics(trades: List[Dict[str, Any]]) -> Dict[str, float]:
    # Updated to use real_gpu_accelerator directly
    if not real_gpu_accelerator.cuda_available: # Simplified check
        return {}
    try:
        pnls = np.array([trade['pnl'] for trade in trades], dtype=np.float32)
        total_pnl, winning_trades, losing_trades, max_drawdown, cumulative_returns = numba_performance_metrics(pnls)
        n = len(pnls)
        win_rate = (winning_trades / n) * 100 if n > 0 else 0
        returns_std = float(np.std(pnls))
        sharpe_ratio = (total_pnl / returns_std) if returns_std > 0 else 0
        return {
            'total_pnl': float(total_pnl),
            'winning_trades': int(winning_trades),
            'losing_trades': int(losing_trades),
            'win_rate': win_rate,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': float(max_drawdown) * 100,
            'total_trades': n
        }
    except Exception as e:
        logger.warning(f"Accelerated performance metrics failed: {e}")
        return {}
