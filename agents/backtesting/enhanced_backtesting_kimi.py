#!/usr/bin/env python3
"""
Enhanced Backtesting System - Main Entry Point
"""

import os
import sys
import asyncio
import logging
import time
from pathlib import Path
from typing import List, Dict, Any, Tuple

# Ensure the project root is in the Python path
project_root = Path(__file__).resolve().parent.parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

import os
import sys
import asyncio
import logging
import time
from pathlib import Path
from typing import List, Dict, Any, Tuple

import polars as pl
import numpy as np

# Ensure the project root is in the Python path
project_root = Path(__file__).resolve().parent.parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

from agents.backtesting.kimi import (
    config,
    file_io
)
from utils.real_gpu_accelerator import real_gpu_accelerator
from agents.signal_generation.signal_agent import SignalAgent # Import SignalAgent

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Initialize SignalAgent globally or pass it around
# It's better to initialize once if strategies don't change during runtime
signal_agent = SignalAgent(str(config.STRATEGIES_FILE))

async def process_file_on_gpu(file_info: Tuple[str, str, str], strategies: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Process a single file on the GPU."""
    file_path, symbol, timeframe = file_info
    logger.info(f"Processing {symbol} ({timeframe}) on GPU...")

    try:
        data_arrays = file_io.load_data_for_gpu(file_path, strategies)
        if data_arrays is None:
            return []

        # Convert numpy arrays to Polars DataFrame for SignalAgent
        # Ensure 'datetime' column is present for intraday rules if needed
        df_data = {k: v for k, v in data_arrays.items()}
        # Assuming 'datetime' is also loaded or can be reconstructed if needed by SignalAgent
        # For now, we'll assume SignalAgent can work with the available columns.
        # If 'datetime' is crucial and not in data_arrays, it needs to be added during data loading.
        
        # Create Polars DataFrame from data_arrays.
        # The 'datetime' column should now be reliably loaded by file_io.load_data_for_gpu.
        df = pl.DataFrame(data_arrays)

        all_strategy_entries: Dict[str, np.ndarray] = {}
        all_strategy_exits: Dict[str, np.ndarray] = {}

        for strategy in strategies:
            strategy_name = strategy.get('name', 'UnknownStrategy')

            # Generate signals using SignalAgent
            signals = signal_agent.get_signals_for_strategy(df, strategy_name, apply_intraday=True)

            if signals:
                # Convert Polars Series to NumPy boolean arrays
                entry_long_arr = signals.get('entry_long', pl.Series("mask", [False] * df.height)).to_numpy().astype(bool)
                entry_short_arr = signals.get('entry_short', pl.Series("mask", [False] * df.height)).to_numpy().astype(bool)
                exit_long_arr = signals.get('exit_long', pl.Series("mask", [False] * df.height)).to_numpy().astype(bool)
                exit_short_arr = signals.get('exit_short', pl.Series("mask", [False] * df.height)).to_numpy().astype(bool)

                # Combine entry signals (long OR short)
                combined_entries = entry_long_arr | entry_short_arr
                combined_exits = exit_long_arr | exit_short_arr

                # Fix conflicting signals: if both entry and exit are True at same bar, prioritize entry
                # This prevents vectorbt from generating 0 trades due to simultaneous entry/exit
                conflict_mask = combined_entries & combined_exits
                combined_exits = combined_exits & ~conflict_mask  # Remove exits where there are conflicts

                all_strategy_entries[strategy_name] = combined_entries
                all_strategy_exits[strategy_name] = combined_exits

                # Log only if there are conflicts (for debugging major issues)
                conflicts_removed = int(conflict_mask.sum())
                if conflicts_removed > 0:
                    logger.debug(f"Resolved {conflicts_removed} signal conflicts for {strategy_name}")

            else:
                logger.warning(f"No signals generated by SignalAgent for strategy {strategy_name}. Skipping.")
                all_strategy_entries[strategy_name] = np.zeros(df.height, dtype=bool)
                all_strategy_exits[strategy_name] = np.zeros(df.height, dtype=bool)

        # Pass pre-generated signals to real_gpu_accelerator
        close_prices = data_arrays['close']
        results = real_gpu_accelerator.vectorized_backtest_gpu(close_prices, all_strategy_entries, all_strategy_exits, strategies)
        
        # Add symbol and timeframe to results and log summary
        total_trades = 0
        for r in results:
            r['symbol'] = symbol
            r['timeframe'] = timeframe
            total_trades += r.get('total_trades', 0)

        if total_trades > 0:
            logger.info(f"✅ {symbol} ({timeframe}): {total_trades} trades generated across {len(results)} strategies")
        else:
            logger.warning(f"⚠️ {symbol} ({timeframe}): No trades generated")

        return results
    except Exception as e:
        logger.error(f"Error processing {symbol} on GPU: {e}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        return []

async def main_async():
    """Main asynchronous entry point"""
    overall_start_time = time.time()
    
    logger.info("[INIT] Starting Enhanced Backtesting System")
    
    strategies = file_io.load_strategies()
    if not strategies:
        logger.error("[ERROR] No strategies loaded, exiting")
        return
    
    feature_files = file_io.get_available_feature_files()
    if not feature_files:
        logger.error("[ERROR] No feature files found, exiting")
        return
    
    logger.info(f"[INFO] Processing {len(feature_files)} files with {len(strategies)} strategies")
    
    Path(config.TEMP_DIR).mkdir(parents=True, exist_ok=True)

    all_backtest_results: List[Dict[str, Any]] = []

    if real_gpu_accelerator.cuda_available:
        logger.info("[INFO] Using GPU for parallel processing.")
        tasks = [process_file_on_gpu(file_info, strategies) for file_info in feature_files]
        results_from_all_files = await asyncio.gather(*tasks)
        
        for result_list in results_from_all_files:
            all_backtest_results.extend(result_list)
    else:
        logger.info("[INFO] Running in sequential CPU mode.")
        for idx, file_info in enumerate(feature_files, 1):
            file_path, symbol, timeframe = file_info
            logger.info(f"Processing file {idx}/{len(feature_files)}: {symbol} ({timeframe})")
            # The CPU path is not implemented in this version
            # You would need to call a CPU-based backtesting function here
            pass

    total_time = time.time() - overall_start_time
    logger.info("[SUCCESS] BACKTESTING COMPLETED!")
    logger.info(f"[TIME] Total time: {total_time:.1f} seconds")
    logger.info(f"[FILES] Files processed: {len(feature_files)}")
    logger.info(f"[RESULTS] Total backtest results generated: {len(all_backtest_results)}")

async def main():
    await main_async()

if __name__ == "__main__":
    asyncio.run(main())
