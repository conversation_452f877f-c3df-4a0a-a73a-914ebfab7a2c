#!/usr/bin/env python3
"""
Execution Agent - Order Lifecycle Management and Trade Execution

Features:
⚙️ 1. Order Lifecycle Management
- Convert structured signals → Angel One order placement
- Auto-calculation of quantity, type (MIS), price, stop-loss, target
- Monitor order status and confirm success/failure with retry logic
- Cancel orders (SL/TP or unmatched) if signal expires or reverses
- Modify orders to update SL or target based on trailing logic

[SECURE] 2. Execution Safety Checks
- Market hours validation (9:15-15:25) with pre-market setup support
- Quantity & price validation for lot size, price steps, margin rules
- Error handling with retry on API timeout and failure logging
- Execution delay monitoring with warnings for trades >2s

[MESSAGE] 3. Order Type Management (Angel One Specific)
- MIS (Intraday) margin trading support
- LIMIT/MARKET order types with manual or auto price entry
- SL-L/SL-M stop loss trigger orders
- Comprehensive order response logging and tracking

[METRICS] 4. Trade Execution Feedback + Logging
- Order response logging with order_id, status, timestamp
- Retry queue for failed broker API calls (up to 3x)
- Trade dashboard feed for live UI or Telegram notifications
- Daily trade report storage in parquet for PnL analytics

[COMM] 5. Webhook/Async Queue Support
- Accept signal payloads from other agents via HTTP or queue
- Async order dispatch to avoid blocking system
- Order ID return with status to calling agent
- Integration with Risk Agent for pre-execution approval
"""

import os
import sys
import asyncio
import logging
import json
import yaml
import polars as pl
import pyarrow as pa
from datetime import datetime, timedelta, time
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass, asdict, field
from collections import defaultdict, deque
from enum import Enum
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

@dataclass
class MarginRequirement:
    """Margin requirement details"""
    symbol: str
    exchange: str
    quantity: int
    price: float
    transaction_type: str
    product_type: str
    margin_required: float
    available_margin: float
    limit_used_percent: float
    is_allowed: bool
    error_message: Optional[str] = None

@dataclass
class RMSLimits:
    """RMS Limits details"""
    available_cash: float
    collateral: float
    total_limit: float
    utilized_margin: float
    span_margin: float
    exposure_margin: float
    option_premium: float
    equity_limit: float
    commodity_limit: float
    currency_limit: float

@dataclass
class PositionData:
    """Position details"""
    symbol: str
    exchange: str
    product_type: str
    quantity: int
    avg_price: float
    ltp: float
    pnl: float
    realized_pnl: float
    unrealized_pnl: float

@dataclass
class FundData:
    """Funds details"""
    available_cash: float
    ledger_balance: float
    margin_used: float
    unrealized_pnl: float
    realized_pnl: float
    collateral: float

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# SmartAPI integration
try:
    from SmartApi import SmartConnect
    from SmartApi.smartWebSocketV2 import SmartWebSocketV2
except ImportError:
    print("[WARN]  SmartAPI not installed. Install with: pip install smartapi-python")
    SmartConnect = None
    SmartWebSocketV2 = None

# Import utility modules
try:
    from utils.paper_trading import VirtualAccount, PaperTrade
except ImportError:
    print("[WARN]  Paper trading utilities not found. Some features will be disabled.")
    VirtualAccount = None

# Import other agents
try:
    from agents.risk_management_gateway import RiskManagementGateway
    from utils.risk_models import TradeRequest, TradeDirection, ProductType, OrderType
    RISK_MANAGEMENT_AVAILABLE = True
except ImportError:
    print("[WARN]  Risk Management Gateway not found. Some features will be disabled.")
    RiskManagementGateway = None
    RISK_MANAGEMENT_AVAILABLE = False

# Enhanced Model Integration
try:
    from utils.enhanced_model_integration import EnhancedModelIntegration, get_enhanced_model_integration
    ENHANCED_MODELS_AVAILABLE = True
except ImportError:
    print("[WARN]  Enhanced Model Integration not found. Advanced ML execution optimization will be disabled.")
    EnhancedModelIntegration = None
    ENHANCED_MODELS_AVAILABLE = False

# Notifications
try:
    import telegram
    from telegram import Bot
    TELEGRAM_AVAILABLE = True
except ImportError:
    print("[WARN]  Telegram not installed. Install with: pip install python-telegram-bot")
    TELEGRAM_AVAILABLE = False

logger = logging.getLogger(__name__)

# ═══════════════════════════════════════════════════════════════════════════════
# [STATUS] DATA MODELS
# ═══════════════════════════════════════════════════════════════════════════════

class OrderStatus(Enum):
    """Order status enumeration"""
    PENDING = "PENDING"
    OPEN = "OPEN"
    COMPLETE = "COMPLETE"
    CANCELLED = "CANCELLED"
    REJECTED = "REJECTED"
    TRIGGER_PENDING = "TRIGGER_PENDING"
    PARTIAL = "PARTIAL"

class OrderType(Enum):
    """Order type enumeration"""
    MARKET = "MARKET"
    LIMIT = "LIMIT"
    SL_MARKET = "SL-M"
    SL_LIMIT = "SL-L"

class ProductType(Enum):
    """Product type enumeration"""
    MIS = "MIS"  # Intraday
    CNC = "CNC"  # Cash and Carry
    NRML = "NRML"  # Normal

class TransactionType(Enum):
    """Transaction type enumeration"""
    BUY = "BUY"
    SELL = "SELL"

@dataclass
class SignalPayload:
    """Signal payload from Signal Generation Agent"""
    symbol: str
    exchange: str
    symbol_token: str
    action: str  # BUY/SELL
    entry_price: float
    sl_price: float
    target_price: float
    quantity: int
    order_type: str = "LIMIT"
    product_type: str = "MIS"
    strategy_name: str = ""
    signal_id: str = ""
    timestamp: datetime = None
    risk_reward_ratio: float = 0.0
    confidence_score: float = 0.0
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()

@dataclass
class OrderRequest:
    """Order request structure for Angel One API"""
    variety: str = "NORMAL"
    tradingsymbol: str = ""
    symboltoken: str = ""
    transactiontype: str = ""
    exchange: str = ""
    ordertype: str = ""
    producttype: str = ""
    duration: str = "DAY"
    price: str = "0"
    squareoff: str = "0"
    stoploss: str = "0"
    quantity: str = "0"
    
    def to_dict(self) -> Dict[str, str]:
        """Convert to dictionary for API call"""
        return asdict(self)

@dataclass
class OrderResponse:
    """Order response from Angel One API"""
    order_id: str
    status: str
    message: str
    timestamp: datetime
    signal_id: str = ""
    order_request: Optional[OrderRequest] = None
    execution_time_ms: float = 0.0
    slippage_percent: float = 0.0
    
@dataclass
class TradeExecution:
    """Complete trade execution record"""
    signal_payload: SignalPayload
    entry_order: Optional[OrderResponse] = None
    sl_order: Optional[OrderResponse] = None
    target_order: Optional[OrderResponse] = None
    status: str = "PENDING"
    created_at: datetime = None
    updated_at: datetime = None
    execution_summary: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.updated_at is None:
            self.updated_at = datetime.now()
        if self.execution_summary is None:
            self.execution_summary = {}

# ═══════════════════════════════════════════════════════════════════════════════
# [INIT] EXECUTION AGENT
# ═══════════════════════════════════════════════════════════════════════════════

class ExecutionAgent:
    """
    Execution Agent for order lifecycle management and trade execution
    
    Handles:
    - Signal processing and order placement
    - Order status monitoring and management
    - Risk validation and safety checks
    - Execution feedback and logging
    - Integration with Angel One SmartAPI
    """
    
    def __init__(self, config_path: str = "agents/config/execution_config.yaml",
                 trading_mode: str = "paper", trading_config: Dict[str, Any] = None):
        """Initialize Execution Agent"""

        # Load configuration
        self.config = self._load_config(config_path)

        # Trading mode configuration
        self.trading_mode = trading_mode
        self.trading_config = trading_config or {}
        self.paper_trading_enabled = trading_mode == "paper"

        # Initialize components
        self.angel_api = None
        self.risk_gateway = None
        self.telegram_bot = None
        self.virtual_account = None
        self.enhanced_models = None

        # Order tracking
        self.active_orders: Dict[str, TradeExecution] = {}
        self.order_history: List[TradeExecution] = []
        self.retry_queue: deque = deque()

        # Performance tracking
        self.execution_stats = {
            'total_orders': 0,
            'successful_orders': 0,
            'failed_orders': 0,
            'avg_execution_time_ms': 0.0,
            'avg_slippage_percent': 0.0
        }

        # Market hours
        self.market_open_time = time(9, 15)  # 9:15 AM
        self.market_close_time = time(15, 25)  # 3:25 PM
        self.pre_market_start = time(9, 0)  # 9:00 AM for pre-market setup

        # Initialize logging
        self._setup_logging()

        logger.info(f"[INIT] Execution Agent initialized in {trading_mode.upper()} mode")
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Load configuration from YAML file with environment variable resolution"""
        try:
            # Use ConfigurationLoader for proper environment variable resolution
            from utils.config_loader import ConfigurationLoader
            config_loader = ConfigurationLoader()
            config = config_loader.load_agent_config(config_path)
            logger.info(f"[SUCCESS] Configuration loaded from {config_path}")
            return config
        except Exception as e:
            logger.error(f"[ERROR] Failed to load config from {config_path}: {e}")
            return {}
    
    def _setup_logging(self):
        """Setup logging configuration"""
        log_config = self.config.get('logging', {})
        log_level = getattr(logging, log_config.get('level', 'INFO'))
        
        # Configure logger
        logger.setLevel(log_level)
        
        # File handler
        if log_config.get('file_enabled', True):
            log_file = log_config.get('file_path', 'logs/execution_agent.log')
            os.makedirs(os.path.dirname(log_file), exist_ok=True)
            
            file_handler = logging.FileHandler(log_file)
            file_handler.setLevel(log_level)
            
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)

    async def initialize(self):
        """Initialize all components"""
        try:
            # Initialize based on trading mode
            if self.paper_trading_enabled:
                # Initialize Virtual Account for paper trading
                if VirtualAccount:
                    self.virtual_account = VirtualAccount(self.trading_config)
                    logger.info("[SUCCESS] Virtual Account initialized for paper trading")
                else:
                    logger.error("[ERROR] VirtualAccount not available for paper trading")
                    return False
            else:
                # Initialize SmartAPI client for real trading
                if SmartConnect and self.config.get('angel_one_api', {}).get('enabled', True):
                    api_key = self.config.get('angel_one_api', {}).get('api_key')
                    username = self.config.get('angel_one_api', {}).get('username')
                    password = self.config.get('angel_one_api', {}).get('password')
                    totp_token = self.config.get('angel_one_api', {}).get('totp_token')

                    if not all([api_key, username, password, totp_token]):
                        logger.error("[ERROR] SmartAPI credentials incomplete for real trading.")
                        return False

                    try:
                        self.angel_api = SmartConnect(api_key=api_key)
                        import pyotp
                        totp = pyotp.TOTP(totp_token)
                        totp_code = totp.now()

                        data = self.angel_api.generateSession(
                            clientCode=username,
                            password=password,
                            totp=totp_code
                        )

                        if data and data.get('status'):
                            self.angel_api.auth_token = data['data']['jwtToken']
                            self.angel_api.refresh_token = data['data']['refreshToken']
                            self.angel_api.feed_token = self.angel_api.getfeedToken()
                            logger.info("[SUCCESS] SmartAPI client initialized and authenticated for real trading")
                        else:
                            message = data.get('message', 'Unknown error') if data else 'No response'
                            logger.error(f"[ERROR] SmartAPI authentication failed for real trading: {message}")
                            return False
                    except Exception as e:
                        logger.error(f"[ERROR] Failed to initialize SmartAPI client for real trading: {e}")
                        return False
                else:
                    logger.error("[ERROR] SmartAPI not available or not enabled for real trading")
                    return False

            # Initialize Risk Management Gateway
            if RISK_MANAGEMENT_AVAILABLE and RiskManagementGateway and self.config.get('risk_management', {}).get('enabled', True):
                try:
                    self.risk_gateway = RiskManagementGateway(
                        event_bus=self.event_bus,
                        config=self.config,
                        session_id=self.session_id
                    )
                    await self.risk_gateway.setup()
                    logger.info("[SUCCESS] Risk Management Gateway initialized")
                except Exception as e:
                    logger.error(f"[ERROR] Risk Management Gateway setup failed: {e}")
                    self.risk_gateway = None

            # Initialize Telegram bot
            if TELEGRAM_AVAILABLE and self.config.get('notifications', {}).get('telegram', {}).get('enabled', False):
                bot_token = self.config['notifications']['telegram']['bot_token']
                self.telegram_bot = Bot(token=bot_token)
                logger.info("[SUCCESS] Telegram bot initialized")

            # Initialize Enhanced Model Integration
            await self._setup_enhanced_models()

            # Download historical data and prepare signals for live trading
            await self._prepare_for_live_trading()

            # Start background tasks
            asyncio.create_task(self._order_monitoring_loop())
            asyncio.create_task(self._retry_queue_processor())

            logger.info("[INIT] Execution Agent fully initialized")
            return True

        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize Execution Agent: {e}")
            return False

    async def _setup_enhanced_models(self):
        """Setup Enhanced Model Integration for execution optimization"""
        if not ENHANCED_MODELS_AVAILABLE:
            logger.warning("[WARN]  Enhanced Model Integration not available")
            return

        try:
            # Initialize enhanced model integration
            self.enhanced_models = get_enhanced_model_integration()
            
            # Initialize models
            success = await self.enhanced_models.initialize()
            
            if success:
                logger.info("[SUCCESS] Enhanced Model Integration setup completed for Execution Agent")
                
                # Log model performance summary
                summary = self.enhanced_models.get_model_performance_summary()
                logger.info(f"[INFO] Execution Agent using {summary['model_count']} enhanced models for optimization")
                
                # Log specific models useful for execution
                if 'profitability_classification' in summary['loaded_models']:
                    logger.info("[INFO] Profitability model available for execution timing optimization")
                if 'roi_prediction' in summary['loaded_models']:
                    logger.info("[INFO] ROI prediction model available for position sizing optimization")
            else:
                logger.error("[ERROR] Enhanced Model Integration initialization failed")
                self.enhanced_models = None

        except Exception as e:
            logger.error(f"[ERROR] Enhanced Model Integration setup failed: {e}")
            self.enhanced_models = None

    async def _prepare_for_live_trading(self):
        """Prepare for live trading by downloading historical data and generating signals"""
        try:
            logger.info("[PREP] Preparing for live trading session...")

            # Check if we have recent signals
            signal_files = list(Path("data/signals").glob("*.parquet")) if Path("data/signals").exists() else []

            if not signal_files:
                logger.info("[PREP] No recent signals found, generating new signals...")
                # In a real implementation, this would trigger signal generation
                # For now, we'll just log that signals would be generated
                logger.info("[PREP] Signal generation would be triggered here")
            else:
                logger.info(f"[PREP] Found {len(signal_files)} signal files")

            # Prepare trading session
            logger.info("[PREP] Trading session preparation completed")

        except Exception as e:
            logger.error(f"[ERROR] Failed to prepare for live trading: {e}")
            raise

    def _is_market_hours(self) -> bool:
        """Check if current time is within market hours"""
        current_time = datetime.now().time()

        # Allow pre-market setup
        if self.config.get('market_hours', {}).get('allow_pre_market', True):
            return self.pre_market_start <= current_time <= self.market_close_time
        else:
            return self.market_open_time <= current_time <= self.market_close_time

    def _validate_signal(self, signal: SignalPayload) -> Tuple[bool, str]:
        """Validate incoming signal"""
        try:
            # Basic validation
            if not signal.symbol or not signal.exchange:
                return False, "Missing symbol or exchange"

            if signal.quantity <= 0:
                return False, "Invalid quantity"

            if signal.entry_price <= 0:
                return False, "Invalid entry price"

            # Market hours check
            if not self._is_market_hours():
                return False, f"Outside market hours. Current time: {datetime.now().time()}"

            # Risk-reward validation
            min_rr = self.config.get('risk_management', {}).get('min_rr_ratio', 1.5)
            if signal.action == "BUY":
                rr_ratio = (signal.target_price - signal.entry_price) / (signal.entry_price - signal.sl_price)
            else:
                rr_ratio = (signal.entry_price - signal.target_price) / (signal.sl_price - signal.entry_price)

            if rr_ratio < min_rr:
                return False, f"Risk-reward ratio {rr_ratio:.2f} below minimum {min_rr}"

            return True, "Signal validation passed"

        except Exception as e:
            return False, f"Validation error: {str(e)}"

    async def process_signal(self, signal: SignalPayload) -> Tuple[bool, str, Optional[TradeExecution]]:
        """
        Process incoming signal and execute trade

        Args:
            signal: Signal payload from Signal Generation Agent

        Returns:
            Tuple of (success, message, trade_execution)
        """
        try:
            logger.info(f"📥 Processing signal: {signal.symbol} {signal.action} @ {signal.entry_price}")

            # Validate signal
            is_valid, validation_msg = self._validate_signal(signal)
            if not is_valid:
                logger.warning(f"[WARN]  Signal validation failed: {validation_msg}")
                return False, validation_msg, None

            # Enhanced ML-based execution optimization
            optimized_signal = await self._optimize_signal_with_ml(signal)
            if optimized_signal:
                signal = optimized_signal
                logger.info(f"[ENHANCED_ML] Signal optimized using ML models")

            # Risk management validation
            if hasattr(self, 'risk_gateway') and self.risk_gateway:
                trade_request = TradeRequest(
                    signal_id=getattr(signal, 'signal_id', f"EXEC_{signal.symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"),
                    symbol=signal.symbol,
                    exchange=signal.exchange,
                    quantity=signal.quantity,
                    entry_price=signal.entry_price,
                    stop_loss=signal.sl_price,
                    take_profit=getattr(signal, 'target_price', signal.sl_price),  # Use target_price if available
                    direction=TradeDirection.LONG if signal.action == "BUY" else TradeDirection.SHORT,
                    product_type=ProductType.MIS,
                    order_type=OrderType.MARKET,  # Add missing order_type
                    strategy_name=signal.strategy_name,
                    risk_amount=getattr(signal, 'risk_amount', abs(signal.entry_price - signal.sl_price) * signal.quantity),
                    capital_allocated=getattr(signal, 'capital_allocated', 50000.0),
                    risk_reward_ratio=getattr(signal, 'risk_reward_ratio', 2.0),
                    market_regime=getattr(signal, 'market_regime', 'neutral'),
                    confidence=getattr(signal, 'confidence', 0.5),
                    timestamp=datetime.now(),
                    context=getattr(signal, 'context', {})
                )

                risk_approved = await self.risk_gateway.validate_trade_request(trade_request)
                if not risk_approved.is_valid:
                    logger.warning(f"[WARN]  Risk validation failed: {risk_approved.rejection_reason}")
                    return False, f"Risk validation failed: {risk_approved.rejection_reason}", None

            # Margin validation for real trading
            if not self.paper_trading_enabled and self.angel_api:
                margin_valid, margin_msg = await self._validate_margin_requirement(signal)
                if not margin_valid:
                    logger.warning(f"[WARN]  Margin validation failed: {margin_msg}")
                    return False, f"Margin validation: {margin_msg}", None

            # Create trade execution record
            trade_execution = TradeExecution(signal_payload=signal)

            # Place entry order (paper or real)
            if self.paper_trading_enabled:
                entry_success, entry_response = await self._place_paper_order(signal)
            else:
                entry_success, entry_response = await self._place_entry_order(signal)

            if not entry_success:
                logger.error(f"[ERROR] Failed to place entry order: {entry_response}")
                return False, f"Entry order failed: {entry_response}", trade_execution

            trade_execution.entry_order = entry_response
            trade_execution.status = "ENTRY_PLACED"

            # Place stop loss order
            if signal.sl_price > 0:
                sl_success, sl_response = await self._place_stop_loss_order(signal, entry_response.order_id)
                if sl_success:
                    trade_execution.sl_order = sl_response
                else:
                    logger.warning(f"[WARN]  Failed to place SL order: {sl_response}")

            # Place target order
            if signal.target_price > 0:
                target_success, target_response = await self._place_target_order(signal, entry_response.order_id)
                if target_success:
                    trade_execution.target_order = target_response
                else:
                    logger.warning(f"[WARN]  Failed to place target order: {target_response}")

            # Track the trade
            self.active_orders[entry_response.order_id] = trade_execution

            # Send notification
            await self._send_notification(f"[SUCCESS] Trade executed: {signal.symbol} {signal.action} @ {signal.entry_price}")

            # Update statistics
            self.execution_stats['total_orders'] += 1
            self.execution_stats['successful_orders'] += 1

            logger.info(f"[SUCCESS] Signal processed successfully: Order ID {entry_response.order_id}")
            return True, f"Trade executed successfully: {entry_response.order_id}", trade_execution

        except Exception as e:
            logger.error(f"[ERROR] Error processing signal: {e}")
            self.execution_stats['total_orders'] += 1
            self.execution_stats['failed_orders'] += 1
            return False, f"Processing error: {str(e)}", None

    async def _optimize_signal_with_ml(self, signal: SignalPayload) -> Optional[SignalPayload]:
        """Optimize signal parameters using enhanced ML models"""
        try:
            if not self.enhanced_models:
                return None
            
            # Prepare strategy data for enhanced models
            strategy_data = self._prepare_signal_strategy_data(signal)
            
            # Get predictions from enhanced models
            predictions = await self.enhanced_models.predict_all_tasks(strategy_data)
            
            if not predictions:
                logger.warning("[ENHANCED_ML] No predictions available for signal optimization")
                return None
            
            # Create optimized signal copy
            optimized_signal = SignalPayload(
                signal_id=signal.signal_id,
                symbol=signal.symbol,
                exchange=signal.exchange,
                action=signal.action,
                quantity=signal.quantity,
                entry_price=signal.entry_price,
                sl_price=signal.sl_price,
                target_price=getattr(signal, 'target_price', 0.0),
                strategy_name=signal.strategy_name,
                confidence=signal.confidence,
                timestamp=signal.timestamp,
                order_type=signal.order_type,
                product_type=signal.product_type,
                symbol_token=signal.symbol_token
            )
            
            # Optimize quantity based on profitability prediction
            prof_pred = predictions.get('profitability_classification')
            if prof_pred and prof_pred.confidence > 0.7:
                if prof_pred.prediction == 1:  # Profitable
                    # Increase quantity by up to 20% for high-confidence profitable signals
                    quantity_multiplier = 1.0 + (prof_pred.confidence - 0.7) * 0.67  # Max 1.2x
                    optimized_signal.quantity = int(signal.quantity * quantity_multiplier)
                    logger.info(f"[ENHANCED_ML] Increased quantity by {(quantity_multiplier-1)*100:.1f}% based on profitability prediction")
                else:  # Not profitable
                    # Decrease quantity by up to 30% for high-confidence unprofitable signals
                    quantity_multiplier = 1.0 - (prof_pred.confidence - 0.7) * 1.0  # Min 0.7x
                    optimized_signal.quantity = max(1, int(signal.quantity * quantity_multiplier))
                    logger.info(f"[ENHANCED_ML] Decreased quantity by {(1-quantity_multiplier)*100:.1f}% based on profitability prediction")
            
            # Optimize stop loss based on drawdown prediction
            drawdown_pred = predictions.get('drawdown_prediction')
            if drawdown_pred and drawdown_pred.confidence > 0.6:
                predicted_drawdown = abs(drawdown_pred.prediction)
                if predicted_drawdown > 15.0:  # High drawdown risk
                    # Tighten stop loss by 20%
                    if signal.action == "BUY":
                        sl_distance = signal.entry_price - signal.sl_price
                        optimized_signal.sl_price = signal.entry_price - (sl_distance * 0.8)
                    else:
                        sl_distance = signal.sl_price - signal.entry_price
                        optimized_signal.sl_price = signal.entry_price + (sl_distance * 0.8)
                    logger.info(f"[ENHANCED_ML] Tightened stop loss by 20% due to high drawdown risk: {predicted_drawdown:.2f}%")
            
            # Optimize target based on ROI prediction
            roi_pred = predictions.get('roi_prediction')
            if roi_pred and roi_pred.confidence > 0.6 and hasattr(signal, 'target_price') and signal.target_price > 0:
                predicted_roi = roi_pred.prediction
                if predicted_roi > 10.0:  # High ROI potential
                    # Extend target by up to 30%
                    if signal.action == "BUY":
                        target_distance = signal.target_price - signal.entry_price
                        roi_multiplier = min(1.3, 1.0 + (predicted_roi - 10.0) / 50.0)
                        optimized_signal.target_price = signal.entry_price + (target_distance * roi_multiplier)
                    else:
                        target_distance = signal.entry_price - signal.target_price
                        roi_multiplier = min(1.3, 1.0 + (predicted_roi - 10.0) / 50.0)
                        optimized_signal.target_price = signal.entry_price - (target_distance * roi_multiplier)
                    logger.info(f"[ENHANCED_ML] Extended target by {(roi_multiplier-1)*100:.1f}% based on ROI prediction: {predicted_roi:.2f}%")
            
            # Update confidence based on ML predictions
            ml_confidence_scores = []
            if prof_pred:
                ml_confidence_scores.append(prof_pred.confidence)
            if drawdown_pred:
                ml_confidence_scores.append(drawdown_pred.confidence)
            if roi_pred:
                ml_confidence_scores.append(roi_pred.confidence)
            
            if ml_confidence_scores:
                ml_confidence = sum(ml_confidence_scores) / len(ml_confidence_scores)
                # Blend original confidence with ML confidence (70% ML, 30% original)
                optimized_signal.confidence = (ml_confidence * 0.7) + (signal.confidence * 0.3)
                logger.info(f"[ENHANCED_ML] Updated confidence from {signal.confidence:.3f} to {optimized_signal.confidence:.3f}")
            
            return optimized_signal
            
        except Exception as e:
            logger.error(f"[ERROR] Signal optimization with ML failed: {e}")
            return None

    def _prepare_signal_strategy_data(self, signal: SignalPayload) -> Dict[str, Any]:
        """Prepare strategy data for enhanced model predictions based on signal"""
        try:
            # Calculate basic metrics from signal
            entry_price = signal.entry_price
            sl_price = signal.sl_price
            target_price = getattr(signal, 'target_price', 0.0)
            
            # Calculate risk-reward ratio
            if signal.action == "BUY" and sl_price > 0:
                risk = entry_price - sl_price
                reward = target_price - entry_price if target_price > 0 else risk * 2
            elif signal.action == "SELL" and sl_price > 0:
                risk = sl_price - entry_price
                reward = entry_price - target_price if target_price > 0 else risk * 2
            else:
                risk = entry_price * 0.02  # 2% default risk
                reward = risk * 2  # 2:1 default RR
            
            rr_ratio = reward / risk if risk > 0 else 2.0
            
            # Simulate strategy performance metrics based on signal characteristics
            base_sharpe = max(-2.0, min(3.0, (rr_ratio - 1.0) * 0.8))
            base_roi = base_sharpe * 8
            base_profit_factor = max(0.5, 1.0 + base_sharpe * 0.3)
            base_drawdown = -abs(15 - abs(base_sharpe * 5))
            
            # Adjust based on confidence
            confidence_multiplier = signal.confidence if hasattr(signal, 'confidence') else 0.5
            base_sharpe *= confidence_multiplier * 2
            base_roi *= confidence_multiplier * 2
            
            strategy_data = {
                # Sharpe ratio metrics
                'avg_sharpe_ratio': base_sharpe,
                'min_sharpe_ratio': base_sharpe - 0.5,
                'max_sharpe_ratio': base_sharpe + 0.5,
                'std_sharpe_ratio': 0.3,
                
                # ROI metrics
                'avg_roi': base_roi,
                'min_roi': base_roi - 5,
                'max_roi': base_roi + 5,
                'std_roi': 3.0,
                
                # Profit factor metrics
                'avg_profit_factor': base_profit_factor,
                'min_profit_factor': base_profit_factor - 0.2,
                'max_profit_factor': base_profit_factor + 0.2,
                'std_profit_factor': 0.1,
                
                # Drawdown metrics
                'avg_max_drawdown': base_drawdown,
                'min_max_drawdown': base_drawdown - 3,
                'max_max_drawdown': base_drawdown + 3,
                'std_max_drawdown': 2.0,
                
                # Expectancy metrics
                'avg_expectancy': base_roi * 0.5,
                'min_expectancy': base_roi * 0.3,
                'max_expectancy': base_roi * 0.7,
                'std_expectancy': base_roi * 0.1,
                
                # Accuracy metrics
                'avg_accuracy': 50 + (base_sharpe * 10),
                'min_accuracy': 40 + (base_sharpe * 8),
                'max_accuracy': 60 + (base_sharpe * 12),
                'std_accuracy': 5.0,
                
                # Trade metrics
                'avg_total_trades': 50,
                'std_total_trades': 10,
                'avg_winning_trades': 25,
                'std_winning_trades': 5,
                
                # PnL metrics
                'avg_total_pnl': base_roi * 100,
                'min_total_pnl': base_roi * 80,
                'max_total_pnl': base_roi * 120,
                'std_total_pnl': base_roi * 20,
                
                # Consistency metrics
                'consistency_score': max(0.1, min(0.9, confidence_multiplier)),
                'sharpe_consistency': max(0.1, min(0.9, 0.6 + base_sharpe * 0.15)),
                'roi_drawdown_ratio': abs(base_roi / base_drawdown) if base_drawdown != 0 else 1.0,
                
                # Other metrics
                'trades_per_period': 10.0,
                'walk_forward_steps': 5
            }
            
            return strategy_data
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to prepare signal strategy data: {e}")
            return {}

    async def _place_entry_order(self, signal: SignalPayload) -> Tuple[bool, Union[OrderResponse, str]]:
        """Place entry order"""
        try:
            start_time = datetime.now()

            # Prepare order parameters
            order_request = OrderRequest(
                variety="NORMAL",
                tradingsymbol=signal.symbol,
                symboltoken=signal.symbol_token,
                transactiontype=signal.action,
                exchange=signal.exchange,
                ordertype=signal.order_type,
                producttype=signal.product_type,
                duration="DAY",
                price=str(signal.entry_price),
                quantity=str(signal.quantity),
                squareoff="0",
                stoploss="0"
            )

            # Place order via Angel One API
            if not self.angel_api:
                return False, "SmartAPI client not initialized"

            response = self.angel_api.placeOrder(order_request.to_dict())

            execution_time = (datetime.now() - start_time).total_seconds() * 1000

            if response.get('status'):
                order_id = response['data']['orderid']

                order_response = OrderResponse(
                    order_id=order_id,
                    status="PLACED",
                    message=response.get('message', 'Order placed successfully'),
                    timestamp=datetime.now(),
                    signal_id=signal.signal_id,
                    order_request=order_request,
                    execution_time_ms=execution_time
                )

                logger.info(f"[SUCCESS] Entry order placed: {order_id} in {execution_time:.2f}ms")

                # Check for execution delay
                if execution_time > 2000:  # 2 seconds
                    logger.warning(f"[WARN]  Slow execution detected: {execution_time:.2f}ms")

                return True, order_response
            else:
                error_msg = response.get('message', 'Unknown error')
                logger.error(f"[ERROR] Entry order failed: {error_msg}")
                return False, error_msg

        except Exception as e:
            logger.error(f"[ERROR] Error placing entry order: {e}")
            return False, str(e)

    async def _place_stop_loss_order(self, signal: SignalPayload, parent_order_id: str) -> Tuple[bool, Union[OrderResponse, str]]:
        """Place stop loss order"""
        try:
            # Determine SL order type and transaction type
            sl_transaction_type = "SELL" if signal.action == "BUY" else "BUY"

            order_request = OrderRequest(
                variety="NORMAL",
                tradingsymbol=signal.symbol,
                symboltoken=signal.symbol_token,
                transactiontype=sl_transaction_type,
                exchange=signal.exchange,
                ordertype="SL-M",  # Stop Loss Market order
                producttype=signal.product_type,
                duration="DAY",
                price=str(signal.sl_price),
                quantity=str(signal.quantity),
                squareoff="0",
                stoploss="0"
            )

            response = self.angel_api.placeOrder(order_request.to_dict())

            if response.get('status'):
                order_id = response['data']['orderid']

                order_response = OrderResponse(
                    order_id=order_id,
                    status="PLACED",
                    message=response.get('message', 'SL order placed successfully'),
                    timestamp=datetime.now(),
                    signal_id=signal.signal_id,
                    order_request=order_request
                )

                logger.info(f"[SUCCESS] Stop loss order placed: {order_id}")
                return True, order_response
            else:
                error_msg = response.get('message', 'Unknown error')
                logger.error(f"[ERROR] Stop loss order failed: {error_msg}")
                return False, error_msg

        except Exception as e:
            logger.error(f"[ERROR] Error placing stop loss order: {e}")
            return False, str(e)

    async def _place_target_order(self, signal: SignalPayload, parent_order_id: str) -> Tuple[bool, Union[OrderResponse, str]]:
        """Place target order"""
        try:
            # Determine target transaction type
            target_transaction_type = "SELL" if signal.action == "BUY" else "BUY"

            order_request = OrderRequest(
                variety="NORMAL",
                tradingsymbol=signal.symbol,
                symboltoken=signal.symbol_token,
                transactiontype=target_transaction_type,
                exchange=signal.exchange,
                ordertype="LIMIT",
                producttype=signal.product_type,
                duration="DAY",
                price=str(signal.target_price),
                quantity=str(signal.quantity),
                squareoff="0",
                stoploss="0"
            )

            response = self.angel_api.placeOrder(order_request.to_dict())

            if response.get('status'):
                order_id = response['data']['orderid']

                order_response = OrderResponse(
                    order_id=order_id,
                    status="PLACED",
                    message=response.get('message', 'Target order placed successfully'),
                    timestamp=datetime.now(),
                    signal_id=signal.signal_id,
                    order_request=order_request
                )

                logger.info(f"[SUCCESS] Target order placed: {order_id}")
                return True, order_response
            else:
                error_msg = response.get('message', 'Unknown error')
                logger.error(f"[ERROR] Target order failed: {error_msg}")
                return False, error_msg

        except Exception as e:
            logger.error(f"[ERROR] Error placing target order: {e}")
            return False, str(e)

    async def _place_paper_order(self, signal: SignalPayload) -> Tuple[bool, Union[OrderResponse, str]]:
        """Place paper trading order using virtual account"""
        try:
            if not self.virtual_account:
                return False, "Virtual account not initialized"

            start_time = datetime.now()

            # Execute paper trade
            success, message, paper_trade = await self.virtual_account.execute_trade(
                symbol=signal.symbol,
                exchange=signal.exchange,
                quantity=signal.quantity,
                price=signal.entry_price,
                transaction_type=signal.action,
                order_type=signal.order_type,
                product_type=signal.product_type,
                strategy_name=signal.strategy_name,
                signal_id=signal.signal_id
            )

            execution_time = (datetime.now() - start_time).total_seconds() * 1000

            if success and paper_trade:
                # Create order response for consistency
                order_response = OrderResponse(
                    order_id=paper_trade.trade_id,
                    status="EXECUTED",
                    message=message,
                    timestamp=paper_trade.timestamp,
                    signal_id=signal.signal_id,
                    execution_time_ms=execution_time,
                    slippage_percent=0.0  # No slippage in paper trading
                )

                logger.info(f"[SUCCESS] Paper trade executed: {paper_trade.trade_id}")
                logger.info(f"   {signal.action} {signal.quantity} {signal.symbol} @ Rs.{signal.entry_price:.2f}")
                logger.info(f"   Charges: Rs.{paper_trade.total_charges:.2f}")
                logger.info(f"   Net Amount: Rs.{paper_trade.net_amount:.2f}")

                # Get account summary for logging
                account_summary = self.virtual_account.get_account_summary()
                logger.info(f"   Account Balance: Rs.{account_summary.get('current_balance', 0):,.2f}")
                logger.info(f"   Available Margin: Rs.{account_summary.get('available_margin', 0):,.2f}")
                logger.info(f"   Trades Today: {account_summary.get('today_trades', 0)}/{account_summary.get('trades_remaining_today', 0) + account_summary.get('today_trades', 0)}")

                return True, order_response
            else:
                logger.error(f"[ERROR] Paper trade failed: {message}")
                return False, message

        except Exception as e:
            logger.error(f"[ERROR] Error placing paper order: {e}")
            return False, str(e)

    async def modify_order(self, order_id: str, new_price: float, new_quantity: int = None) -> Tuple[bool, str]:
        """Modify existing order"""
        try:
            if order_id not in self.active_orders:
                return False, "Order not found in active orders"

            trade_execution = self.active_orders[order_id]
            original_order = trade_execution.entry_order.order_request

            modify_params = {
                "variety": "NORMAL",
                "orderid": order_id,
                "ordertype": original_order.ordertype,
                "producttype": original_order.producttype,
                "duration": "DAY",
                "price": str(new_price),
                "quantity": str(new_quantity or original_order.quantity),
                "tradingsymbol": original_order.tradingsymbol,
                "symboltoken": original_order.symboltoken,
                "exchange": original_order.exchange
            }

            response = self.angel_api.modifyOrder(modify_params)

            if response.get('status'):
                logger.info(f"[SUCCESS] Order modified: {order_id} - New price: {new_price}")
                return True, "Order modified successfully"
            else:
                error_msg = response.get('message', 'Unknown error')
                logger.error(f"[ERROR] Order modification failed: {error_msg}")
                return False, error_msg

        except Exception as e:
            logger.error(f"[ERROR] Error modifying order: {e}")
            return False, str(e)

    async def cancel_order(self, order_id: str, reason: str = "Manual cancellation") -> Tuple[bool, str]:
        """Cancel existing order"""
        try:
            if order_id not in self.active_orders:
                return False, "Order not found in active orders"

            cancel_params = {
                "variety": "NORMAL",
                "orderid": order_id
            }

            response = self.angel_api.cancelOrder(cancel_params)

            if response.get('status'):
                # Update trade execution status
                trade_execution = self.active_orders[order_id]
                trade_execution.status = "CANCELLED"
                trade_execution.updated_at = datetime.now()

                # Move to history
                self.order_history.append(trade_execution)
                del self.active_orders[order_id]

                logger.info(f"[SUCCESS] Order cancelled: {order_id} - Reason: {reason}")
                await self._send_notification(f"[ERROR] Order cancelled: {order_id} - {reason}")

                return True, "Order cancelled successfully"
            else:
                error_msg = response.get('message', 'Unknown error')
                logger.error(f"[ERROR] Order cancellation failed: {error_msg}")
                return False, error_msg

        except Exception as e:
            logger.error(f"[ERROR] Error cancelling order: {e}")
            return False, str(e)

    async def get_order_status(self, order_id: str) -> Optional[Dict[str, Any]]:
        """Get current order status from broker"""
        try:
            response = self.angel_api.orderBook()

            if response.get('status'):
                orders = response.get('data', [])
                for order in orders:
                    if order.get('orderid') == order_id:
                        return order

            return None

        except Exception as e:
            logger.error(f"[ERROR] Error fetching order status: {e}")
            return None

    async def _order_monitoring_loop(self):
        """Background task to monitor active orders"""
        while True:
            try:
                if not self.active_orders:
                    await asyncio.sleep(30)  # Check every 30 seconds when no active orders
                    continue

                # Check status of all active orders
                for order_id, trade_execution in list(self.active_orders.items()):
                    order_status = await self.get_order_status(order_id)

                    if order_status:
                        status = order_status.get('orderstatus', '').upper()

                        if status == 'COMPLETE':
                            # Order filled
                            await self._handle_order_filled(order_id, trade_execution, order_status)
                        elif status in ['CANCELLED', 'REJECTED']:
                            # Order cancelled or rejected
                            await self._handle_order_failed(order_id, trade_execution, order_status)

                await asyncio.sleep(10)  # Check every 10 seconds

            except Exception as e:
                logger.error(f"[ERROR] Error in order monitoring loop: {e}")
                await asyncio.sleep(30)

    async def _handle_order_filled(self, order_id: str, trade_execution: TradeExecution, order_status: Dict[str, Any]):
        """Handle filled order"""
        try:
            fill_price = float(order_status.get('averageprice', 0))
            fill_quantity = int(order_status.get('filledshares', 0))

            # Calculate slippage
            expected_price = trade_execution.signal_payload.entry_price
            slippage_percent = abs(fill_price - expected_price) / expected_price * 100

            # Update trade execution
            trade_execution.entry_order.slippage_percent = slippage_percent
            trade_execution.status = "FILLED"
            trade_execution.updated_at = datetime.now()

            # Update statistics
            self._update_execution_stats(trade_execution.entry_order.execution_time_ms, slippage_percent)

            logger.info(f"[SUCCESS] Order filled: {order_id} @ {fill_price} (Slippage: {slippage_percent:.2f}%)")

            # Send notification
            await self._send_notification(
                f"[SUCCESS] Order filled: {trade_execution.signal_payload.symbol} @ {fill_price} "
                f"(Slippage: {slippage_percent:.2f}%)"
            )

            # Check slippage threshold
            max_slippage = self.config.get('execution', {}).get('max_slippage_percent', 0.5)
            if slippage_percent > max_slippage:
                logger.warning(f"[WARN]  High slippage detected: {slippage_percent:.2f}% > {max_slippage}%")

        except Exception as e:
            logger.error(f"[ERROR] Error handling filled order: {e}")

    async def _handle_order_failed(self, order_id: str, trade_execution: TradeExecution, order_status: Dict[str, Any]):
        """Handle failed/cancelled order"""
        try:
            status = order_status.get('orderstatus', '').upper()
            reason = order_status.get('text', 'Unknown reason')

            # Update trade execution
            trade_execution.status = status
            trade_execution.updated_at = datetime.now()

            # Move to history
            self.order_history.append(trade_execution)
            del self.active_orders[order_id]

            logger.warning(f"[WARN]  Order {status.lower()}: {order_id} - {reason}")

            # Send notification
            await self._send_notification(f"[ERROR] Order {status.lower()}: {order_id} - {reason}")

            # Add to retry queue if appropriate
            if status == 'REJECTED' and self.config.get('execution', {}).get('auto_retry', True):
                self.retry_queue.append((trade_execution.signal_payload, datetime.now()))
                logger.info(f"[WORKFLOW] Added to retry queue: {order_id}")

        except Exception as e:
            logger.error(f"[ERROR] Error handling failed order: {e}")

    async def _retry_queue_processor(self):
        """Background task to process retry queue"""
        while True:
            try:
                if not self.retry_queue:
                    await asyncio.sleep(60)  # Check every minute
                    continue

                # Process retry queue
                retry_delay_minutes = self.config.get('execution', {}).get('retry_delay_minutes', 5)
                max_retries = self.config.get('execution', {}).get('max_retries', 3)

                current_time = datetime.now()

                while self.retry_queue:
                    signal, retry_time = self.retry_queue[0]

                    # Check if enough time has passed
                    if (current_time - retry_time).total_seconds() < retry_delay_minutes * 60:
                        break

                    # Remove from queue
                    self.retry_queue.popleft()

                    # Check retry count
                    retry_count = getattr(signal, 'retry_count', 0)
                    if retry_count >= max_retries:
                        logger.warning(f"[WARN]  Max retries exceeded for signal: {signal.signal_id}")
                        continue

                    # Increment retry count
                    signal.retry_count = retry_count + 1

                    # Retry signal processing
                    logger.info(f"[WORKFLOW] Retrying signal: {signal.signal_id} (Attempt {signal.retry_count})")
                    success, message, _ = await self.process_signal(signal)

                    if not success:
                        logger.warning(f"[WARN]  Retry failed: {message}")

                await asyncio.sleep(60)  # Check every minute

            except Exception as e:
                logger.error(f"[ERROR] Error in retry queue processor: {e}")
                await asyncio.sleep(60)

    def _update_execution_stats(self, execution_time_ms: float, slippage_percent: float):
        """Update execution statistics"""
        try:
            # Update average execution time
            total_orders = self.execution_stats['successful_orders']
            current_avg_time = self.execution_stats['avg_execution_time_ms']

            new_avg_time = ((current_avg_time * (total_orders - 1)) + execution_time_ms) / total_orders
            self.execution_stats['avg_execution_time_ms'] = new_avg_time

            # Update average slippage
            current_avg_slippage = self.execution_stats['avg_slippage_percent']
            new_avg_slippage = ((current_avg_slippage * (total_orders - 1)) + slippage_percent) / total_orders
            self.execution_stats['avg_slippage_percent'] = new_avg_slippage

        except Exception as e:
            logger.error(f"[ERROR] Error updating execution stats: {e}")

    async def _send_notification(self, message: str):
        """Send notification via configured channels"""
        try:
            # Telegram notification
            if self.telegram_bot:
                chat_id = self.config['notifications']['telegram']['chat_id']
                await self.telegram_bot.send_message(chat_id=chat_id, text=message)

            # Log notification
            logger.info(f"[NOTIFY] Notification: {message}")

        except Exception as e:
            logger.error(f"[ERROR] Error sending notification: {e}")

    async def get_execution_summary(self) -> Dict[str, Any]:
        """Get execution summary and statistics"""
        try:
            return {
                'statistics': self.execution_stats.copy(),
                'active_orders_count': len(self.active_orders),
                'total_history_count': len(self.order_history),
                'retry_queue_size': len(self.retry_queue),
                'last_updated': datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"[ERROR] Error getting execution summary: {e}")
            return {}

    async def save_trade_data(self, file_path: str = None):
        """Save trade execution data to parquet file"""
        try:
            if not file_path:
                file_path = f"data/execution/trades_{datetime.now().strftime('%Y%m%d')}.parquet"

            # Prepare data for saving
            trade_records = []

            # Add active orders
            for trade_execution in self.active_orders.values():
                trade_records.append(self._trade_execution_to_dict(trade_execution))

            # Add historical orders
            for trade_execution in self.order_history:
                trade_records.append(self._trade_execution_to_dict(trade_execution))

            if trade_records:
                # Create DataFrame and save
                df = pl.DataFrame(trade_records)

                # Ensure directory exists
                os.makedirs(os.path.dirname(file_path), exist_ok=True)

                # Save to parquet
                df.write_parquet(file_path, compression="zstd", compression_level=15)

                logger.info(f"[SUCCESS] Trade data saved: {file_path} ({len(trade_records)} records)")
            else:
                logger.info("[INFO] No trade data to save")

        except Exception as e:
            logger.error(f"[ERROR] Error saving trade data: {e}")

    def _trade_execution_to_dict(self, trade_execution: TradeExecution) -> Dict[str, Any]:
        """Convert TradeExecution to dictionary for storage"""
        try:
            signal = trade_execution.signal_payload
            entry_order = trade_execution.entry_order

            return {
                'signal_id': signal.signal_id,
                'symbol': signal.symbol,
                'exchange': signal.exchange,
                'action': signal.action,
                'entry_price': signal.entry_price,
                'sl_price': signal.sl_price,
                'target_price': signal.target_price,
                'quantity': signal.quantity,
                'strategy_name': signal.strategy_name,
                'order_id': entry_order.order_id if entry_order else '',
                'status': trade_execution.status,
                'execution_time_ms': entry_order.execution_time_ms if entry_order else 0.0,
                'slippage_percent': entry_order.slippage_percent if entry_order else 0.0,
                'created_at': trade_execution.created_at.isoformat(),
                'updated_at': trade_execution.updated_at.isoformat()
            }
        except Exception as e:
            logger.error(f"[ERROR] Error converting trade execution to dict: {e}")
            return {}

    async def get_execution_stats(self) -> Dict[str, Any]:
        """Get execution statistics"""
        try:
            stats = self.execution_stats.copy()

            # Calculate success rate
            if stats['total_orders'] > 0:
                stats['success_rate'] = (stats['successful_orders'] / stats['total_orders']) * 100
            else:
                stats['success_rate'] = 0.0

            # Add active orders count
            stats['active_orders'] = len(self.active_orders)

            # Add market status
            stats['market_open'] = self._is_market_open()

            # Add trading mode specific stats
            stats['trading_mode'] = self.trading_mode

            if self.paper_trading_enabled and self.virtual_account:
                # Add paper trading stats
                account_summary = self.virtual_account.get_account_summary()
                stats.update({
                    'paper_trading': {
                        'account_balance': account_summary.get('current_balance', 0),
                        'available_margin': account_summary.get('available_margin', 0),
                        'total_pnl': account_summary.get('total_pnl', 0),
                        'return_percent': account_summary.get('return_percent', 0),
                        'total_trades': account_summary.get('total_trades', 0),
                        'active_positions': account_summary.get('active_positions', 0),
                        'today_trades': account_summary.get('today_trades', 0),
                        'trades_remaining_today': account_summary.get('trades_remaining_today', 0)
                    }
                })

            return stats

        except Exception as e:
            logger.error(f"[ERROR] Error getting execution stats: {e}")
            return {}

    async def get_account_status(self) -> Dict[str, Any]:
        """Get account status (paper or real)"""
        try:
            if self.paper_trading_enabled and self.virtual_account:
                # Return paper trading account status
                account_summary = self.virtual_account.get_account_summary()
                positions_summary = self.virtual_account.get_positions_summary()

                return {
                    'trading_mode': 'paper',
                    'account_summary': account_summary,
                    'positions': positions_summary,
                    'timestamp': datetime.now().isoformat()
                }
            elif self.angel_api:
                # Return real trading account status using SmartAPI
                # SmartAPI does not have direct equivalents for get_rms_limits, get_positions, get_funds
                # We need to call individual API endpoints and parse the response

                # Get RMS Limits (User Profile)
                rms_limits_data = self.angel_api.getProfile()
                rms_limits = {}
                if rms_limits_data and rms_limits_data.get('status') and rms_limits_data.get('data'):
                    profile_data = rms_limits_data['data']
                    rms_limits = RMSLimits(
                        available_cash=float(profile_data.get('availablecash', 0)),
                        collateral=float(profile_data.get('collateral', 0)),
                        total_limit=float(profile_data.get('overalllimit', 0)),
                        utilized_margin=float(profile_data.get('utilisedmargin', 0)),
                        span_margin=float(profile_data.get('spanmargin', 0)),
                        exposure_margin=float(profile_data.get('exposuremargin', 0)),
                        option_premium=float(profile_data.get('optionpremium', 0)),
                        equity_limit=float(profile_data.get('equitylimit', 0)),
                        commodity_limit=float(profile_data.get('commoditylimit', 0)),
                        currency_limit=float(profile_data.get('currencylimit', 0))
                    )

                # Get Positions
                positions_data = self.angel_api.holding() # This is for holdings, not open positions
                positions = []
                if positions_data and positions_data.get('status') and positions_data.get('data'):
                    for item in positions_data['data']:
                        positions.append(PositionData(
                            symbol=item.get('tradingsymbol', ''),
                            exchange=item.get('exchange', ''),
                            product_type=item.get('producttype', ''),
                            quantity=int(item.get('quantity', 0)),
                            avg_price=float(item.get('averageprice', 0)),
                            ltp=float(item.get('ltp', 0)),
                            pnl=float(item.get('pnl', 0)),
                            realized_pnl=0.0, # Not directly available
                            unrealized_pnl=float(item.get('unrealizedpnl', 0))
                        ))
                
                # Get Funds (User Profile again for some fund details)
                funds = {}
                if rms_limits_data and rms_limits_data.get('status') and rms_limits_data.get('data'):
                    profile_data = rms_limits_data['data']
                    funds = FundData(
                        available_cash=float(profile_data.get('availablecash', 0)),
                        ledger_balance=float(profile_data.get('ledgerbalance', 0)),
                        margin_used=float(profile_data.get('utilisedmargin', 0)),
                        unrealized_pnl=0.0, # Not directly available
                        realized_pnl=0.0, # Not directly available
                        collateral=float(profile_data.get('collateral', 0))
                    )

                return {
                    'trading_mode': 'real',
                    'rms_limits': rms_limits.__dict__ if isinstance(rms_limits, RMSLimits) else rms_limits,
                    'positions': [pos.__dict__ for pos in positions] if positions else [],
                    'funds': funds.__dict__ if isinstance(funds, FundData) else funds,
                    'timestamp': datetime.now().isoformat()
                }
            else:
                return {
                    'trading_mode': self.trading_mode,
                    'status': 'not_initialized',
                    'timestamp': datetime.now().isoformat()
                }

        except Exception as e:
            logger.error(f"[ERROR] Error getting account status: {e}")
            return {'error': str(e)}

    async def reset_paper_account(self) -> bool:
        """Reset paper trading account (only in paper mode)"""
        try:
            if not self.paper_trading_enabled:
                logger.warning("[WARN] Reset only available in paper trading mode")
                return False

            if not self.virtual_account:
                logger.error("[ERROR] Virtual account not initialized")
                return False

            self.virtual_account.reset_account()

            # Clear active orders and history
            self.active_orders.clear()
            self.order_history.clear()

            # Reset execution stats
            self.execution_stats = {
                'total_orders': 0,
                'successful_orders': 0,
                'failed_orders': 0,
                'avg_execution_time_ms': 0.0,
                'avg_slippage_percent': 0.0
            }

            logger.info("[SUCCESS] Paper trading account reset successfully")
            return True

        except Exception as e:
            logger.error(f"[ERROR] Error resetting paper account: {e}")
            return False

    async def _validate_margin_requirement(self, signal: SignalPayload) -> Tuple[bool, str]:
        """
        Validate margin requirement for real trading

        Args:
            signal: Signal payload to validate

        Returns:
            Tuple of (is_valid, message)
        """
        try:
            if not self.angel_api:
                return False, "SmartAPI client not initialized"

            # SmartAPI does not have a direct 'get_margin_requirement' method.
            # We need to simulate this by checking available funds and order value.
            # This is a simplified check and might not cover all broker-specific margin rules.

            # Get user profile to check available cash/margin
            profile_data = self.angel_api.getProfile()
            available_cash = 0.0
            if profile_data and profile_data.get('status') and profile_data.get('data'):
                available_cash = float(profile_data['data'].get('availablecash', 0))
            else:
                return False, "Failed to retrieve available cash from SmartAPI profile."

            # Calculate estimated order value
            order_value = signal.quantity * signal.entry_price

            # For MIS (Intraday), margin requirements are typically lower.
            # A simple heuristic: assume 5x leverage for MIS.
            # This needs to be replaced with actual margin calculation if SmartAPI provides it.
            estimated_margin_required = order_value / 5 # Simplified assumption

            is_allowed = available_cash >= estimated_margin_required

            margin_req = MarginRequirement(
                symbol=signal.symbol,
                exchange=signal.exchange,
                quantity=signal.quantity,
                price=signal.entry_price,
                transaction_type=signal.action,
                product_type=signal.product_type,
                margin_required=estimated_margin_required,
                available_margin=available_cash,
                limit_used_percent=(estimated_margin_required / available_cash * 100) if available_cash > 0 else 0,
                is_allowed=is_allowed,
                error_message=None
            )

            if not margin_req.is_allowed:
                return False, f"Insufficient margin. Required: Rs.{margin_req.margin_required:,.2f}, Available: Rs.{margin_req.available_margin:,.2f}"

            if margin_req.limit_used_percent > 80:
                logger.warning(f"[WARN]  High margin utilization: {margin_req.limit_used_percent:.1f}%")

            logger.info(f"[SUCCESS] Margin validation passed for {signal.symbol}")
            logger.info(f"   Margin Required: Rs.{margin_req.margin_required:,.2f}")
            logger.info(f"   Available Margin: Rs.{margin_req.available_margin:,.2f}")
            logger.info(f"   Utilization: {margin_req.limit_used_percent:.1f}%")

            return True, "Margin validation successful"

        except Exception as e:
            logger.error(f"[ERROR] Error validating margin requirement: {e}")
            return False, f"Margin validation error: {str(e)}"

    async def cleanup(self):
        """Cleanup resources"""
        try:
            # Save final trade data
            await self.save_trade_data()

            # Close Angel One API session
            if self.angel_api:
                await self.angel_api.close_session()

            logger.info("[SUCCESS] Execution Agent cleanup completed")

        except Exception as e:
            logger.error(f"[ERROR] Error during cleanup: {e}")

# ═══════════════════════════════════════════════════════════════════════════════
# [CONFIG] UTILITY FUNCTIONS
# ═══════════════════════════════════════════════════════════════════════════════

async def main():
    """Main function for testing"""
    try:
        # Initialize execution agent
        agent = ExecutionAgent()
        await agent.initialize()

        # Example signal - use dynamic stock selection
        try:
            from utils.stock_universe import StockUniverse
            stock_universe = StockUniverse()
            if stock_universe.load_stock_universe():
                # Get first large cap stock for testing
                large_cap_stocks = stock_universe.get_stocks_by_market_cap("Large")
                if large_cap_stocks:
                    test_stock = large_cap_stocks[0]
                    signal = SignalPayload(
                        symbol=f"{test_stock.symbol}-EQ",
                        exchange="NSE",
                        symbol_token=test_stock.token,
                        action="BUY",
                        entry_price=2780.0,
                        sl_price=2765.0,
                        target_price=2815.0,
                        quantity=1,
                        strategy_name="test_strategy",
                        signal_id="test_001"
                    )
                else:
                    logger.error("[ERROR] No stocks available for testing")
                    return
            else:
                logger.error("[ERROR] Could not load stock universe for testing")
                return
        except ImportError:
            logger.error("[ERROR] Stock universe not available for testing")
            return

        # Process signal
        success, message, trade_execution = await agent.process_signal(signal)
        print(f"Signal processing result: {success} - {message}")

        # Keep running for monitoring
        await asyncio.sleep(300)  # Run for 5 minutes

        # Cleanup
        await agent.cleanup()

    except Exception as e:
        logger.error(f"[ERROR] Error in main: {e}")

if __name__ == "__main__":
    asyncio.run(main())
