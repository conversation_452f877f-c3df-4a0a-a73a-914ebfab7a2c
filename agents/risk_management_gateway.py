#!/usr/bin/env python3
"""
Risk Management Gateway

Unified gateway for all risk management functionality. This module acts as the single
entry point for other agents to interact with the modular risk management system.

Features:
- Unified API for all risk management operations
- Automatic module initialization and coordination
- Event-driven architecture integration
- Comprehensive error handling and logging
- Performance monitoring and metrics
- Thread-safe operations
"""

import logging
import asyncio
from typing import Dict, List, Optional, Tuple, Any, Union
from datetime import datetime
from dataclasses import dataclass
import threading

# Import base agent
from agents.base_agent import BaseAgent
from utils.event_bus import EventBus, Event, EventTypes

# Import risk management core modules
try:
    from agents.risk_management.core.capital_allocator import CapitalAllocator
    from agents.risk_management.core.position_sizer import PositionSizer, PositionSizingMethod
    from agents.risk_management.core.pre_trade_filters import PreTradeFilters, ValidationSummary
    from agents.risk_management.core.circuit_breakers import CircuitBreakers, CircuitBreakerEvent
    from agents.risk_management.core.portfolio_monitor import PortfolioMonitor, VaRResult, StressTestResult
    from agents.risk_management.core.risk_calculator import RiskCalculator, PositionRiskAssessment
    from agents.risk_management.core.configuration_manager import ConfigurationManager
    MODULAR_COMPONENTS_AVAILABLE = True
except ImportError as e:
    logging.warning(f"[WARN] Modular risk management components not available: {e}")
    MODULAR_COMPONENTS_AVAILABLE = False

# Import risk models
from utils.risk_models import (
    TradeRequest, ValidationResult, RiskMetrics, Position, Portfolio,
    RiskAlert, DrawdownEvent, PerformanceSnapshot,
    TradeDirection, ProductType, OrderType, RiskLevel, ValidationStatus
)

logger = logging.getLogger(__name__)


@dataclass
class RiskManagementStatus:
    """Overall risk management system status"""
    system_operational: bool
    modules_loaded: Dict[str, bool]
    trading_allowed: bool
    emergency_stop_active: bool
    current_risk_level: RiskLevel
    active_positions: int
    portfolio_value: float
    total_risk_amount: float
    risk_utilization_percent: float
    last_update: datetime
    warnings: List[str]
    errors: List[str]


class RiskManagementGateway(BaseAgent):
    """
    Risk Management Gateway - Unified interface for all risk management operations
    
    This gateway provides a single entry point for:
    - Trade validation and approval
    - Position sizing calculations
    - Portfolio risk monitoring
    - Circuit breaker management
    - Capital allocation
    - Risk metrics calculation
    """
    
    def __init__(self, event_bus: EventBus, config: Any, session_id: str):
        """Initialize Risk Management Gateway"""
        super().__init__("RiskManagementGateway", event_bus, config, session_id)
        
        # Configuration
        self.config_dict = self._convert_config_to_dict(config)
        
        # Initialize core modules
        self.modules_initialized = False
        self.initialization_lock = threading.Lock()
        
        # Core modules (will be initialized in setup)
        self.capital_allocator: Optional[CapitalAllocator] = None
        self.position_sizer: Optional[PositionSizer] = None
        self.pre_trade_filters: Optional[PreTradeFilters] = None
        self.circuit_breakers: Optional[CircuitBreakers] = None
        self.portfolio_monitor: Optional[PortfolioMonitor] = None
        self.risk_calculator: Optional[RiskCalculator] = None
        self.config_manager: Optional[ConfigurationManager] = None
        
        # System state
        self.system_operational = False
        self.emergency_stop_active = False
        self.current_positions: Dict[str, Position] = {}
        self.pending_validations: Dict[str, ValidationResult] = {}
        
        # Performance metrics
        self.validation_count = 0
        self.approval_count = 0
        self.rejection_count = 0
        self.last_performance_update = datetime.now()
        
        logger.info("[INIT] Risk Management Gateway initialized")

    async def initialize(self) -> bool:
        """Initialize the risk management gateway"""
        self.log_info("Initializing Risk Management Gateway...")
        success = await self.setup()
        if success:
            self.initialized = True
            self.log_info("Risk Management Gateway initialized successfully.")
        else:
            self.log_error("Failed to initialize Risk Management Gateway.")
        return success

    async def start(self):
        """Start the risk management gateway"""
        self.log_info("Starting Risk Management Gateway...")
        self.running = True
        # No specific long-running tasks for the gateway itself,
        # its modules handle continuous monitoring.
        self.log_info("Risk Management Gateway started.")

    async def stop(self):
        """Stop the risk management gateway"""
        self.log_info("Stopping Risk Management Gateway...")
        self.running = False
        self.system_operational = False
        # Disconnect from event bus if necessary, or other cleanup
        self.log_info("Risk Management Gateway stopped.")
    
    async def setup(self):
        """Setup the risk management gateway and all modules"""
        try:
            if not MODULAR_COMPONENTS_AVAILABLE:
                logger.error("[ERROR] Modular risk management components not available")
                return False
            
            with self.initialization_lock:
                if self.modules_initialized:
                    return True
                
                logger.info("[SETUP] Initializing risk management modules...")
                
                # Initialize configuration manager first
                config_path = getattr(self.config, 'config_path', 'config/risk_management_config.yaml')
                self.config_manager = ConfigurationManager(config_path, auto_reload=True)
                
                # Get updated configuration
                self.config_dict = self.config_manager.get_configuration()
                
                # Initialize core modules
                self.capital_allocator = CapitalAllocator(self.config_dict)
                self.position_sizer = PositionSizer(self.config_dict)
                self.pre_trade_filters = PreTradeFilters(self.config_dict)
                self.circuit_breakers = CircuitBreakers(self.config_dict)
                self.portfolio_monitor = PortfolioMonitor(self.config_dict)
                self.risk_calculator = RiskCalculator(self.config_dict)
                
                # Register circuit breaker callbacks
                self._setup_circuit_breaker_callbacks()
                
                # Register configuration change callbacks
                self._setup_configuration_callbacks()
                
                # Subscribe to relevant events
                await self._setup_event_subscriptions()
                
                self.modules_initialized = True
                self.system_operational = True
                
                logger.info("[SUCCESS] Risk management gateway setup completed")
                return True
                
        except Exception as e:
            logger.error(f"[ERROR] Risk management gateway setup failed: {e}")
            self.system_operational = False
            return False
    
    async def validate_trade_request(
        self, 
        trade_request: TradeRequest,
        market_data: Optional[Dict[str, Any]] = None
    ) -> ValidationResult:
        """
        Comprehensive trade validation using all risk management modules
        
        Args:
            trade_request: Trade request to validate
            market_data: Real-time market data
            
        Returns:
            ValidationResult with approval/rejection decision
        """
        try:
            if not self.system_operational:
                return self._create_rejection_result(
                    trade_request, "Risk management system not operational", RiskLevel.CRITICAL
                )
            
            self.validation_count += 1
            validation_start = datetime.now()
            
            # Check if trading is allowed (circuit breakers)
            trading_allowed, reason = self.circuit_breakers.is_trading_allowed()
            if not trading_allowed:
                self.rejection_count += 1
                return self._create_rejection_result(
                    trade_request, f"Trading not allowed: {reason}", RiskLevel.HIGH
                )
            
            # Step 1: Pre-trade filters validation
            filter_validation = self.pre_trade_filters.validate_trade(
                trade_request, self.current_positions, market_data
            )
            
            if not filter_validation.overall_passed:
                self.rejection_count += 1
                return self._create_rejection_result(
                    trade_request, 
                    f"Pre-trade filters failed: {'; '.join(filter_validation.errors + filter_validation.critical_issues)}",
                    RiskLevel.HIGH
                )
            
            # Step 2: Position sizing calculation
            available_capital = self.capital_allocator.get_available_capital()
            position_size_result = self.position_sizer.calculate_position_size(
                trade_request, available_capital, market_data=market_data
            )
            
            if position_size_result.quantity <= 0:
                self.rejection_count += 1
                return self._create_rejection_result(
                    trade_request, "Invalid position size calculated", RiskLevel.MEDIUM
                )
            
            # Update trade request with calculated position size
            trade_request.quantity = position_size_result.quantity
            
            # Step 3: Capital allocation
            allocation_success, allocated_amount, allocation_reason = self.capital_allocator.allocate_capital(
                trade_request
            )
            
            if not allocation_success:
                self.rejection_count += 1
                return self._create_rejection_result(
                    trade_request, f"Capital allocation failed: {allocation_reason}", RiskLevel.MEDIUM
                )
            
            # Step 4: Risk assessment
            risk_assessment = self.risk_calculator.calculate_position_risk(
                trade_request, market_data
            )
            
            if risk_assessment.risk_level == RiskLevel.CRITICAL:
                # Release allocated capital
                allocation_id = f"{trade_request.symbol}_{trade_request.signal_id}"
                self.capital_allocator.release_capital(allocation_id)
                self.rejection_count += 1
                return self._create_rejection_result(
                    trade_request, "Critical risk level detected", RiskLevel.CRITICAL
                )
            
            # Step 5: Portfolio impact analysis
            portfolio_risk = self.risk_calculator.calculate_portfolio_risk(self.current_positions)
            
            # Step 6: Circuit breaker check with updated metrics
            portfolio_metrics = {
                'daily_pnl': getattr(self, 'daily_pnl', 0.0),
                'current_drawdown': portfolio_risk.risk_percent,
                'portfolio_heat': portfolio_risk.risk_percent
            }
            
            triggered_breakers = self.circuit_breakers.check_circuit_breakers(
                portfolio_metrics, market_data
            )
            
            if triggered_breakers:
                # Release allocated capital
                allocation_id = f"{trade_request.symbol}_{trade_request.signal_id}"
                self.capital_allocator.release_capital(allocation_id)
                self.rejection_count += 1
                return self._create_rejection_result(
                    trade_request, 
                    f"Circuit breakers triggered: {[e.message for e in triggered_breakers]}",
                    RiskLevel.HIGH
                )
            
            # All validations passed - create approval result
            self.approval_count += 1
            validation_time = (datetime.now() - validation_start).total_seconds() * 1000
            
            # Publish approval event
            await self.event_bus.publish(
                EventTypes.SIGNAL_RISK_APPROVED,
                {
                    "trade_request": trade_request,
                    "position_size_result": position_size_result,
                    "risk_assessment": risk_assessment,
                    "allocated_capital": allocated_amount,
                    "validation_time_ms": validation_time
                },
                source=self.name
            )
            
            return ValidationResult(
                trade_request=trade_request,
                is_valid=True,
                validation_status=ValidationStatus.APPROVED,
                risk_level=risk_assessment.risk_level,
                position_size=position_size_result.quantity,
                capital_allocated=allocated_amount,
                risk_amount=risk_assessment.risk_amount,
                risk_reward_ratio=risk_assessment.risk_reward_ratio,
                confidence=position_size_result.confidence,
                validation_details={
                    'filter_validation': filter_validation,
                    'position_sizing': position_size_result,
                    'risk_assessment': risk_assessment,
                    'portfolio_risk': portfolio_risk,
                    'validation_time_ms': validation_time
                },
                warnings=filter_validation.warnings + risk_assessment.recommendations,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"[ERROR] Trade validation error: {e}")
            self.rejection_count += 1
            return self._create_rejection_result(
                trade_request, f"Validation system error: {str(e)}", RiskLevel.CRITICAL
            )
    
    async def update_position(self, position_id: str, position: Position):
        """Update position in all relevant modules"""
        try:
            self.current_positions[position_id] = position
            
            # Update in all modules
            self.pre_trade_filters.update_position(position_id, {
                'symbol': position.symbol,
                'value': position.quantity * position.current_price
            })
            
            self.portfolio_monitor.add_position(position_id, position)
            self.risk_calculator.update_positions(self.current_positions)
            
            logger.debug(f"[UPDATE] Position {position_id} updated across all modules")
            
        except Exception as e:
            logger.error(f"[ERROR] Error updating position: {e}")
    
    async def remove_position(self, position_id: str):
        """Remove position from all modules"""
        try:
            if position_id in self.current_positions:
                # Release allocated capital
                self.capital_allocator.release_capital(position_id)
                
                # Remove from tracking
                del self.current_positions[position_id]
                self.pre_trade_filters.remove_position(position_id)
                self.portfolio_monitor.remove_position(position_id)
                self.risk_calculator.update_positions(self.current_positions)
                
                logger.debug(f"[REMOVE] Position {position_id} removed from all modules")
                
        except Exception as e:
            logger.error(f"[ERROR] Error removing position: {e}")
    
    def get_portfolio_metrics(self) -> RiskMetrics:
        """Get comprehensive portfolio risk metrics"""
        try:
            if not self.system_operational:
                return self._create_empty_risk_metrics()
            
            # Get metrics from various modules
            capital_summary = self.capital_allocator.get_allocation_summary()
            portfolio_summary = self.portfolio_monitor.get_portfolio_summary()
            portfolio_risk = self.risk_calculator.calculate_portfolio_risk(self.current_positions)
            
            return RiskMetrics(
                total_capital=capital_summary['total_capital'],
                available_capital=capital_summary['available_capital'],
                utilized_capital=capital_summary['allocated_capital'],
                capital_utilization_percent=capital_summary['utilization_percent'],
                total_risk_amount=portfolio_risk.risk_amount,
                daily_risk_amount=portfolio_risk.risk_amount,  # Simplified
                portfolio_risk_percent=portfolio_risk.risk_percent * 100,
                daily_risk_percent=portfolio_risk.risk_percent * 100,
                total_positions=len(self.current_positions),
                long_positions=sum(1 for p in self.current_positions.values() if p.quantity > 0),
                short_positions=sum(1 for p in self.current_positions.values() if p.quantity < 0),
                max_position_size=max([p.quantity * p.current_price for p in self.current_positions.values()], default=0),
                avg_position_size=sum([p.quantity * p.current_price for p in self.current_positions.values()]) / len(self.current_positions) if self.current_positions else 0,
                total_pnl=portfolio_summary.get('total_pnl', 0),
                realized_pnl=0,  # Would need trade history
                unrealized_pnl=portfolio_summary.get('total_pnl', 0),
                daily_pnl=portfolio_summary.get('daily_pnl', 0),
                max_drawdown=portfolio_summary.get('max_drawdown', 0),
                current_drawdown=portfolio_summary.get('current_drawdown', 0),
                total_margin_required=sum(capital_summary['margin_requirements'].values(), 0) if 'margin_requirements' in capital_summary else 0,
                available_margin=capital_summary['available_capital'],
                margin_utilization_percent=capital_summary['utilization_percent'],
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"[ERROR] Error getting portfolio metrics: {e}")
            return self._create_empty_risk_metrics()
    
    def get_system_status(self) -> RiskManagementStatus:
        """Get comprehensive system status"""
        try:
            # Check module status
            modules_status = {
                'capital_allocator': self.capital_allocator is not None,
                'position_sizer': self.position_sizer is not None,
                'pre_trade_filters': self.pre_trade_filters is not None,
                'circuit_breakers': self.circuit_breakers is not None,
                'portfolio_monitor': self.portfolio_monitor is not None,
                'risk_calculator': self.risk_calculator is not None,
                'config_manager': self.config_manager is not None
            }
            
            # Get current risk level
            portfolio_risk = self.risk_calculator.calculate_portfolio_risk() if self.risk_calculator else None
            current_risk_level = portfolio_risk.risk_level if portfolio_risk else RiskLevel.LOW
            
            # Get trading status
            trading_allowed = True
            warnings = []
            errors = []
            
            if self.circuit_breakers:
                trading_allowed, reason = self.circuit_breakers.is_trading_allowed()
                if not trading_allowed:
                    warnings.append(f"Trading restricted: {reason}")
            
            # Calculate portfolio metrics
            portfolio_value = sum(p.quantity * p.current_price for p in self.current_positions.values())
            total_risk = portfolio_risk.risk_amount if portfolio_risk else 0
            risk_utilization = (total_risk / portfolio_value * 100) if portfolio_value > 0 else 0
            
            return RiskManagementStatus(
                system_operational=self.system_operational,
                modules_loaded=modules_status,
                trading_allowed=trading_allowed,
                emergency_stop_active=self.emergency_stop_active,
                current_risk_level=current_risk_level,
                active_positions=len(self.current_positions),
                portfolio_value=portfolio_value,
                total_risk_amount=total_risk,
                risk_utilization_percent=risk_utilization,
                last_update=datetime.now(),
                warnings=warnings,
                errors=errors
            )
            
        except Exception as e:
            logger.error(f"[ERROR] Error getting system status: {e}")
            return RiskManagementStatus(
                False, {}, False, True, RiskLevel.CRITICAL, 0, 0, 0, 0,
                datetime.now(), [], [f"Status error: {str(e)}"]
            )

    def trigger_emergency_stop(self, reason: str = "Manual emergency stop"):
        """Trigger emergency stop across all modules"""
        try:
            self.emergency_stop_active = True

            if self.circuit_breakers:
                self.circuit_breakers.trigger_emergency_stop(reason)

            logger.critical(f"[EMERGENCY] Emergency stop triggered: {reason}")

            # Publish emergency stop event
            asyncio.create_task(self.event_bus.publish(
                EventTypes.EMERGENCY_STOP,
                {"reason": reason, "timestamp": datetime.now()},
                source=self.name
            ))

        except Exception as e:
            logger.error(f"[ERROR] Error triggering emergency stop: {e}")

    def reset_emergency_stop(self):
        """Reset emergency stop state"""
        try:
            self.emergency_stop_active = False

            if self.circuit_breakers:
                from agents.risk_management.core.circuit_breakers import CircuitBreakerType
                self.circuit_breakers.reset_circuit_breaker(CircuitBreakerType.EMERGENCY_STOP)

            logger.info("[RESET] Emergency stop reset")

        except Exception as e:
            logger.error(f"[ERROR] Error resetting emergency stop: {e}")

    # Event handling methods
    async def _setup_event_subscriptions(self):
        """Setup event subscriptions"""
        try:
            # Subscribe to trading signals for validation
            await self.event_bus.subscribe(
                EventTypes.SIGNAL_GENERATED,
                self._handle_trading_signal
            )

            # Subscribe to position updates
            await self.event_bus.subscribe(
                EventTypes.POSITION_OPENED,
                self._handle_position_opened
            )

            await self.event_bus.subscribe(
                EventTypes.POSITION_CLOSED,
                self._handle_position_closed
            )

            # Subscribe to market data updates
            await self.event_bus.subscribe(
                EventTypes.MARKET_DATA_UPDATE,
                self._handle_market_data_update
            )

            logger.info("[EVENTS] Event subscriptions setup completed")

        except Exception as e:
            logger.error(f"[ERROR] Error setting up event subscriptions: {e}")

    async def _handle_trading_signal(self, event: Event):
        """Handle incoming trading signal for validation"""
        try:
            signal_data = event.data

            # Convert signal to trade request
            trade_request = self._convert_signal_to_trade_request(signal_data)

            # Validate trade request
            validation_result = await self.validate_trade_request(trade_request)

            # Publish validation result
            if validation_result.is_valid:
                await self.event_bus.publish(
                    EventTypes.SIGNAL_RISK_APPROVED,
                    {
                        "signal": signal_data,
                        "validation_result": validation_result,
                        "trade_request": trade_request
                    },
                    source=self.name
                )
            else:
                await self.event_bus.publish(
                    EventTypes.SIGNAL_RISK_REJECTED,
                    {
                        "signal": signal_data,
                        "validation_result": validation_result,
                        "rejection_reason": validation_result.rejection_reason
                    },
                    source=self.name
                )

        except Exception as e:
            logger.error(f"[ERROR] Error handling trading signal: {e}")

    async def _handle_position_opened(self, event: Event):
        """Handle position opened event"""
        try:
            position_data = event.data
            position_id = position_data.get('position_id')

            if position_id:
                # Create Position object
                position = Position(
                    symbol=position_data.get('symbol'),
                    quantity=position_data.get('quantity', 0),
                    entry_price=position_data.get('entry_price', 0),
                    current_price=position_data.get('current_price', position_data.get('entry_price', 0)),
                    direction=TradeDirection.LONG if position_data.get('quantity', 0) > 0 else TradeDirection.SHORT,
                    product_type=ProductType.MIS,  # Default
                    timestamp=datetime.now()
                )

                await self.update_position(position_id, position)

        except Exception as e:
            logger.error(f"[ERROR] Error handling position opened: {e}")

    async def _handle_position_closed(self, event: Event):
        """Handle position closed event"""
        try:
            position_data = event.data
            position_id = position_data.get('position_id')

            if position_id:
                await self.remove_position(position_id)

        except Exception as e:
            logger.error(f"[ERROR] Error handling position closed: {e}")

    async def _handle_market_data_update(self, event: Event):
        """Handle market data update"""
        try:
            market_data = event.data

            # Update current prices in positions
            for position_id, position in self.current_positions.items():
                if position.symbol in market_data:
                    symbol_data = market_data[position.symbol]
                    if 'price' in symbol_data:
                        position.current_price = symbol_data['price']

            # Check circuit breakers with updated market data
            if self.circuit_breakers:
                portfolio_metrics = {
                    'daily_pnl': getattr(self, 'daily_pnl', 0.0),
                    'current_drawdown': 0.0,  # Would calculate from positions
                    'portfolio_heat': 0.0     # Would calculate from positions
                }

                triggered_breakers = self.circuit_breakers.check_circuit_breakers(
                    portfolio_metrics, market_data
                )

                if triggered_breakers:
                    for breaker_event in triggered_breakers:
                        await self.event_bus.publish(
                            EventTypes.CIRCUIT_BREAKER_TRIGGERED,
                            {"breaker_event": breaker_event},
                            source=self.name
                        )

        except Exception as e:
            logger.error(f"[ERROR] Error handling market data update: {e}")

    # Setup helper methods
    def _setup_circuit_breaker_callbacks(self):
        """Setup circuit breaker action callbacks"""
        try:
            if not self.circuit_breakers:
                return

            from agents.risk_management.core.circuit_breakers import CircuitBreakerAction

            # Register callbacks for different actions
            self.circuit_breakers.register_action_callback(
                CircuitBreakerAction.STOP_NEW_TRADES,
                self._handle_stop_new_trades
            )

            self.circuit_breakers.register_action_callback(
                CircuitBreakerAction.CLOSE_LOSING_POSITIONS,
                self._handle_close_losing_positions
            )

            self.circuit_breakers.register_action_callback(
                CircuitBreakerAction.SYSTEM_SHUTDOWN,
                self._handle_system_shutdown
            )

            logger.info("[CALLBACKS] Circuit breaker callbacks setup completed")

        except Exception as e:
            logger.error(f"[ERROR] Error setting up circuit breaker callbacks: {e}")

    def _setup_configuration_callbacks(self):
        """Setup configuration change callbacks"""
        try:
            if not self.config_manager:
                return

            # Register callbacks for configuration changes
            self.config_manager.register_change_callback(
                'capital_management',
                self._handle_capital_config_change
            )

            self.config_manager.register_change_callback(
                'circuit_breakers',
                self._handle_circuit_breaker_config_change
            )

            logger.info("[CALLBACKS] Configuration callbacks setup completed")

        except Exception as e:
            logger.error(f"[ERROR] Error setting up configuration callbacks: {e}")

    # Callback handlers
    def _handle_stop_new_trades(self, breaker_event):
        """Handle stop new trades action"""
        logger.warning(f"[ACTION] Stopping new trades: {breaker_event.message}")
        # Additional logic to stop new trades

    def _handle_close_losing_positions(self, breaker_event):
        """Handle close losing positions action"""
        logger.warning(f"[ACTION] Closing losing positions: {breaker_event.message}")
        # Logic to identify and close losing positions
        asyncio.create_task(self._close_losing_positions())

    def _handle_system_shutdown(self, breaker_event):
        """Handle system shutdown action"""
        logger.critical(f"[ACTION] System shutdown: {breaker_event.message}")
        self.emergency_stop_active = True
        self.system_operational = False

    def _handle_capital_config_change(self, section: str, config: Dict[str, Any]):
        """Handle capital management configuration changes"""
        try:
            if self.capital_allocator:
                # Reinitialize capital allocator with new config
                self.capital_allocator = CapitalAllocator(self.config_manager.get_configuration())
                logger.info("[CONFIG] Capital allocator updated with new configuration")

        except Exception as e:
            logger.error(f"[ERROR] Error handling capital config change: {e}")

    def _handle_circuit_breaker_config_change(self, section: str, config: Dict[str, Any]):
        """Handle circuit breaker configuration changes"""
        try:
            if self.circuit_breakers:
                # Reinitialize circuit breakers with new config
                self.circuit_breakers = CircuitBreakers(self.config_manager.get_configuration())
                self._setup_circuit_breaker_callbacks()
                logger.info("[CONFIG] Circuit breakers updated with new configuration")

        except Exception as e:
            logger.error(f"[ERROR] Error handling circuit breaker config change: {e}")

    async def _close_losing_positions(self):
        """Close positions that are currently losing money"""
        try:
            for position_id, position in self.current_positions.items():
                current_pnl = (position.current_price - position.entry_price) * position.quantity
                if position.direction == TradeDirection.SHORT:
                    current_pnl = -current_pnl

                if current_pnl < 0:  # Losing position
                    await self.event_bus.publish(
                        EventTypes.CLOSE_POSITION_REQUEST,
                        {
                            "position_id": position_id,
                            "reason": "Circuit breaker - close losing positions"
                        },
                        source=self.name
                    )

        except Exception as e:
            logger.error(f"[ERROR] Error closing losing positions: {e}")

    # Utility methods
    def _convert_config_to_dict(self, config: Any) -> Dict[str, Any]:
        """Convert config object to dictionary"""
        try:
            if isinstance(config, dict):
                return config
            elif hasattr(config, '__dict__'):
                return vars(config)
            else:
                # Try to extract common config attributes
                config_dict = {}
                for attr in ['capital_management', 'position_sizing', 'pre_trade_filters',
                           'circuit_breakers', 'portfolio_monitor', 'risk_calculator']:
                    if hasattr(config, attr):
                        config_dict[attr] = getattr(config, attr)
                return config_dict

        except Exception as e:
            logger.error(f"[ERROR] Error converting config to dict: {e}")
            return {}

    def _convert_signal_to_trade_request(self, signal_data: Dict[str, Any]) -> TradeRequest:
        """Convert signal data to TradeRequest"""
        try:
            return TradeRequest(
                signal_id=signal_data.get('signal_id', ''),
                symbol=signal_data.get('symbol', ''),
                exchange=signal_data.get('exchange', 'NSE'),
                strategy_name=signal_data.get('strategy_name', ''),
                direction=TradeDirection.LONG if signal_data.get('signal_type', 1) > 0 else TradeDirection.SHORT,
                entry_price=signal_data.get('entry_price', 0),
                stop_loss=signal_data.get('stop_loss', 0),
                take_profit=signal_data.get('take_profit', 0),
                quantity=signal_data.get('quantity', 0),
                product_type=ProductType.MIS,
                order_type=OrderType.LIMIT,
                risk_amount=signal_data.get('risk_amount', 0),
                capital_allocated=signal_data.get('capital_allocated', 0),
                risk_reward_ratio=signal_data.get('risk_reward_ratio', 0),
                market_regime=signal_data.get('market_regime', 'neutral'),
                confidence=signal_data.get('confidence', 0.5),
                timestamp=datetime.now(),
                context=signal_data.get('context', {})
            )

        except Exception as e:
            logger.error(f"[ERROR] Error converting signal to trade request: {e}")
            return TradeRequest('', '', 'NSE', '', TradeDirection.LONG, 0, 0, 0, 0,
                              ProductType.MIS, OrderType.LIMIT, 0, 0, 0, 'neutral', 0.5, datetime.now(), {})

    def _create_rejection_result(
        self,
        trade_request: TradeRequest,
        reason: str,
        risk_level: RiskLevel
    ) -> ValidationResult:
        """Create rejection validation result"""
        return ValidationResult(
            trade_request=trade_request,
            is_valid=False,
            validation_status=ValidationStatus.REJECTED,
            risk_level=risk_level,
            rejection_reason=reason,
            timestamp=datetime.now()
        )

    def _create_empty_risk_metrics(self) -> RiskMetrics:
        """Create empty risk metrics for error cases"""
        return RiskMetrics(
            total_capital=0, available_capital=0, utilized_capital=0, capital_utilization_percent=0,
            total_risk_amount=0, daily_risk_amount=0, portfolio_risk_percent=0, daily_risk_percent=0,
            total_positions=0, long_positions=0, short_positions=0, max_position_size=0, avg_position_size=0,
            total_pnl=0, realized_pnl=0, unrealized_pnl=0, daily_pnl=0, max_drawdown=0, current_drawdown=0,
            total_margin_required=0, available_margin=0, margin_utilization_percent=0, timestamp=datetime.now()
        )

    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get gateway performance metrics"""
        return {
            'validation_count': self.validation_count,
            'approval_count': self.approval_count,
            'rejection_count': self.rejection_count,
            'approval_rate': (self.approval_count / self.validation_count * 100) if self.validation_count > 0 else 0,
            'system_operational': self.system_operational,
            'modules_initialized': self.modules_initialized,
            'active_positions': len(self.current_positions),
            'last_update': self.last_performance_update.isoformat()
        }
