# ═══════════════════════════════════════════════════════════════════════════════
# 🧠 ENHANCED STRATEGY GENERATION AGENT CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════
# Advanced configuration for regime-aware, event-driven, and time-based strategies

# ═══════════════════════════════════════════════════════════════════════════════
# ⚙️ GENERAL CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════
general:
  max_strategies_per_symbol: 10
  min_confidence_threshold: 0.7
  max_risk_per_strategy: 0.02  # 2% of capital
  enable_synthetic_creation: true
  enable_ensemble_scoring: true
  enable_regime_filtering: true
  enable_event_driven_strategies: true
  enable_time_based_routing: true

# ═══════════════════════════════════════════════════════════════════════════════
# 🌊 REGIME DETECTION CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════
regime_detection:
  lookback_periods: 20
  volatility_threshold: 0.02  # 2% for high volatility
  trend_threshold: 0.01       # 1% for trend detection
  regime_confidence_threshold: 0.6
  
  # Market regime parameters
  market_regimes:
    bullish:
      trend_threshold: 0.01
      volume_multiplier: 1.2
      rsi_range: [45, 80]
    
    bearish:
      trend_threshold: -0.01
      volume_multiplier: 1.2
      rsi_range: [20, 55]
    
    sideways:
      trend_threshold: 0.005
      volatility_max: 0.015
      rsi_range: [30, 70]
    
    volatile:
      volatility_threshold: 0.025
      atr_multiplier: 1.5
    
    calm:
      volatility_threshold: 0.01
      atr_multiplier: 0.7

# ═══════════════════════════════════════════════════════════════════════════════
# 📊 VOLATILITY REGIME DETECTION
# ═══════════════════════════════════════════════════════════════════════════════
volatility_detection:
  iv_percentile_lookback: 252  # 1 year
  
  volatility_regimes:
    low_IV:
      iv_percentile_max: 30
      atr_multiplier_max: 0.8
    
    normal_IV:
      iv_percentile_range: [30, 70]
      atr_multiplier_range: [0.8, 1.2]
    
    high_IV:
      iv_percentile_min: 70
      atr_multiplier_min: 1.2
    
    IV_expansion:
      iv_change_threshold: 0.2  # 20% increase
      lookback_days: 5
    
    IV_crush:
      iv_change_threshold: -0.2  # 20% decrease
      lookback_days: 3

# ═══════════════════════════════════════════════════════════════════════════════
# 📅 EVENT DETECTION CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════
event_detection:
  gap_thresholds:
    gap_up_min: 0.01    # 1% minimum gap up
    gap_down_min: -0.01 # 1% minimum gap down
    significant_gap: 0.02  # 2% for significant gaps
  
  expiry_detection:
    weekly_expiry_day: 4  # Thursday (0=Monday)
    monthly_expiry_calculation: "last_thursday"
    expiry_effect_days: 3  # Days before expiry to activate strategies
  
  special_events:
    rbi_policy_dates: []  # To be populated
    budget_dates: []      # To be populated
    earnings_dates: {}    # Symbol-specific earnings dates

# ═══════════════════════════════════════════════════════════════════════════════
# ⏰ TIME-BASED ROUTING CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════
time_filtering:
  enable_time_windows: true
  market_hours_only: true
  exclude_first_last_minutes: 5
  
  time_windows:
    pre_market:
      start: "09:00"
      end: "09:15"
      strategies: ["gap_detection", "pre_market_momentum"]
    
    opening_range:
      start: "09:15"
      end: "10:15"
      strategies: ["breakout", "opening_range_breakout", "gap_fill"]
    
    morning_session:
      start: "10:15"
      end: "11:30"
      strategies: ["momentum", "trend_following", "breakout"]
    
    midday_lull:
      start: "11:30"
      end: "13:30"
      strategies: ["mean_reversion", "range_trading", "scalping"]
    
    afternoon_session:
      start: "13:30"
      end: "14:30"
      strategies: ["momentum", "trend_continuation"]
    
    power_hour:
      start: "14:30"
      end: "15:15"
      strategies: ["momentum", "breakout", "closing_strategies"]
    
    closing:
      start: "15:15"
      end: "15:30"
      strategies: ["closing_strategies", "eod_reversion"]

# ═══════════════════════════════════════════════════════════════════════════════
# ⚠️ RISK MANAGEMENT CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════
risk_management:
  max_drawdown_threshold: 0.1     # 10% maximum drawdown
  min_sharpe_ratio: 1.0           # Minimum Sharpe ratio
  performance_lookback_days: 30   # Days to look back for performance
  auto_disable_poor_performers: true
  min_signals_for_evaluation: 10  # Minimum signals before evaluation
  
  risk_scoring:
    confidence_weight: 0.3
    performance_weight: 0.4
    regime_match_weight: 0.2
    time_match_weight: 0.1
  
  position_sizing:
    default_method: "fixed_fraction"
    max_position_size: 0.05  # 5% of capital
    kelly_fraction_max: 0.25 # Maximum Kelly fraction
    volatility_scaling: true

# ═══════════════════════════════════════════════════════════════════════════════
# 🎯 ENSEMBLE SCORING CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════
ensemble_scoring:
  min_strategies_for_ensemble: 2
  max_strategies_for_ensemble: 5
  confidence_weight: 0.4
  performance_weight: 0.6
  
  voting_methods:
    - "weighted_average"
    - "majority_vote"
    - "confidence_threshold"
  
  conflict_resolution:
    method: "highest_confidence"
    min_confidence_difference: 0.1

# ═══════════════════════════════════════════════════════════════════════════════
# 🔄 SYNTHETIC STRATEGY CREATION
# ═══════════════════════════════════════════════════════════════════════════════
synthetic_creation:
  enable_auto_creation: true
  creation_frequency_days: 7
  min_base_strategies: 2
  max_base_strategies: 3
  
  combination_methods:
    - "condition_union"      # OR logic
    - "condition_intersection" # AND logic
    - "weighted_combination"
  
  testing_requirements:
    min_backtest_days: 30
    min_sharpe_ratio: 1.2
    max_drawdown: 0.05
    min_win_rate: 0.55

# ═══════════════════════════════════════════════════════════════════════════════
# 📈 PERFORMANCE TRACKING
# ═══════════════════════════════════════════════════════════════════════════════
performance_tracking:
  enable_real_time_tracking: true
  update_frequency_minutes: 15
  
  metrics_to_track:
    - "win_rate"
    - "profit_factor"
    - "sharpe_ratio"
    - "max_drawdown"
    - "avg_return"
    - "total_signals"
    - "successful_signals"
  
  time_windows_for_analysis:
    - "last_7_days"
    - "last_30_days"
    - "last_90_days"
    - "inception_to_date"

# ═══════════════════════════════════════════════════════════════════════════════
# 💾 STORAGE CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════
storage:
  strategies_metadata_path: "agents/config/enhanced_strategies_metadata.yaml"
  performance_data_path: "data/strategy_performance"
  signal_history_path: "data/signal_history"
  
  backup_settings:
    enable_backup: true
    backup_frequency_hours: 6
    max_backup_files: 10
  
  file_formats:
    metadata: "yaml"
    performance: "parquet"
    signals: "parquet"

# ═══════════════════════════════════════════════════════════════════════════════
# 🔧 ADVANCED FEATURES
# ═══════════════════════════════════════════════════════════════════════════════
advanced_features:
  enable_ml_confidence_scoring: true
  enable_regime_prediction: true
  enable_adaptive_thresholds: true
  enable_strategy_evolution: true
  
  ml_models:
    regime_classifier: "random_forest"
    confidence_scorer: "gradient_boosting"
    performance_predictor: "xgboost"
  
  adaptation_settings:
    adaptation_frequency_days: 7
    min_data_points: 100
    adaptation_rate: 0.1

# ═══════════════════════════════════════════════════════════════════════════════
# 🚨 ALERTS AND NOTIFICATIONS
# ═══════════════════════════════════════════════════════════════════════════════
notifications:
  enable_telegram_alerts: false
  enable_email_alerts: false
  enable_webhook_alerts: false
  
  alert_conditions:
    new_strategy_created: true
    strategy_disabled: true
    high_confidence_signal: true
    regime_change_detected: true
    performance_degradation: true
  
  telegram_config:
    bot_token: ""
    chat_id: ""
  
  webhook_config:
    url: ""
    headers: {}

# ═══════════════════════════════════════════════════════════════════════════════
# 🧪 TESTING AND VALIDATION
# ═══════════════════════════════════════════════════════════════════════════════
testing:
  enable_paper_trading: true
  enable_backtesting: true
  
  validation_requirements:
    min_backtest_period_days: 90
    min_out_of_sample_days: 30
    required_metrics:
      - "sharpe_ratio > 1.0"
      - "max_drawdown < 0.1"
      - "win_rate > 0.5"
  
  paper_trading:
    initial_capital: 100000
    max_positions: 5
    commission_per_trade: 20
