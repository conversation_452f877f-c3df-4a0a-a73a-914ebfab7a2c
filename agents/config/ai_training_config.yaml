# Configuration for Enhanced AI Training Agent
data_dir: "data/backtest"
input_pattern: "backtest_*.parquet"
models_dir: "data/models/enhanced"
registry_dir: "data/models/registry"

prediction_tasks:
  roi_prediction:
    type: "regression"
    target_column: "roi"
    weight: 1.0
    metrics: ["r2_score", "rmse", "mae"]

enabled_models:
  - lightgbm
  - xgboost
  - catboost
  - tabnet
  - mlp
  - sgd

ensemble_method: "stacking"
hyperopt_all_models: false
optuna_trials: 10
optuna_timeout: 300
use_gpu: false
