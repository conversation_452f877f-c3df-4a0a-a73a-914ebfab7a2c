#!/usr/bin/env python3
"""
Enhanced Market Monitoring Agent - Gateway and Runner
Production-ready gateway with comprehensive error handling, monitoring, and optimization
"""

import os
import sys
import asyncio
import logging
import signal
import argparse
from datetime import datetime
from typing import Optional, Dict, List, Any, Callable
import warnings
warnings.filterwarnings('ignore')

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import all modular components
from market_monitoring.data_structures import (
    MarketTick, OHLCV, MarketIndicators, MarketRegime, TradingSignal, MarketMonitoringConfig
)
from market_monitoring.config_manager import ConfigManager
from market_monitoring.data_downloader import DataDownloader
from market_monitoring.websocket_manager import WebSocketManager
from market_monitoring.data_processor import DataProcessor
from market_monitoring.indicator_calculator import IndicatorCalculator
from market_monitoring.regime_detector import RegimeDetector
from market_monitoring.signal_generator import SignalGenerator
from market_monitoring.ai_integration import AIIntegration
from market_monitoring.notification_manager import NotificationManager
from market_monitoring.performance_monitor import PerformanceMonitor

# Import enhanced modules
from market_monitoring.performance_optimizer import PerformanceOptimizer
from market_monitoring.resource_manager import ResourceManager
from market_monitoring.health_monitor import HealthMonitor
from market_monitoring.graceful_degradation import GracefulDegradationManager
from market_monitoring.strategy_manager import StrategyManager

logger = logging.getLogger(__name__)


def check_dependencies() -> dict:
    """Check required dependencies"""
    dependencies = {}

    try:
        import polars
        dependencies['polars'] = True
    except ImportError:
        dependencies['polars'] = False

    try:
        import pandas
        dependencies['pandas'] = True
    except ImportError:
        dependencies['pandas'] = False

    try:
        import numpy
        dependencies['numpy'] = True
    except ImportError:
        dependencies['numpy'] = False

    try:
        from SmartApi import SmartConnect
        dependencies['smartapi'] = True
    except ImportError:
        dependencies['smartapi'] = False

    try:
        import telegram
        dependencies['telegram'] = True
    except ImportError:
        dependencies['telegram'] = False

    return dependencies


def get_system_info() -> dict:
    """Get system information"""
    try:
        import psutil

        return {
            'cpu_percent': psutil.cpu_percent(interval=1),
            'memory_percent': psutil.virtual_memory().percent,
            'disk_usage': psutil.disk_usage('/').percent,
            'cpu_count': psutil.cpu_count()
        }
    except ImportError:
        return {
            'cpu_percent': 0,
            'memory_percent': 0,
            'disk_usage': 0,
            'cpu_count': 1
        }


class MarketMonitoringAgent:
    """
    Market Monitoring Agent Gateway

    Clean gateway that coordinates all market monitoring modules.
    Acts as the central orchestrator for real-time market data tracking,
    technical analysis, regime detection, signal generation, and notifications.
    """

    def __init__(self, config_path: str = "agents/config/market_monitoring_config.yaml"):
        """Initialize the Market Monitoring Agent"""
        self.config_path = config_path
        self.config = None
        self.is_running = False
        self.is_connected = False

        # Initialize all modular components
        self.config_manager = ConfigManager()
        self.data_downloader = None
        self.websocket_manager = None
        self.data_processor = None
        self.indicator_calculator = None
        self.regime_detector = None
        self.signal_generator = None
        self.ai_integration = None
        self.notification_manager = None
        self.performance_monitor = None

        # Event handlers
        self.signal_handlers = []
        self.regime_change_handlers = []

        logger.info("[INIT] Market Monitoring Agent Gateway initialized")

    async def initialize(self):
        """Initialize all components"""
        try:
            logger.info("[INIT] Initializing Market Monitoring Agent components...")

            # Load configuration
            self.config = self.config_manager.load_config(self.config_path)

            # Initialize all components
            self.data_downloader = DataDownloader(self.config)
            self.websocket_manager = WebSocketManager(self.config)
            self.data_processor = DataProcessor(self.config)
            self.indicator_calculator = IndicatorCalculator(self.config)
            self.regime_detector = RegimeDetector(self.config)
            self.signal_generator = SignalGenerator(self.config)
            self.ai_integration = AIIntegration(self.config)
            self.notification_manager = NotificationManager(self.config)
            self.performance_monitor = PerformanceMonitor(self.config)

            # Initialize components
            await self.data_downloader.initialize()
            await self.websocket_manager.initialize()
            await self.data_processor.initialize()
            await self.indicator_calculator.initialize()
            await self.regime_detector.initialize()
            await self.signal_generator.initialize()
            await self.ai_integration.initialize()
            await self.notification_manager.initialize()
            await self.performance_monitor.initialize()

            logger.info("[SUCCESS] All components initialized successfully")

        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize components: {e}")
            raise

    async def setup(self):
        """Setup the agent (alias for initialize for backward compatibility)"""
        await self.initialize()

    async def start(self):
        """Start the Market Monitoring Agent"""
        try:
            logger.info("[START] Starting Market Monitoring Agent...")

            if not self.config:
                await self.initialize()

            # Start all components
            await self.websocket_manager.start()
            await self.data_processor.start()
            await self.performance_monitor.start()

            self.is_running = True
            self.is_connected = True

            logger.info("[SUCCESS] Market Monitoring Agent started successfully")

        except Exception as e:
            logger.error(f"[ERROR] Failed to start Market Monitoring Agent: {e}")
            raise

    async def stop(self):
        """Stop the Market Monitoring Agent"""
        try:
            logger.info("[STOP] Stopping Market Monitoring Agent...")

            self.is_running = False
            self.is_connected = False

            # Stop all components
            if self.websocket_manager:
                await self.websocket_manager.stop()
            if self.data_processor:
                await self.data_processor.stop()
            if self.performance_monitor:
                await self.performance_monitor.stop()

            logger.info("[SUCCESS] Market Monitoring Agent stopped successfully")

        except Exception as e:
            logger.error(f"[ERROR] Error stopping Market Monitoring Agent: {e}")

    async def start_background_tasks(self):
        """Start background monitoring tasks"""
        try:
            logger.info("[BACKGROUND] Starting background monitoring tasks...")

            # Start monitoring loops
            if self.data_processor:
                asyncio.create_task(self.data_processor.start_monitoring())
            if self.signal_generator:
                asyncio.create_task(self.signal_generator.start_monitoring())
            if self.regime_detector:
                asyncio.create_task(self.regime_detector.start_monitoring())

            logger.info("[SUCCESS] Background tasks started")

        except Exception as e:
            logger.error(f"[ERROR] Error starting background tasks: {e}")

    async def download_live_historical_data(self, days_back: int = 30, max_symbols: int = 500, testing_mode: bool = False):
        """Download historical data"""
        try:
            if not self.data_downloader:
                logger.error("[ERROR] Data downloader not initialized")
                return False

            logger.info(f"[DOWNLOAD] Starting historical data download: {days_back} days, {max_symbols} symbols")

            success = await self.data_downloader.download_historical_data(
                days_back=days_back,
                max_symbols=max_symbols,
                testing_mode=testing_mode
            )

            if success:
                logger.info("[SUCCESS] Historical data download completed")
            else:
                logger.error("[ERROR] Historical data download failed")

            return success

        except Exception as e:
            logger.error(f"[ERROR] Error in historical data download: {e}")
            return False

    def add_signal_handler(self, handler: Callable):
        """Add signal handler"""
        self.signal_handlers.append(handler)
        logger.debug(f"[HANDLER] Signal handler added, total: {len(self.signal_handlers)}")

    def add_regime_change_handler(self, handler: Callable):
        """Add regime change handler"""
        self.regime_change_handlers.append(handler)
        logger.debug(f"[HANDLER] Regime change handler added, total: {len(self.regime_change_handlers)}")

    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics"""
        try:
            metrics = {
                'is_running': self.is_running,
                'is_connected': self.is_connected,
                'active_signals': 0,
                'subscribed_symbols': 0,
                'market_regime': 'unknown',
                'system_info': {}
            }

            # Get metrics from components
            if self.signal_generator:
                active_signals = self.signal_generator.get_active_signals()
                metrics['active_signals'] = len(active_signals)

            if self.websocket_manager:
                connection_status = self.websocket_manager.get_connection_status()
                metrics['subscribed_symbols'] = connection_status.get('subscribed_symbols', 0)
                metrics['is_connected'] = connection_status.get('is_connected', False)

            if self.regime_detector:
                current_regime = self.regime_detector.get_current_regime()
                if current_regime:
                    metrics['market_regime'] = current_regime.regime

            if self.performance_monitor:
                system_metrics = self.performance_monitor.get_system_metrics()
                metrics['system_info'] = system_metrics

            return metrics

        except Exception as e:
            logger.error(f"[ERROR] Error getting performance metrics: {e}")
            return {
                'is_running': self.is_running,
                'is_connected': self.is_connected,
                'error': str(e)
            }

    async def cleanup_memory(self):
        """Cleanup memory"""
        try:
            if self.data_processor:
                await self.data_processor.cleanup_old_data()

            if self.performance_monitor:
                self.performance_monitor.cleanup_old_metrics()

            logger.info("[CLEANUP] Memory cleanup completed")

        except Exception as e:
            logger.error(f"[ERROR] Error during memory cleanup: {e}")

    async def reduce_processing_load(self):
        """Reduce processing load"""
        try:
            if self.data_processor:
                await self.data_processor.reduce_processing_frequency()

            if self.websocket_manager:
                await self.websocket_manager.reduce_subscription_count()

            logger.info("[LOAD] Processing load reduced")

        except Exception as e:
            logger.error(f"[ERROR] Error reducing processing load: {e}")

    async def reconnect_websocket(self):
        """Reconnect WebSocket"""
        try:
            if self.websocket_manager:
                await self.websocket_manager.reconnect()
                self.is_connected = self.websocket_manager.is_connected()
                logger.info("[RECONNECT] WebSocket reconnection completed")

        except Exception as e:
            logger.error(f"[ERROR] Error reconnecting WebSocket: {e}")

    async def refresh_data(self):
        """Refresh stale data"""
        try:
            if self.data_processor:
                await self.data_processor.refresh_stale_data()

            logger.info("[REFRESH] Data refresh completed")

        except Exception as e:
            logger.error(f"[ERROR] Error refreshing data: {e}")

    async def update_strategy_parameters(self, adjustments: Dict[str, Any]):
        """Update strategy parameters"""
        try:
            if self.signal_generator:
                await self.signal_generator.update_parameters(adjustments)

            logger.info(f"[STRATEGY] Parameters updated: {adjustments}")

        except Exception as e:
            logger.error(f"[ERROR] Error updating strategy parameters: {e}")

    async def _notify_signal_handlers(self, signal: TradingSignal):
        """Notify all signal handlers"""
        for handler in self.signal_handlers:
            try:
                await handler(signal)
            except Exception as e:
                logger.error(f"[ERROR] Error in signal handler: {e}")

    async def _notify_regime_change_handlers(self, old_regime: Optional[MarketRegime], new_regime: MarketRegime):
        """Notify all regime change handlers"""
        for handler in self.regime_change_handlers:
            try:
                await handler(old_regime, new_regime)
            except Exception as e:
                logger.error(f"[ERROR] Error in regime change handler: {e}")


class MarketMonitoringRunner:
    """
    Enhanced Production runner for Market Monitoring Agent

    Features:
    - Enhanced graceful startup and shutdown
    - Advanced signal handling
    - Intelligent error recovery
    - Comprehensive performance monitoring
    - Advanced health checks
    - Resource management
    - Graceful degradation
    """

    def __init__(self, config_path: str = "agents/config/market_monitoring_config.yaml"):
        """Initialize enhanced runner"""
        self.config_path = config_path
        self.agent = None  # Use the gateway agent directly
        self.is_running = False
        self.shutdown_event = asyncio.Event()

        # Initialize enhanced managers
        self.config_manager = ConfigManager()
        self.performance_optimizer = None
        self.resource_manager = None
        self.health_monitor = None
        self.degradation_manager = None
        self.strategy_manager = None

        # Setup signal handlers
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

        logger.info("[INIT] Enhanced Market Monitoring Runner initialized")

    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.info(f"[SIGNAL] Received signal {signum}, initiating graceful shutdown...")
        self.shutdown_event.set()
    
    async def start(self):
        """Start the Enhanced Market Monitoring Agent"""
        try:
            logger.info("[CONFIG] Starting Enhanced Market Monitoring Agent...")

            # Check dependencies
            await self._check_dependencies()

            # Check system resources
            await self._check_system_resources()

            # Initialize enhanced components
            await self._initialize_enhanced_components()

            # Create and setup gateway agent
            self.agent = MarketMonitoringAgent(self.config_path)
            await self.agent.initialize()

            # Add custom handlers
            await self._setup_enhanced_handlers()

            # Start monitoring
            self.is_running = True
            logger.info("[SUCCESS] Enhanced Market Monitoring Agent started successfully")

            # Run main loop
            await self._run_enhanced_main_loop()

        except Exception as e:
            logger.error(f"[ERROR] Failed to start Enhanced Market Monitoring Agent: {e}")
            raise
    
    async def stop(self):
        """Stop the Enhanced Market Monitoring Agent"""
        try:
            logger.info("[STOP] Stopping Enhanced Market Monitoring Agent...")

            self.is_running = False

            # Stop gateway agent
            if self.agent:
                await self.agent.stop()

            # Stop enhanced components
            await self._shutdown_enhanced_components()

            logger.info("[SUCCESS] Enhanced Market Monitoring Agent stopped successfully")

        except Exception as e:
            logger.error(f"[ERROR] Error stopping enhanced agent: {e}")

    async def _initialize_enhanced_components(self):
        """Initialize enhanced components"""
        try:
            logger.info("[INIT] Initializing enhanced components...")

            # Load configuration
            config = self.config_manager.load_config(self.config_path)

            # Initialize performance optimizer
            self.performance_optimizer = PerformanceOptimizer(config)
            await self.performance_optimizer.initialize()

            # Initialize resource manager
            self.resource_manager = ResourceManager(config)
            await self.resource_manager.start_monitoring()

            # Initialize health monitor
            self.health_monitor = HealthMonitor(config)
            await self.health_monitor.start_monitoring()

            # Initialize degradation manager
            self.degradation_manager = GracefulDegradationManager(config)

            # Initialize strategy manager
            self.strategy_manager = StrategyManager(config)

            logger.info("[SUCCESS] Enhanced components initialized")

        except Exception as e:
            logger.error(f"[ERROR] Error initializing enhanced components: {e}")
            raise

    async def _shutdown_enhanced_components(self):
        """Shutdown enhanced components"""
        try:
            logger.info("[SHUTDOWN] Shutting down enhanced components...")

            if self.performance_optimizer:
                await self.performance_optimizer.shutdown()

            if self.resource_manager:
                await self.resource_manager.stop_monitoring()

            if self.health_monitor:
                await self.health_monitor.stop_monitoring()

            logger.info("[SUCCESS] Enhanced components shutdown completed")

        except Exception as e:
            logger.error(f"[ERROR] Error shutting down enhanced components: {e}")
    
    async def _check_dependencies(self):
        """Check required dependencies with enhanced validation"""
        logger.info("[DEBUG] Checking dependencies...")

        deps = check_dependencies()
        missing_deps = [dep for dep, available in deps.items() if not available]

        if missing_deps:
            logger.error(f"[ERROR] Missing dependencies: {missing_deps}")
            logger.info("📦 Install missing dependencies:")

            if 'smartapi' in missing_deps:
                logger.info("   pip install smartapi-python pyotp")
            if 'telegram' in missing_deps:
                logger.info("   pip install python-telegram-bot")
            if 'polars' in missing_deps:
                logger.info("   pip install polars[gpu]")  # Use GPU-accelerated version
            if 'pandas' in missing_deps:
                logger.info("   pip install pandas")
            if 'numpy' in missing_deps:
                logger.info("   pip install numpy")

            # Configure graceful degradation for missing optional services
            if self.degradation_manager:
                await self.degradation_manager.configure_fallbacks(missing_deps)

            # Only fail for critical dependencies
            critical_deps = ['polars', 'numpy']  # Reduced critical deps, pandas is optional
            critical_missing = [dep for dep in missing_deps if dep in critical_deps]

            if critical_missing:
                raise RuntimeError(f"Critical dependencies missing: {critical_missing}")

        logger.info("[SUCCESS] Enhanced dependencies check completed")
    
    async def _check_system_resources(self):
        """Check system resources with enhanced monitoring"""
        logger.info("[SYSTEM] Checking system resources...")

        if self.resource_manager:
            # Use enhanced resource manager
            resource_status = await self.resource_manager.check_system_resources()

            # Check memory
            memory_percent = resource_status.get('memory_percent', 0)
            if memory_percent > 90:
                logger.warning(f"[WARN]  High memory usage: {memory_percent}%")
            elif memory_percent > 80:
                logger.info(f"[INFO]  Moderate memory usage: {memory_percent}%")

            # Check CPU
            cpu_percent = resource_status.get('cpu_percent', 0)
            if cpu_percent > 90:
                logger.warning(f"[WARN]  High CPU usage: {cpu_percent}%")
            elif cpu_percent > 80:
                logger.info(f"[INFO]  Moderate CPU usage: {cpu_percent}%")

            # Check disk space
            disk_percent = resource_status.get('disk_percent', 0)
            if disk_percent > 90:
                logger.warning(f"[WARN]  Low disk space: {disk_percent}% used")
            elif disk_percent > 80:
                logger.info(f"[INFO]  Moderate disk usage: {disk_percent}%")

            # Log comprehensive system info
            logger.info(f"[SYSTEM] Enhanced: CPU {cpu_percent:.1f}%, Memory {memory_percent:.1f}%, "
                       f"Disk {disk_percent:.1f}%, Available Memory: {resource_status.get('memory_available_gb', 0):.1f}GB")

            # Check if resources are adequate
            if not resource_status.get('adequate', True):
                logger.warning("[WARN]  System resources may be insufficient for optimal performance")
        else:
            # Fallback to basic system info
            system_info = get_system_info()
            memory_percent = system_info.get('memory_percent', 0)
            cpu_percent = system_info.get('cpu_percent', 0)
            disk_usage = system_info.get('disk_usage', 0)

            logger.info(f"[SYSTEM] Basic: CPU {cpu_percent}%, Memory {memory_percent}%, Disk {disk_usage}%")
    
    async def _setup_enhanced_handlers(self):
        """Setup enhanced event handlers with performance tracking"""

        async def enhanced_signal_handler(signal: TradingSignal):
            """Enhanced signal handler with validation and performance tracking"""
            try:
                # Validate signal through strategy manager
                if self.strategy_manager:
                    # Update strategy performance tracking
                    # This would be called later when we know the outcome
                    pass

                # Process through degradation manager if needed
                if self.degradation_manager:
                    signal = await self.degradation_manager.process_signal(signal)

                # Enhanced logging with more context
                logger.info(f"[SIGNAL] {signal.symbol} | {signal.strategy} | {signal.action} | "
                           f"Price: ₹{signal.price:.2f} | Target: ₹{signal.target:.2f} | "
                           f"SL: ₹{signal.stop_loss:.2f} | Confidence: {signal.confidence:.1%} | "
                           f"Regime: {getattr(signal, 'market_regime', 'unknown')}")

                # Record performance metrics
                if self.performance_optimizer:
                    # Track signal generation performance
                    pass

                # Add custom signal processing here
                # e.g., send to order management system, update database, etc.

            except Exception as e:
                logger.error(f"[ERROR] Error in enhanced signal handler: {e}")

        async def enhanced_regime_change_handler(old_regime: Optional[MarketRegime], new_regime: MarketRegime):
            """Enhanced regime change handler with strategy adjustment"""
            try:
                old_regime_name = old_regime.regime if old_regime else "None"

                logger.info(f"[REGIME] {old_regime_name} → {new_regime.regime} | "
                           f"Confidence: {new_regime.confidence:.1%} | "
                           f"Volatility: {new_regime.volatility_level} | "
                           f"Breadth: {new_regime.market_breadth:.1f}%")

                # Update strategy manager with new regime
                if self.strategy_manager:
                    # Strategy manager can adjust parameters based on regime
                    pass

                # Update resource manager for regime-based scaling
                if self.resource_manager:
                    # Adjust resource allocation based on market regime
                    if new_regime.volatility_level == 'high':
                        logger.info("[RESOURCE] High volatility detected, adjusting resource allocation")

                # Record health metrics
                if self.health_monitor:
                    self.health_monitor.record_operation(success=True)

                # Add custom regime change processing here
                # e.g., adjust strategy parameters, send alerts, etc.

            except Exception as e:
                logger.error(f"[ERROR] Error in enhanced regime change handler: {e}")

        # Register enhanced handlers with the gateway agent
        if self.agent:
            self.agent.add_signal_handler(enhanced_signal_handler)
            self.agent.add_regime_change_handler(enhanced_regime_change_handler)

        logger.info("[CONFIG] Enhanced handlers registered")
    
    async def _run_enhanced_main_loop(self):
        """Run enhanced main monitoring loop with advanced features"""
        try:
            # Check if this is part of a workflow (demo=False means full workflow)
            demo_mode = os.getenv('DEMO_MODE', 'true').lower() == 'true'
            workflow_mode = os.getenv('WORKFLOW_MODE', 'false').lower() == 'true'

            if not demo_mode and workflow_mode:
                # Enhanced workflow mode with performance optimization
                logger.info("[WORKFLOW] Running in enhanced workflow mode")

                # Determine parameters based on testing mode
                testing_mode = os.getenv('TESTING_MODE', 'false').lower() == 'true'
                download_days_back = 5 if testing_mode else 35
                max_symbols_to_download = int(os.getenv('MAX_SYMBOLS', '500')) if not testing_mode else int(os.getenv('MAX_SYMBOLS', '20'))

                # Optimize resource allocation for symbol count
                if self.resource_manager:
                    optimization = await self.resource_manager.optimize_for_symbol_count(max_symbols_to_download)
                    logger.info(f"[OPTIMIZATION] Resource optimization: {optimization}")

                # Step 1: Enhanced historical data download with incremental loading
                logger.info(f"[PRE-MARKET] Starting optimized historical data download for {download_days_back} days and {max_symbols_to_download} symbols...")

                if self.agent:
                    download_success = await self.agent.download_live_historical_data(
                        days_back=download_days_back,
                        max_symbols=max_symbols_to_download,
                        testing_mode=testing_mode
                    )
                else:
                    logger.warning("[WORKFLOW] Gateway agent not available, skipping download")
                    download_success = True

                if not download_success:
                    logger.error("[ERROR] Enhanced historical data download failed")
                    # Try graceful degradation
                    if self.degradation_manager:
                        await self.degradation_manager.handle_service_failure('data_download', Exception("Download failed"))
                    return

                logger.info("[SUCCESS] Enhanced historical data download completed")

                # Step 2: Start enhanced live monitoring
                logger.info("[LIVE] Starting enhanced live market monitoring...")

                # Start gateway agent
                if self.agent:
                    await self.agent.start()
                    await self.agent.start_background_tasks()

                # Run for specified duration with enhanced monitoring
                timeout_duration = 30  # 30 seconds for live monitoring in workflow
                logger.info(f"[WORKFLOW] Running enhanced monitoring for {timeout_duration} seconds...")

                try:
                    # Wait for the specified duration or shutdown signal
                    await asyncio.wait_for(self.shutdown_event.wait(), timeout=timeout_duration)
                    logger.info("[WORKFLOW] Enhanced monitoring stopped by shutdown signal")
                except asyncio.TimeoutError:
                    logger.info(f"[WORKFLOW] Enhanced monitoring completed {timeout_duration} second session")

                return

            # Enhanced continuous mode for demo/standalone operation
            logger.info("[CONTINUOUS] Starting enhanced continuous monitoring mode")

            # Start gateway agent
            if self.agent:
                await self.agent.start()
                await self.agent.start_background_tasks()

            # Start enhanced health monitoring
            health_task = asyncio.create_task(self._enhanced_health_monitoring_loop())

            # Start performance monitoring
            performance_task = asyncio.create_task(self._performance_monitoring_loop())

            # Wait for shutdown signal
            done, pending = await asyncio.wait(
                [health_task, performance_task, asyncio.create_task(self.shutdown_event.wait())],
                return_when=asyncio.FIRST_COMPLETED
            )

            # Cancel pending tasks
            for task in pending:
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass

            logger.info("[STOP] Enhanced main monitoring loop completed")

        except Exception as e:
            logger.error(f"[ERROR] Error in enhanced main loop: {e}")
            # Try graceful degradation
            if self.degradation_manager:
                await self.degradation_manager.handle_service_failure('main_loop', e)
            raise
    
    async def _enhanced_health_monitoring_loop(self):
        """Enhanced health monitoring loop with comprehensive checks"""
        while self.is_running:
            try:
                # Perform comprehensive health check
                if self.health_monitor and self.agent:
                    health_status = await self.health_monitor.perform_health_check(self.agent)

                    # Log health status based on severity
                    if health_status['overall'] == 'healthy':
                        logger.debug(f"[HEALTH] System healthy - Score: {health_status['score']}")
                    elif health_status['overall'] == 'warning':
                        logger.warning(f"[HEALTH] System warnings - Score: {health_status['score']}, Issues: {len(health_status['issues'])}")
                    else:
                        logger.error(f"[HEALTH] System critical - Score: {health_status['score']}, Issues: {len(health_status['issues'])}")

                        # Handle critical issues
                        for issue in health_status['issues']:
                            if issue['severity'] == 'critical':
                                logger.error(f"[CRITICAL] {issue['message']}")

                                # Trigger recovery mechanisms
                                if self.degradation_manager:
                                    await self.degradation_manager.handle_service_failure(
                                        issue['type'],
                                        Exception(issue['message'])
                                    )

                # Check resource usage and scaling
                if self.resource_manager:
                    resource_status = await self.resource_manager.check_resource_usage()

                    # Log resource summary
                    logger.debug(f"[RESOURCES] CPU: {resource_status.get('cpu_percent', 0):.1f}%, "
                               f"Memory: {resource_status.get('memory_percent', 0):.1f}%, "
                               f"Disk: {resource_status.get('disk_percent', 0):.1f}%")

                # Wait for next health check with adaptive intervals
                testing_mode = os.getenv('TESTING_MODE', 'false').lower() == 'true'
                health_interval = 10 if testing_mode else 30  # More frequent checks
                await asyncio.sleep(health_interval)

            except Exception as e:
                logger.error(f"[ERROR] Error in enhanced health monitoring: {e}")
                if self.health_monitor:
                    self.health_monitor.record_operation(success=False)
                await asyncio.sleep(60)

    async def _performance_monitoring_loop(self):
        """Performance monitoring loop with optimization"""
        while self.is_running:
            try:
                # Get performance metrics from all components
                performance_data = {}

                if self.performance_optimizer:
                    performance_data['optimizer'] = self.performance_optimizer.get_performance_metrics()

                if self.resource_manager:
                    performance_data['resources'] = self.resource_manager.get_resource_summary()

                if self.strategy_manager:
                    performance_data['strategies'] = self.strategy_manager.get_strategy_performance()

                if self.health_monitor:
                    performance_data['health'] = self.health_monitor.get_health_summary()

                # Log performance summary
                if performance_data:
                    cache_hit_rate = performance_data.get('optimizer', {}).get('cache_hit_rate', 0)
                    current_score = performance_data.get('health', {}).get('current_score', 0)

                    logger.info(f"[PERFORMANCE] Cache Hit Rate: {cache_hit_rate:.1%}, "
                               f"Health Score: {current_score}, "
                               f"Components: {len(performance_data)}")

                # Trigger optimizations if needed
                if self.performance_optimizer:
                    # Clear old cache entries
                    self.performance_optimizer.clear_cache(older_than_hours=2)

                # Wait for next performance check
                testing_mode = os.getenv('TESTING_MODE', 'false').lower() == 'true'
                perf_interval = 30 if testing_mode else 120  # 30s in testing, 2min in production
                await asyncio.sleep(perf_interval)

            except Exception as e:
                logger.error(f"[ERROR] Error in performance monitoring: {e}")
                await asyncio.sleep(120)
    
    async def run(self):
        """Main run method"""
        try:
            await self.start()
        except KeyboardInterrupt:
            logger.info("[STOP] Received keyboard interrupt")
        except Exception as e:
            logger.error(f"[ERROR] Runtime error: {e}")
            raise
        finally:
            await self.stop()

def setup_logging(log_level: str = "INFO"):
    """Setup logging configuration"""
    # Create logs directory
    os.makedirs("logs", exist_ok=True)
    
    # Configure logging
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format=log_format,
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('logs/market_monitoring_runner.log')
        ]
    )

def main():
    """Enhanced main entry point"""
    parser = argparse.ArgumentParser(description='Enhanced Market Monitoring Agent Runner')
    parser.add_argument('--config', '-c',
                       default='agents/config/market_monitoring_config.yaml',
                       help='Configuration file path')
    parser.add_argument('--log-level', '-l',
                       default='INFO',
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='Logging level')
    parser.add_argument('--check-deps', action='store_true',
                       help='Check dependencies and exit')
    parser.add_argument('--validate-config', action='store_true',
                       help='Validate configuration and exit')
    parser.add_argument('--performance-report', action='store_true',
                       help='Generate performance report and exit')

    args = parser.parse_args()

    # Setup enhanced logging
    setup_logging(args.log_level)

    # Check dependencies only
    if args.check_deps:
        deps = check_dependencies()
        print("\n📦 Enhanced Dependency Status:")
        for dep, available in deps.items():
            status = "✅ [AVAILABLE]" if available else "❌ [MISSING]"
            print(f"   {status} {dep}")

        missing = [dep for dep, available in deps.items() if not available]
        if missing:
            print(f"\n❌ Missing dependencies: {missing}")
            print("\n📦 Installation commands:")
            if 'polars' in missing:
                print("   pip install polars[gpu]")
            if 'smartapi' in missing:
                print("   pip install smartapi-python pyotp")
            if 'telegram' in missing:
                print("   pip install python-telegram-bot")
            sys.exit(1)
        else:
            print("\n✅ All dependencies available")
            sys.exit(0)

    # Configuration validation mode
    if args.validate_config:
        try:
            from market_monitoring.config_validator import ConfigValidator
            validator = ConfigValidator()
            report = validator.generate_validation_report(args.config)
            print(report)
            return
        except ImportError:
            logger.error("❌ Configuration validator not available")
            return
        except Exception as e:
            logger.error(f"❌ Configuration validation failed: {e}")
            return

    # Performance report mode
    if args.performance_report:
        logger.info("📊 Performance report mode not yet implemented")
        return

    # Check if config file exists
    if not os.path.exists(args.config):
        logger.error(f"❌ Configuration file not found: {args.config}")
        sys.exit(1)

    # Create and run enhanced agent
    runner = MarketMonitoringRunner(args.config)

    try:
        logger.info("=" * 60)
        logger.info("🚀 ENHANCED MARKET MONITORING AGENT")
        logger.info("=" * 60)
        logger.info(f"📁 Configuration: {args.config}")
        logger.info(f"📊 Log Level: {args.log_level}")
        logger.info(f"⏰ Start Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info("=" * 60)

        asyncio.run(runner.run())

    except KeyboardInterrupt:
        logger.info("🛑 Enhanced Market Monitoring Agent stopped by user")
    except Exception as e:
        logger.error(f"❌ Fatal error in enhanced agent: {e}")
        logger.exception("Full error traceback:")
        sys.exit(1)
    finally:
        logger.info("=" * 60)
        logger.info("🏁 ENHANCED MARKET MONITORING AGENT SHUTDOWN")
        logger.info(f"⏰ End Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info("=" * 60)

if __name__ == "__main__":
    # Check if virtual environment is activated
    if not hasattr(sys, 'real_prefix') and not (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("⚠️  WARNING: Virtual environment not detected!")
        print("   Consider activating venv: source /media/jmk/BKP/Documents/Option/.venv/bin/activate")
        print()

    main()
