#!/usr/bin/env python3
"""
MARKET MONITORING AGENT
Wrapper for market data agent to provide backward compatibility

This module provides a unified interface for market monitoring functionality
by wrapping the existing market data agent and data structures.
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional

# Import data structures
from .data_structures import (
    MarketTick, OHLCV, MarketIndicators, MarketRegime, 
    TradingSignal, MarketMonitoringConfig, SystemMetrics
)

# Import core components
from .market_data_agent import MarketDataAgent, MarketDataPoint
from .modern_market_data_agent import ModernMarketDataAgent

logger = logging.getLogger(__name__)

class MarketMonitoringAgent:
    """
    Market Monitoring Agent - Unified interface for market data monitoring
    
    This class provides a unified interface that wraps the existing market data agents
    and provides backward compatibility for the live trading orchestrator.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize the market monitoring agent"""
        self.config = config
        self.name = "MarketMonitoringAgent"
        self.running = False
        self.initialized = False

        # Create a dummy event bus and session ID for compatibility
        from utils.event_bus import EventBus
        import uuid

        self.event_bus = EventBus()
        self.session_id = str(uuid.uuid4())

        # Initialize the underlying market data agent
        try:
            self.market_data_agent = ModernMarketDataAgent(
                event_bus=self.event_bus,
                config=config,
                session_id=self.session_id
            )
        except Exception as e:
            logger.warning(f"[FALLBACK] ModernMarketDataAgent failed: {e}, trying MarketDataAgent")
            # Fallback to basic market data agent
            try:
                self.market_data_agent = MarketDataAgent(
                    event_bus=self.event_bus,
                    config=config,
                    session_id=self.session_id
                )
            except Exception as e2:
                logger.error(f"[ERROR] Both MarketDataAgent implementations failed: {e2}")
                # Create a minimal mock agent for now
                self.market_data_agent = None

        # Market data storage
        self.market_data: Dict[str, MarketDataPoint] = {}
        self.ohlcv_data: Dict[str, List[OHLCV]] = {}
        self.market_indicators: Dict[str, MarketIndicators] = {}

        logger.info(f"[INIT] {self.name} initialized")
    
    async def initialize(self) -> bool:
        """Initialize the market monitoring agent"""
        try:
            logger.info(f"[INIT] Initializing {self.name}...")

            # Start the event bus processor
            if hasattr(self.event_bus, 'start_processor'):
                await self.event_bus.start_processor()

            # Initialize the underlying agent
            if self.market_data_agent and hasattr(self.market_data_agent, 'initialize'):
                success = await self.market_data_agent.initialize()
                if not success:
                    logger.warning(f"[WARNING] Market data agent initialization failed, continuing with limited functionality")
            elif not self.market_data_agent:
                logger.warning(f"[WARNING] No market data agent available, running in mock mode")

            self.initialized = True
            logger.info(f"[SUCCESS] {self.name} initialized successfully")
            return True

        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize {self.name}: {e}")
            return False
    
    async def start(self) -> bool:
        """Start the market monitoring agent"""
        try:
            if not self.initialized:
                await self.initialize()

            logger.info(f"[START] Starting {self.name}...")

            # Start the underlying agent
            if self.market_data_agent and hasattr(self.market_data_agent, 'start'):
                await self.market_data_agent.start()
            elif not self.market_data_agent:
                logger.info(f"[INFO] Running in mock mode - no market data agent available")

            self.running = True
            logger.info(f"[SUCCESS] {self.name} started successfully")
            return True

        except Exception as e:
            logger.error(f"[ERROR] Failed to start {self.name}: {e}")
            return False

    async def shutdown(self) -> bool:
        """Shutdown the market monitoring agent"""
        try:
            logger.info(f"[SHUTDOWN] Shutting down {self.name}...")

            self.running = False

            # Shutdown the underlying agent
            if self.market_data_agent and hasattr(self.market_data_agent, 'stop'):
                await self.market_data_agent.stop()

            # Shutdown event bus
            if hasattr(self.event_bus, 'shutdown'):
                await self.event_bus.shutdown()
            elif hasattr(self.event_bus, 'stop'):
                await self.event_bus.stop()

            logger.info(f"[SUCCESS] {self.name} shutdown successfully")
            return True

        except Exception as e:
            logger.error(f"[ERROR] Failed to shutdown {self.name}: {e}")
            return False

    def add_signal_handler(self, handler):
        """Add a signal handler for compatibility"""
        try:
            logger.info(f"[HANDLER] Adding signal handler to {self.name}")
            # Store the handler for future use
            if not hasattr(self, 'signal_handlers'):
                self.signal_handlers = []
            self.signal_handlers.append(handler)

            # If the underlying agent supports signal handlers, add it there too
            if self.market_data_agent and hasattr(self.market_data_agent, 'add_signal_handler'):
                self.market_data_agent.add_signal_handler(handler)

            return True

        except Exception as e:
            logger.error(f"[ERROR] Failed to add signal handler: {e}")
            return False
    
    async def stop(self) -> bool:
        """Stop the market monitoring agent"""
        try:
            logger.info(f"[STOP] Stopping {self.name}...")
            
            # Stop the underlying agent
            if hasattr(self.market_data_agent, 'stop'):
                await self.market_data_agent.stop()
            
            self.running = False
            logger.info(f"[SUCCESS] {self.name} stopped successfully")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to stop {self.name}: {e}")
            return False
    
    async def is_healthy(self) -> bool:
        """Check if the agent is healthy"""
        try:
            if hasattr(self.market_data_agent, 'is_healthy'):
                return await self.market_data_agent.is_healthy()
            return self.running and self.initialized
        except Exception:
            return False
    
    def get_market_data(self, symbol: str) -> Optional[MarketDataPoint]:
        """Get latest market data for a symbol"""
        try:
            if hasattr(self.market_data_agent, 'get_market_data'):
                return self.market_data_agent.get_market_data(symbol)
            return self.market_data.get(symbol)
        except Exception as e:
            logger.error(f"[ERROR] Failed to get market data for {symbol}: {e}")
            return None
    
    def get_ohlcv_data(self, symbol: str, timeframe: str = "1min") -> List[OHLCV]:
        """Get OHLCV data for a symbol"""
        try:
            if hasattr(self.market_data_agent, 'get_ohlcv_data'):
                return self.market_data_agent.get_ohlcv_data(symbol, timeframe)
            return self.ohlcv_data.get(f"{symbol}_{timeframe}", [])
        except Exception as e:
            logger.error(f"[ERROR] Failed to get OHLCV data for {symbol}: {e}")
            return []
    
    def get_market_indicators(self, symbol: str) -> Optional[MarketIndicators]:
        """Get market indicators for a symbol"""
        try:
            return self.market_indicators.get(symbol)
        except Exception as e:
            logger.error(f"[ERROR] Failed to get market indicators for {symbol}: {e}")
            return None
    
    async def subscribe_symbols(self, symbols: List[str]) -> bool:
        """Subscribe to market data for symbols"""
        try:
            if hasattr(self.market_data_agent, 'subscribe_symbols'):
                return await self.market_data_agent.subscribe_symbols(symbols)
            return True
        except Exception as e:
            logger.error(f"[ERROR] Failed to subscribe to symbols: {e}")
            return False
    
    async def unsubscribe_symbols(self, symbols: List[str]) -> bool:
        """Unsubscribe from market data for symbols"""
        try:
            if hasattr(self.market_data_agent, 'unsubscribe_symbols'):
                return await self.market_data_agent.unsubscribe_symbols(symbols)
            return True
        except Exception as e:
            logger.error(f"[ERROR] Failed to unsubscribe from symbols: {e}")
            return False
    
    def get_status(self) -> Dict[str, Any]:
        """Get agent status"""
        return {
            'name': self.name,
            'running': self.running,
            'initialized': self.initialized,
            'symbols_count': len(self.market_data),
            'last_update': datetime.now().isoformat()
        }

# Export the main class and data structures for backward compatibility
__all__ = [
    'MarketMonitoringAgent',
    'MarketTick',
    'OHLCV', 
    'MarketIndicators',
    'MarketRegime',
    'TradingSignal',
    'MarketMonitoringConfig',
    'SystemMetrics'
]
