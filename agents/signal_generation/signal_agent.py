#!/usr/bin/env python3
"""
Modular Signal Agent for Trading System
- Generates entry and exit signals based on strategy configurations
- Can be used by backtesting, live trading, and evolution agents
- Supports intraday trading rules and time-based filtering
- Compatible with strategies.yaml configuration format
"""

import os
import logging
import yaml
import polars as pl
import numpy as np
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, time as dt_time
import re

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SignalAgent:
    """Modular Signal Agent for generating trading signals"""
    
    def __init__(self, strategies_config_path: str = "agents/config/strategies.yaml"):
        """Initialize Signal Agent with strategy configuration"""
        self.strategies_config_path = Path(strategies_config_path)
        self.strategies = self._load_strategies()
        logger.info(f"[SIGNAL_AGENT] Loaded {len(self.strategies)} strategies")
    
    def _load_strategies(self) -> List[Dict[str, Any]]:
        """Load strategies from YAML configuration file"""
        if not self.strategies_config_path.exists():
            logger.error(f"Strategies config file not found: {self.strategies_config_path}")
            return []
        
        try:
            with open(self.strategies_config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            strategies = config.get('strategies', [])
            logger.info(f"[LOAD] Loaded {len(strategies)} strategies from config")
            return strategies
        except Exception as e:
            logger.error(f"Error loading strategies config: {e}")
            return []
    
    def get_strategies(self, ranking_threshold: int = 0) -> List[Dict[str, Any]]:
        """Get strategies filtered by ranking threshold"""
        filtered_strategies = [s for s in self.strategies if s.get('ranking', 0) >= ranking_threshold]
        logger.debug(f"[FILTER] {len(filtered_strategies)} strategies above ranking {ranking_threshold}")
        return filtered_strategies
    
    def generate_signals(self, df: pl.DataFrame, strategy: Dict[str, Any], 
                        signal_types: List[str] = None) -> Dict[str, pl.Series]:
        """
        Generate trading signals for a given strategy
        
        Args:
            df: DataFrame with market data
            strategy: Strategy configuration dictionary
            signal_types: List of signal types to generate ['entry_long', 'entry_short', 'exit_long', 'exit_short']
        
        Returns:
            Dictionary of signal series
        """
        if signal_types is None:
            signal_types = ['entry_long', 'entry_short', 'exit_long', 'exit_short']
        
        strategy_name = strategy.get('name', 'Unknown')
        logger.debug(f"[SIGNALS] Generating signals for {strategy_name}")
        
        signals = {}
        
        for signal_type in signal_types:
            try:
                signal_series = self._generate_single_signal(df, signal_type, strategy)
                signals[signal_type] = signal_series
                logger.debug(f"[{strategy_name}] {signal_type}: {signal_series.sum()} signals")
            except Exception as e:
                logger.error(f"Error generating {signal_type} for {strategy_name}: {e}")
                signals[signal_type] = pl.Series("mask", [False] * df.height)
        
        return signals
    
    def _generate_single_signal(self, df: pl.DataFrame, signal_type: str, strategy: dict) -> pl.Series:
        """Generate a single signal type with enhanced validation"""
        strategy_name = strategy.get('name', 'Unknown')
        
        expr_str = strategy.get(signal_type, "").strip()

        # Special handling for exit conditions that include 'Entry' or risk parameters
        if signal_type.startswith('exit_'):
            # These parts should be handled by the backtesting engine, not signal generation
            # For now, we'll filter them out from the signal generation expression
            original_expr_parts = [p.strip() for p in expr_str.split('|')]
            filtered_expr_parts = []
            for part in original_expr_parts:
                if 'Entry' not in part and 'stop_loss_pct' not in part and 'take_profit_pct' not in part:
                    filtered_expr_parts.append(part)
                else:
                    logger.debug(f"[{strategy_name}] Filtering out backtesting-specific exit condition part: '{part}' from signal generation.")
            expr_str = " | ".join(filtered_expr_parts)
            if not expr_str: # If all parts were filtered out, no signal can be generated by SignalAgent
                logger.debug(f"[{strategy_name}] All exit condition parts for {signal_type} were filtered out. No signal generated by SignalAgent.")
                return pl.Series("mask", [False] * df.height)
        
        if not expr_str:
            logger.debug(f"No {signal_type} expression for strategy {strategy_name}")
            return pl.Series("mask", [False] * df.height)
        
        # Check available columns in dataframe
        available_cols = set(df.columns)
        logger.debug(f"Available columns in DataFrame: {sorted(available_cols)}")
        logger.debug(f"Original expression string for {signal_type}: '{expr_str}'")

        # Replace boolean operators first
        polars_expr = expr_str.replace(" and ", " & ").replace(" or ", " | ").replace(" not ", " ~")
        logger.debug(f"Expression after boolean operator replacement: '{polars_expr}'")

        # Replace column names with polars expressions
        # Create a list of column names sorted by length (longest first) to avoid partial matches
        sorted_cols = sorted(list(available_cols), key=len, reverse=True)
        
        for col_name in sorted_cols:
            # Use word boundaries to avoid partial matches within other words
            pattern = r'\b' + re.escape(col_name) + r'\b'
            polars_expr = re.sub(pattern, f'pl.col("{col_name}")', polars_expr)

        # Convert .rolling(N).<agg>() to Polars rolling_<agg>(window_size=N)
        rolling_patterns = [
            (r'pl\.col\("([^"]+)"\)\.rolling\(\s*(\d+)\s*\)\.mean\(\s*\)', r'pl.col("\1").rolling_mean(window_size=\2)'),
            (r'pl\.col\("([^"]+)"\)\.rolling\(\s*(\d+)\s*\)\.std\(\s*\)', r'pl.col("\1").rolling_std(window_size=\2)'),
            (r'pl\.col\("([^"]+)"\)\.rolling\(\s*(\d+)\s*\)\.max\(\s*\)', r'pl.col("\1").rolling_max(window_size=\2)'),
            (r'pl\.col\("([^"]+)"\)\.rolling\(\s*(\d+)\s*\)\.min\(\s*\)', r'pl.col("\1").rolling_min(window_size=\2)'),
            (r'pl\.col\("([^"]+)"\)\.rolling\(\s*(\d+)\s*\)\.sum\(\s*\)', r'pl.col("\1").rolling_sum(window_size=\2)'),
        ]
        for pat, repl in rolling_patterns:
            polars_expr = re.sub(pat, repl, polars_expr)

        # Normalize spacing around boolean operators to make splitting reliable
        polars_expr = re.sub(r'\s*&\s*', ' & ', polars_expr)
        polars_expr = re.sub(r'\s*\|\s*', ' | ', polars_expr)

        # Ensure each boolean operand is parenthesized to avoid Python comparison chaining
        # Example: a > b & c > d  ->  (a > b) & (c > d)
        def _wrap_condition(token: str) -> str:
            c = token.strip()
            if not c:
                return c
            # handle unary NOT '~'
            neg = ''
            while c.startswith('~'):
                neg += '~'
                c = c[1:].lstrip()
            # already wrapped
            if c.startswith('(') and c.endswith(')'):
                inner = c
            else:
                inner = f'({c})'
            return f'{neg}{inner}'

        parts = re.split(r'(\s*&\s*|\s*\|\s*)', polars_expr)
        if len(parts) > 1:
            polars_expr = ''.join(_wrap_condition(tok) if i % 2 == 0 else tok for i, tok in enumerate(parts))

        logger.debug(f"Expression after transformations: '{polars_expr}'")

        # Ensure all required indicators are present in the DataFrame
        # Extract all `pl.col("...")` references from the `polars_expr`
        referenced_cols = set(re.findall(r'pl.col\("(\w+)"\)', polars_expr))

        missing_cols_for_eval = [col for col in referenced_cols if col not in available_cols]

        if missing_cols_for_eval:
            logger.warning(f"Strategy '{strategy_name}' {signal_type} expression uses missing indicators: {missing_cols_for_eval}. Returning False signals.")
            return pl.Series("mask", [False] * df.height)

        logger.debug(f"Final Polars expression for eval: {polars_expr}")

        logger.debug(f"Evaluating expression for strategy '{strategy_name}' signal '{signal_type}': {polars_expr}")

        try:
            # Evaluate the expression using Polars' context
            result = df.with_columns(
                eval(polars_expr, {"pl": pl}).alias("signal")
            )["signal"]
            logger.debug(f"Generated {result.sum()} signals for {signal_type}")
            return result
        except Exception as e:
            logger.error(f"Error evaluating expression '{polars_expr}' for {strategy_name}: {e}")
            return pl.Series("mask", [False] * df.height)
    
    def apply_intraday_rules(self, df: pl.DataFrame, signals: Dict[str, pl.Series], 
                           strategy: Dict[str, Any]) -> Dict[str, pl.Series]:
        """Apply intraday trading rules to filter signals based on time constraints"""
        intraday_rules = strategy.get('intraday_rules', {})
        
        if not intraday_rules:
            return signals
        
        # Parse time constraints
        no_trade_after = intraday_rules.get('no_trade_after', '14:30')
        exit_all_at = intraday_rules.get('exit_all_at', '15:10')
        
        # Convert time strings to time objects
        try:
            no_trade_time = dt_time(*map(int, no_trade_after.split(':')))
            exit_time = dt_time(*map(int, exit_all_at.split(':')))
        except ValueError:
            logger.warning(f"Invalid time format in intraday rules: {no_trade_after}, {exit_all_at}")
            return signals
        
        # Extract time from datetime column
        if 'datetime' not in df.columns:
            logger.warning("No datetime column found for intraday rules")
            return signals
        
        df_with_time = df.with_columns([
            pl.col('datetime').dt.time().alias('time_only')
        ])
        
        # Apply time-based filtering
        filtered_signals = signals.copy()
        
        # Filter entry signals - no new trades after specified time
        if 'entry_long' in signals:
            entry_long_filtered = df_with_time.select(
                pl.when(pl.col('time_only') > no_trade_time)
                .then(False)
                .otherwise(signals['entry_long'])
                .alias('entry_long')
            ).to_series()
            filtered_signals['entry_long'] = entry_long_filtered
        
        if 'entry_short' in signals:
            entry_short_filtered = df_with_time.select(
                pl.when(pl.col('time_only') > no_trade_time)
                .then(False)
                .otherwise(signals['entry_short'])
                .alias('entry_short')
            ).to_series()
            filtered_signals['entry_short'] = entry_short_filtered
        
        # Add forced exit signals at market close
        if 'exit_long' in signals:
            exit_long_filtered = df_with_time.select(
                pl.when(pl.col('time_only') >= exit_time)
                .then(True)
                .otherwise(signals['exit_long'])
                .alias('exit_long')
            ).to_series()
            filtered_signals['exit_long'] = exit_long_filtered
        
        if 'exit_short' in signals:
            exit_short_filtered = df_with_time.select(
                pl.when(pl.col('time_only') >= exit_time)
                .then(True)
                .otherwise(signals['exit_short'])
                .alias('exit_short')
            ).to_series()
            filtered_signals['exit_short'] = exit_short_filtered
        
        # Only log significant signal filtering
        original_total = sum(int(v.sum()) for v in signals.values())
        filtered_total = sum(int(v.sum()) for v in filtered_signals.values())
        if original_total > 0 and filtered_total < original_total * 0.5:
            logger.warning(f"Intraday rules filtered out {original_total - filtered_total} signals ({((original_total - filtered_total) / original_total * 100):.1f}%)")
        logger.debug(f"Applied intraday rules: no_trade_after={no_trade_after}, exit_all_at={exit_all_at}")
        return filtered_signals
    
    def get_signals_for_strategy(self, df: pl.DataFrame, strategy_name: str, 
                               apply_intraday: bool = True) -> Optional[Dict[str, pl.Series]]:
        """Get signals for a specific strategy by name"""
        strategy = next((s for s in self.strategies if s.get('name') == strategy_name), None)
        if not strategy:
            logger.error(f"Strategy '{strategy_name}' not found")
            return None
        
        signals = self.generate_signals(df, strategy)
        
        if apply_intraday:
            signals = self.apply_intraday_rules(df, signals, strategy)
        
        return signals
    
    def get_strategy_by_name(self, strategy_name: str) -> Optional[Dict[str, Any]]:
        """Get strategy configuration by name"""
        return next((s for s in self.strategies if s.get('name') == strategy_name), None)


# Convenience functions for backward compatibility
def load_strategies() -> List[Dict[str, Any]]:
    """Load strategies using SignalAgent"""
    agent = SignalAgent()
    return agent.get_strategies()

def generate_strategy_signals(df: pl.DataFrame, signal_type: str, strategy: dict) -> pl.Series:
    """Generate strategy signals using SignalAgent"""
    agent = SignalAgent()
    signals = agent.generate_signals(df, strategy, [signal_type])
    return signals.get(signal_type, pl.Series("mask", [False] * df.height))
