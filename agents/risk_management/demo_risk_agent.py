#!/usr/bin/env python3
"""
Risk Management Agent Demo
Demonstration of the Risk Management Agent capabilities

Features Demonstrated:
- Configuration and setup
- Trade validation with various scenarios
- Position sizing calculations
- Portfolio tracking
- Risk alerts and monitoring
- Integration with Signal Generation Agent
"""

import os
import sys
import asyncio
import logging
from datetime import datetime, timedelta
from typing import List

# Add parent directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

# Import modules
from risk_agent import RiskManagementAgent
from utils.risk_models import (
    TradeRequest, TradeDirection, ProductType, OrderType,
    RiskLevel, ValidationStatus
)

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class RiskAgentDemo:
    """Risk Management Agent demonstration"""
    
    def __init__(self):
        self.risk_agent = None
        self.demo_trades = []
    
    async def setup(self):
        """Setup the demo environment"""
        try:
            logger.info("[CONFIG] Setting up Risk Management Agent Demo...")
            
            # Check if config exists
            config_path = "agents/config/risk_management_config.yaml"
            if not os.path.exists(config_path):
                logger.warning(f"[WARN]  Config file not found: {config_path}")
                logger.info("Creating demo configuration...")
                await self._create_demo_config()
            
            # Initialize Risk Management Agent (without Angel One API for demo)
            self.risk_agent = RiskManagementAgent(config_path)
            
            # Mock the Angel One API for demo purposes
            await self._setup_mock_api()
            
            logger.info("[SUCCESS] Demo setup completed")
            
        except Exception as e:
            logger.error(f"[ERROR] Demo setup failed: {e}")
            raise
    
    async def _create_demo_config(self):
        """Create a demo configuration file"""
        demo_config = {
            'angel_one_api': {
                'api_key': 'DEMO_API_KEY',
                'username': 'DEMO_USER',
                'password': 'DEMO_PASS',
                'totp_token': 'DEMO_TOTP',
                'timeout': 10,
                'max_retries': 3
            },
            'capital_allocation': {
                'total_capital': 100000,
                'max_risk_per_trade_percent': 1.0,
                'max_daily_risk_percent': 5.0,
                'max_portfolio_risk_percent': 10.0,
                'position_sizing': {
                    'default_method': 'fixed_fraction',
                    'fixed_fraction': {
                        'default_percent': 2.0,
                        'max_percent': 5.0
                    }
                },
                'intraday_margin': {
                    'multiplier': 3.5,
                    'margin_buffer_percent': 10.0
                }
            },
            'pre_trade_filters': {
                'concurrent_trades': {
                    'max_total_trades': 5,
                    'max_trades_per_symbol': 2,
                    'max_trades_per_strategy': 3
                },
                'risk_reward': {
                    'min_rr_ratio': 1.5,
                    'max_stop_loss_percent': 3.0
                },
                'time_filters': {
                    'market_hours_only': False,  # Disabled for demo
                    'market_open_time': '09:20',
                    'market_close_time': '15:00'
                }
            },
            'live_supervision': {
                'drawdown_controls': {
                    'max_daily_drawdown_percent': 10.0,
                    'max_total_drawdown_percent': 15.0
                }
            },
            'logging_reporting': {
                'trade_logging': {
                    'enable': True,
                    'log_level': 'INFO'
                }
            }
        }
        
        # Save demo config
        import yaml
        os.makedirs('agents/config', exist_ok=True)
        with open('agents/config/risk_management_config.yaml', 'w') as f:
            yaml.dump(demo_config, f, indent=2)
        
        logger.info("[SUCCESS] Demo configuration created")
    
    async def _setup_mock_api(self):
        """Setup mock Angel One API for demo"""
        from unittest.mock import AsyncMock, Mock
        from utils.angel_api import MarginRequirement, FundData
        
        # Mock the Angel One API client
        mock_api = Mock()
        mock_api.authenticate = AsyncMock(return_value=True)
        mock_api.get_positions = AsyncMock(return_value=[])
        mock_api.get_funds = AsyncMock(return_value=FundData(
            available_cash=100000.0,
            utilized_margin=0.0,
            available_margin=100000.0,
            collateral_value=0.0,
            m2m_unrealized=0.0,
            m2m_realized=0.0,
            opening_balance=100000.0,
            payin=0.0,
            payout=0.0,
            timestamp=datetime.now()
        ))
        mock_api.validate_trade_margin = AsyncMock(return_value=(True, "Valid", MarginRequirement(
            symbol="DEMO",
            exchange="NSE",
            quantity=10,
            price=1000.0,
            product_type="MIS",
            transaction_type="BUY",
            margin_required=3000.0,
            available_margin=100000.0,
            is_allowed=True,
            limit_used_percent=3.0
        )))
        
        # Replace the real API with mock
        self.risk_agent.angel_api = mock_api
        
        logger.info("[SUCCESS] Mock API setup completed")
    
    def create_demo_trades(self) -> List[TradeRequest]:
        """Create demo trade requests for testing"""
        demo_trades = [
            # Valid trade
            TradeRequest(
                signal_id="DEMO_001",
                symbol="RELIANCE",
                exchange="NSE",
                strategy_name="momentum_strategy",
                direction=TradeDirection.LONG,
                entry_price=2500.0,
                stop_loss=2450.0,
                take_profit=2600.0,
                quantity=10,
                product_type=ProductType.MIS,
                order_type=OrderType.LIMIT,
                risk_amount=500.0,
                capital_allocated=25000.0,
                risk_reward_ratio=2.0,
                confidence=0.8,
                timestamp=datetime.now()
            ),
            
            # Trade with poor risk-reward ratio
            TradeRequest(
                signal_id="DEMO_002",
                symbol="TCS",
                exchange="NSE",
                strategy_name="mean_reversion",
                direction=TradeDirection.LONG,
                entry_price=3500.0,
                stop_loss=3450.0,
                take_profit=3520.0,  # Poor RR: 20/50 = 0.4
                quantity=5,
                product_type=ProductType.MIS,
                order_type=OrderType.LIMIT,
                risk_amount=250.0,
                capital_allocated=17500.0,
                risk_reward_ratio=0.4,
                confidence=0.6,
                timestamp=datetime.now()
            ),
            
            # Large position size trade
            TradeRequest(
                signal_id="DEMO_003",
                symbol="INFY",
                exchange="NSE",
                strategy_name="breakout_strategy",
                direction=TradeDirection.LONG,
                entry_price=1500.0,
                stop_loss=1450.0,
                take_profit=1650.0,
                quantity=50,  # Large quantity
                product_type=ProductType.MIS,
                order_type=OrderType.LIMIT,
                risk_amount=2500.0,
                capital_allocated=75000.0,  # 75% of capital
                risk_reward_ratio=3.0,
                confidence=0.9,
                timestamp=datetime.now()
            ),
            
            # Short trade
            TradeRequest(
                signal_id="DEMO_004",
                symbol="HDFC",
                exchange="NSE",
                strategy_name="reversal_strategy",
                direction=TradeDirection.SHORT,
                entry_price=1600.0,
                stop_loss=1650.0,
                take_profit=1500.0,
                quantity=15,
                product_type=ProductType.MIS,
                order_type=OrderType.LIMIT,
                risk_amount=750.0,
                capital_allocated=24000.0,
                risk_reward_ratio=2.0,
                confidence=0.7,
                timestamp=datetime.now()
            )
        ]
        
        return demo_trades
    
    async def demonstrate_trade_validation(self):
        """Demonstrate trade validation capabilities"""
        logger.info("\n" + "="*60)
        logger.info("[SECURITY]  TRADE VALIDATION DEMONSTRATION")
        logger.info("="*60)
        
        demo_trades = self.create_demo_trades()
        
        for i, trade in enumerate(demo_trades, 1):
            logger.info(f"\n[STATUS] Validating Trade {i}: {trade.symbol} {trade.direction.name}")
            logger.info(f"   Entry: Rs.{trade.entry_price}, SL: Rs.{trade.stop_loss}, TP: Rs.{trade.take_profit}")
            logger.info(f"   Quantity: {trade.quantity}, RR Ratio: {trade.risk_reward_ratio}")
            
            try:
                # Validate trade
                validation_result = await self.risk_agent.validate_trade(trade)
                
                if validation_result.is_valid:
                    logger.info(f"   [SUCCESS] VALIDATION PASSED - Risk Level: {validation_result.risk_level.value}")
                    logger.info(f"   [METRICS] Position Size: {validation_result.position_size_percent:.2f}% of capital")
                    
                    # Add position if valid
                    position_id = await self.risk_agent.add_position(trade, trade.entry_price)
                    if position_id:
                        logger.info(f"   📍 Position added: {position_id}")
                else:
                    logger.info(f"   [ERROR] VALIDATION FAILED: {validation_result.rejection_reason}")
                    logger.info(f"   🚨 Risk Level: {validation_result.risk_level.value}")
                
                # Show passed and failed checks
                if validation_result.passed_checks:
                    logger.info(f"   [SUCCESS] Passed: {', '.join(validation_result.passed_checks[:2])}")
                if validation_result.failed_checks:
                    logger.info(f"   [ERROR] Failed: {', '.join(validation_result.failed_checks[:2])}")
                
            except Exception as e:
                logger.error(f"   [ERROR] Validation error: {e}")
    
    async def demonstrate_position_management(self):
        """Demonstrate position management"""
        logger.info("\n" + "="*60)
        logger.info("[METRICS] POSITION MANAGEMENT DEMONSTRATION")
        logger.info("="*60)
        
        # Get current positions
        active_positions = list(self.risk_agent.active_positions.values())
        
        if not active_positions:
            logger.info("No active positions to manage")
            return
        
        logger.info(f"[STATUS] Managing {len(active_positions)} active positions:")
        
        for position in active_positions[:2]:  # Manage first 2 positions
            logger.info(f"\n[DEBUG] Position: {position.symbol} ({position.direction.name})")
            logger.info(f"   Entry: Rs.{position.entry_price}, Current: Rs.{position.current_price}")
            logger.info(f"   Quantity: {position.quantity}, Capital: Rs.{position.capital_allocated:,.2f}")
            
            # Simulate price movement
            if position.direction == TradeDirection.LONG:
                # Simulate profit scenario
                new_price = position.entry_price * 1.03  # 3% profit
                position.current_price = new_price
                position.unrealized_pnl = (new_price - position.entry_price) * position.quantity
            else:
                # Simulate profit scenario for short
                new_price = position.entry_price * 0.97  # 3% profit
                position.current_price = new_price
                position.unrealized_pnl = (position.entry_price - new_price) * position.quantity
            
            logger.info(f"   [METRICS] Price updated to: Rs.{position.current_price:.2f}")
            logger.info(f"   [MONEY] Unrealized PnL: Rs.{position.unrealized_pnl:.2f}")
            
            # Close position if profitable
            if position.unrealized_pnl > 0:
                success = await self.risk_agent.close_position(
                    position.position_id, position.current_price, "take_profit"
                )
                if success:
                    logger.info(f"   [SUCCESS] Position closed with profit: Rs.{position.unrealized_pnl:.2f}")
    
    async def demonstrate_portfolio_metrics(self):
        """Demonstrate portfolio metrics"""
        logger.info("\n" + "="*60)
        logger.info("[STATUS] PORTFOLIO METRICS DEMONSTRATION")
        logger.info("="*60)
        
        # Get performance metrics
        metrics = await self.risk_agent.get_performance_metrics()
        
        # Portfolio metrics
        portfolio = metrics.get('portfolio', {})
        logger.info(f"[MONEY] Total Capital: Rs.{portfolio.get('total_capital', 0):,.2f}")
        logger.info(f"💵 Available Capital: Rs.{portfolio.get('available_capital', 0):,.2f}")
        logger.info(f"[METRICS] Total PnL: Rs.{portfolio.get('total_pnl', 0):,.2f}")
        logger.info(f"📉 Current Drawdown: {portfolio.get('current_drawdown', 0):.2f}%")
        
        # Position metrics
        positions = metrics.get('positions', {})
        logger.info(f"📍 Total Positions: {positions.get('total_positions', 0)}")
        logger.info(f"[METRICS] Long Positions: {positions.get('long_positions', 0)}")
        logger.info(f"📉 Short Positions: {positions.get('short_positions', 0)}")
        
        # Risk metrics
        risk = metrics.get('risk_metrics', {})
        logger.info(f"[WARN]  Daily Risk Amount: Rs.{risk.get('daily_risk_amount', 0):,.2f}")
        logger.info(f"🚨 Active Alerts: {risk.get('active_alerts', 0)}")
        
        # Performance metrics
        performance = metrics.get('performance', {})
        logger.info(f"[STATUS] Total Validations: {performance.get('total_validations', 0)}")
        logger.info(f"[SUCCESS] Passed Validations: {performance.get('passed_validations', 0)}")
        logger.info(f"[ERROR] Failed Validations: {performance.get('failed_validations', 0)}")
    
    async def run_demo(self):
        """Run the complete demonstration"""
        try:
            logger.info("[INIT] Starting Risk Management Agent Demo")
            
            # Setup
            await self.setup()
            
            # Demonstrate features
            await self.demonstrate_trade_validation()
            await self.demonstrate_position_management()
            await self.demonstrate_portfolio_metrics()
            
            logger.info("\n" + "="*60)
            logger.info("[SUCCESS] Risk Management Agent Demo Completed Successfully!")
            logger.info("="*60)
            
        except Exception as e:
            logger.error(f"[ERROR] Demo failed: {e}")
            raise

async def main():
    """Main demo function"""
    demo = RiskAgentDemo()
    await demo.run_demo()

if __name__ == "__main__":
    # Run the demo
    asyncio.run(main())
