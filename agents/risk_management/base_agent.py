#!/usr/bin/env python3
"""
BASE AGENT CLASS
Abstract base class for all trading system agents

Features:
- Common agent interface and lifecycle management
- Event-driven communication
- Logging and error handling
- Configuration management
- Health monitoring
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Any, Dict, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class AgentStatus:
    """Agent status information"""
    name: str
    running: bool
    initialized: bool
    last_activity: datetime
    error_count: int
    message_count: int

class BaseAgent(ABC):
    """
    Abstract base class for all trading system agents
    
    Provides common functionality:
    - Lifecycle management (initialize, start, stop)
    - Event bus communication
    - Health monitoring
    - Error handling
    """
    
    def __init__(self, name: str, event_bus: Any, config: Any, session_id: str):
        """Initialize base agent"""
        self.name = name
        self.event_bus = event_bus
        self.config = config
        self.session_id = session_id
        
        # Agent state
        self.running = False
        self.initialized = False
        self.last_activity = datetime.now()
        self.error_count = 0
        self.message_count = 0
        
        # Logger for this agent
        self.logger = logging.getLogger(f"{__name__}.{name}")
        
        self.logger.info(f"[INIT] {name} agent created")
    
    @abstractmethod
    async def initialize(self) -> bool:
        """Initialize the agent - must be implemented by subclasses"""
        pass
    
    @abstractmethod
    async def start(self):
        """Start the agent - must be implemented by subclasses"""
        pass
    
    @abstractmethod
    async def stop(self):
        """Stop the agent - must be implemented by subclasses"""
        pass
    
    def get_status(self) -> AgentStatus:
        """Get current agent status"""
        return AgentStatus(
            name=self.name,
            running=self.running,
            initialized=self.initialized,
            last_activity=self.last_activity,
            error_count=self.error_count,
            message_count=self.message_count
        )
    
    def update_activity(self):
        """Update last activity timestamp"""
        self.last_activity = datetime.now()
    
    def increment_error_count(self):
        """Increment error counter"""
        self.error_count += 1
    
    def increment_message_count(self):
        """Increment message counter"""
        self.message_count += 1
        self.update_activity()
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check"""
        try:
            status = self.get_status()
            
            # Calculate time since last activity
            time_since_activity = (datetime.now() - self.last_activity).total_seconds()
            
            # Determine health status
            is_healthy = (
                self.initialized and
                self.running and
                time_since_activity < 300 and  # Less than 5 minutes since last activity
                self.error_count < 10  # Less than 10 errors
            )
            
            return {
                "agent": self.name,
                "healthy": is_healthy,
                "status": status,
                "time_since_activity": time_since_activity,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"[ERROR] Health check failed: {e}")
            return {
                "agent": self.name,
                "healthy": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    def log_info(self, message: str):
        """Log info message with agent context"""
        self.logger.info(f"[{self.name}] {message}")
        self.update_activity()
    
    def log_warning(self, message: str):
        """Log warning message with agent context"""
        self.logger.warning(f"[{self.name}] {message}")
        self.update_activity()
    
    def log_error(self, message: str):
        """Log error message with agent context"""
        self.logger.error(f"[{self.name}] {message}")
        self.increment_error_count()
        self.update_activity()
    
    def log_debug(self, message: str):
        """Log debug message with agent context"""
        self.logger.debug(f"[{self.name}] {message}")
        self.update_activity()