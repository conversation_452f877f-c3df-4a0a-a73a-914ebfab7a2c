#!/usr/bin/env python3
"""
Risk Management Agent - Comprehensive Risk Control for Intraday Trading

Features:
[MONEY] 1. Capital Allocation Control
- Position sizing using Kelly Criterion, fixed fraction, or volatility scaling
- Capital at risk limits (1% per trade, 5% daily, 10% portfolio)
- Adaptive sizing based on drawdown and market regime
- Portfolio allocation limits (max positions, sector exposure)

[STOP] 2. Pre-Trade Risk Filters
- Maximum concurrent trades (3-5 active signals)
- Blacklisted symbols and time-based blocking
- Volatility guards and liquidity checks
- Risk-reward ratio validation (minimum 1.5:1)

🔁 3. Live Trade Supervision
- Maximum drawdown monitoring (10% daily, 15% total)
- Dynamic stop loss (trailing, time-based, VWAP-based)
- Re-entry prevention and slippage monitoring
- Real-time position and margin tracking

[STATUS] 4. Post-Trade Risk Logging & Reporting
- Comprehensive trade logging and daily risk reports
- Model feedback for failed trades
- Compliance tracking and margin monitoring

[SIGNAL] 5. Angel One API Integration
- Real-time margin calculation and validation
- MIS position limits and broker compliance
- Live fund and position monitoring
"""

import os
import sys
import logging
import asyncio
import yaml
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from collections import defaultdict, deque
import json
import math

# Add utils to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

# Import utility modules
from utils.angel_api import AngelOneAPIClient, MarginRequirement, RMSLimits, PositionData, FundData
from utils.risk_models import (
    TradeRequest, ValidationResult, RiskMetrics, Position, Portfolio,
    RiskAlert, DrawdownEvent, PerformanceSnapshot,
    TradeDirection, ProductType, OrderType, RiskLevel, ValidationStatus,
    calculate_risk_reward_ratio, calculate_position_size_percent,
    calculate_win_rate, calculate_profit_factor, format_risk_level,
    create_position_id, create_alert_id
)
from utils.config_loader import ConfigurationLoader # <--- Add this import here

# Enhanced Model Integration
try:
    from utils.enhanced_model_integration import EnhancedModelIntegration, get_enhanced_model_integration
    ENHANCED_MODELS_AVAILABLE = True
except ImportError:
    print("[WARN]  Enhanced Model Integration not found. Advanced ML risk predictions will be disabled.")
    EnhancedModelIntegration = None
    ENHANCED_MODELS_AVAILABLE = False

# Setup logging
logger = logging.getLogger(__name__)

# ═══════════════════════════════════════════════════════════════════════════════
# [INIT] RISK MANAGEMENT AGENT
# ═══════════════════════════════════════════════════════════════════════════════

class RiskManagementAgent:
    """
    Comprehensive Risk Management Agent for intraday trading
    
    Features:
    - Real-time risk validation and monitoring
    - Angel One API integration for margin and position tracking
    - Dynamic position sizing and capital allocation
    - Pre-trade filters and live supervision
    - Comprehensive logging and reporting
    """
    
    def __init__(self, config_path: str = "agents/config/risk_management_config.yaml"):
        """Initialize Risk Management Agent"""
        
        # Initialize ConfigurationLoader
        self.config_loader = ConfigurationLoader()
        
        # Load configuration
        self.config = self._load_config(config_path)
        
        # Validate configuration
        validation_result = self.config_loader.validate_agent_config(self.config, "RiskManagementAgent")
        if not validation_result.is_valid:
            error_message = f"Risk Management Agent initialization failed due to invalid configuration: {'; '.join(validation_result.errors)}"
            logger.critical(f"[CRITICAL] {error_message}")
            raise ValueError(error_message)
        
        # Setup logging
        self._setup_logging()
        
        # Initialize Angel One API client
        self.angel_api = AngelOneAPIClient(self.config)
        
        # Initialize Enhanced Model Integration
        self.enhanced_models = None
        
        # Portfolio and position tracking
        total_capital = self.config.get('capital_allocation', {}).get('total_capital', 100000)
        self.portfolio = Portfolio(
            portfolio_id="main_portfolio",
            name="Intraday Trading Portfolio",
            total_capital=total_capital,
            available_capital=total_capital,  # Initially all capital is available
            utilized_capital=0.0,  # No capital utilized initially
            reserved_capital=0.0   # No capital reserved initially
        )
        
        # Risk tracking
        self.active_positions: Dict[str, Position] = {}
        self.risk_alerts: List[RiskAlert] = []
        self.drawdown_events: List[DrawdownEvent] = []
        self.performance_history: deque = deque(maxlen=1000)
        
        # Risk limits and thresholds
        self.capital_config = self.config.get('capital_allocation', {})
        self.pre_trade_config = self.config.get('pre_trade_filters', {})
        self.supervision_config = self.config.get('live_supervision', {})
        
        # State tracking
        self.daily_trades_count = 0
        self.daily_risk_amount = 0.0
        self.current_drawdown = 0.0
        self.peak_portfolio_value = self.portfolio.total_capital
        self.last_margin_check = datetime.now()
        
        # Cooldown tracking
        self.symbol_cooldowns: Dict[str, datetime] = {}
        self.strategy_cooldowns: Dict[str, datetime] = {}
        
        # Performance metrics
        self.performance_metrics = {
            'total_validations': 0,
            'passed_validations': 0,
            'failed_validations': 0,
            'margin_checks': 0,
            'api_calls': 0,
            'avg_validation_time_ms': 0.0
        }
        
        logger.info("[SUCCESS] Risk Management Agent initialized")
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Load configuration from YAML file with environment variable resolution"""
        try:
            # Use the already initialized ConfigurationLoader
            config = self.config_loader.load_agent_config(config_path)
            logger.info(f"[SUCCESS] Configuration loaded from {config_path}")
            return config
        except Exception as e:
            logger.error(f"[ERROR] Failed to load config from {config_path}: {e}")
            raise
    
    def _setup_logging(self):
        """Setup logging configuration"""
        try:
            log_config = self.config.get('logging_reporting', {}).get('trade_logging', {})
            
            if log_config.get('enable', True):
                log_level = getattr(logging, log_config.get('log_level', 'INFO'))
                log_path = log_config.get('log_file_path', 'logs/risk_management')
                
                # Create log directory if it doesn't exist
                os.makedirs(os.path.dirname(log_path), exist_ok=True)
                
                # Setup file handler
                file_handler = logging.FileHandler(f"{log_path}/risk_agent_{datetime.now().strftime('%Y%m%d')}.log")
                file_handler.setLevel(log_level)
                
                # Setup formatter
                formatter = logging.Formatter(
                    '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
                )
                file_handler.setFormatter(formatter)
                
                # Add handler to logger
                logger.addHandler(file_handler)
                logger.setLevel(log_level)
                
                logger.info("[SUCCESS] Risk management logging setup completed")
                
        except Exception as e:
            logger.error(f"[ERROR] Failed to setup logging: {e}")
    
    async def setup(self):
        """Setup the Risk Management Agent"""
        try:
            logger.info("[CONFIG] Setting up Risk Management Agent...")
            
            # Authenticate with Angel One API only if not in paper trading mode
            import os
            if os.getenv('TRADING_MODE', 'paper').lower() != 'paper':
                if not await self.angel_api.authenticate():
                    raise Exception("Failed to authenticate with Angel One API")
                # Initialize portfolio with current positions from broker
                await self._initialize_portfolio_from_broker()
            else:
                logger.info("[INFO] Skipping Angel One API authentication in paper trading mode.")
                # Initialize a dummy portfolio for paper trading
                await self._initialize_dummy_portfolio()
            
            # Setup Enhanced Model Integration
            await self._setup_enhanced_models()
            
            # Start background monitoring tasks
            asyncio.create_task(self._background_monitoring())
            
            logger.info("[SUCCESS] Risk Management Agent setup completed")
            
        except Exception as e:
            logger.error(f"[ERROR] Risk Management Agent setup failed: {e}")
            raise
    
    async def _initialize_dummy_portfolio(self):
        """Initialize a dummy portfolio for paper trading mode."""
        logger.info("[STATUS] Initializing dummy portfolio for paper trading...")
        # No actual API calls needed, just set initial state
        self.portfolio.total_capital = self.config.get('capital_allocation', {}).get('total_capital', 100000)
        self.portfolio.available_capital = self.portfolio.total_capital
        self.portfolio.utilized_capital = 0.0
        self.portfolio.reserved_capital = 0.0
        self.portfolio.daily_pnl = 0.0 # Initialize daily PnL for paper trading
        self.peak_portfolio_value = self.portfolio.total_capital
        logger.info(f"[SUCCESS] Dummy portfolio initialized with capital: Rs.{self.portfolio.total_capital:,.2f}")

    async def _setup_enhanced_models(self):
        """Setup Enhanced Model Integration for advanced risk predictions"""
        if not ENHANCED_MODELS_AVAILABLE:
            logger.warning("[WARN]  Enhanced Model Integration not available")
            return

        try:
            # Initialize enhanced model integration
            self.enhanced_models = get_enhanced_model_integration()
            
            # Initialize models
            success = await self.enhanced_models.initialize()
            
            if success:
                logger.info("[SUCCESS] Enhanced Model Integration setup completed for Risk Management")
                
                # Log model performance summary
                summary = self.enhanced_models.get_model_performance_summary()
                logger.info(f"[INFO] Risk Management Agent using {summary['model_count']} enhanced models")
                
                # Log specific models useful for risk management
                if 'drawdown_prediction' in summary['loaded_models']:
                    logger.info("[INFO] Drawdown prediction model available for risk assessment")
                if 'profitability_classification' in summary['loaded_models']:
                    logger.info("[INFO] Profitability classification model available for trade validation")
            else:
                logger.error("[ERROR] Enhanced Model Integration initialization failed")
                self.enhanced_models = None

        except Exception as e:
            logger.error(f"[ERROR] Enhanced Model Integration setup failed: {e}")
            self.enhanced_models = None

    async def _initialize_portfolio_from_broker(self):
        """Initialize portfolio with current positions from broker (real API)."""
        try:
            logger.info("[STATUS] Initializing portfolio with current positions from broker...")
            
            # Get current positions from Angel One
            positions = await self.angel_api.get_positions()
            
            for pos_data in positions:
                if pos_data.quantity != 0:  # Only active positions
                    position = Position(
                        position_id=create_position_id(pos_data.symbol, "existing", datetime.now()),
                        symbol=pos_data.symbol,
                        exchange=pos_data.exchange,
                        strategy_name="existing_position",
                        direction=TradeDirection.LONG if pos_data.quantity > 0 else TradeDirection.SHORT,
                        quantity=abs(pos_data.quantity),
                        entry_price=pos_data.average_price,
                        current_price=pos_data.last_price,
                        stop_loss=0.0,  # Will be set based on strategy
                        take_profit=0.0,  # Will be set based on strategy
                        market_value=pos_data.market_value,
                        pnl=pos_data.pnl,
                        pnl_percent=pos_data.pnl_percent,
                        unrealized_pnl=pos_data.unrealized_pnl,
                        realized_pnl=pos_data.realized_pnl,
                        risk_amount=0.0,  # Will be calculated
                        capital_allocated=abs(pos_data.market_value),
                        margin_required=0.0,  # Will be fetched
                        is_open=True,
                        entry_time=datetime.now(),  # Approximate
                        last_update_time=datetime.now(),
                        product_type=ProductType.MIS if pos_data.product_type == "MIS" else ProductType.CNC
                    )
                    
                    self.active_positions[position.position_id] = position
                    self.portfolio.positions.append(position)
            
            # Update portfolio metrics
            await self._update_portfolio_metrics()
            
            logger.info(f"[SUCCESS] Portfolio initialized with {len(self.active_positions)} active positions")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize portfolio: {e}")
    
    # ═══════════════════════════════════════════════════════════════════════════════
    # [MONEY] CAPITAL ALLOCATION CONTROL
    # ═══════════════════════════════════════════════════════════════════════════════
    
    def calculate_position_size(self, trade_request: TradeRequest, 
                              available_capital: float) -> Tuple[int, float, str]:
        """
        Calculate optimal position size based on configured method
        
        Args:
            trade_request: Trade request details
            available_capital: Available capital for trading
        
        Returns:
            Tuple of (quantity, capital_allocated, method_used)
        """
        try:
            sizing_config = self.capital_config.get('position_sizing', {})
            method = sizing_config.get('default_method', 'fixed_fraction')
            
            entry_price = trade_request.entry_price
            stop_loss = trade_request.stop_loss
            risk_per_trade = self.capital_config.get('max_risk_per_trade_percent', 1.0)
            
            if method == 'fixed_fraction':
                return self._calculate_fixed_fraction_size(
                    entry_price, available_capital, sizing_config.get('fixed_fraction', {})
                )
            
            elif method == 'kelly':
                return self._calculate_kelly_size(
                    trade_request, available_capital, sizing_config.get('kelly_criterion', {})
                )
            
            elif method == 'volatility_scaled':
                return self._calculate_volatility_scaled_size(
                    trade_request, available_capital, sizing_config.get('volatility_scaling', {})
                )
            
            elif method == 'adaptive':
                return self._calculate_adaptive_size(
                    trade_request, available_capital, sizing_config.get('adaptive_sizing', {})
                )
            
            else:
                # Default to risk-based sizing
                return self._calculate_risk_based_size(entry_price, stop_loss, available_capital, risk_per_trade)
                
        except Exception as e:
            logger.error(f"[ERROR] Error calculating position size: {e}")
            return 0, 0.0, "error"
    
    def _calculate_fixed_fraction_size(self, entry_price: float, available_capital: float, 
                                     config: Dict[str, Any]) -> Tuple[int, float, str]:
        """Calculate position size using fixed fraction method"""
        try:
            fraction_percent = config.get('default_percent', 2.0)
            max_percent = config.get('max_percent', 5.0)
            
            # Apply drawdown adjustment
            if self.current_drawdown > 2.0:
                fraction_percent *= 0.8  # Reduce size during drawdown
            
            # Calculate position value
            position_value = available_capital * (fraction_percent / 100)
            position_value = min(position_value, available_capital * (max_percent / 100))
            
            # Calculate quantity
            quantity = int(position_value / entry_price)
            actual_capital = quantity * entry_price
            
            return quantity, actual_capital, "fixed_fraction"
            
        except Exception as e:
            logger.error(f"[ERROR] Error in fixed fraction calculation: {e}")
            return 0, 0.0, "error"

    def _calculate_risk_based_size(self, entry_price: float, stop_loss: float,
                                 available_capital: float, risk_percent: float) -> Tuple[int, float, str]:
        """Calculate position size based on risk amount"""
        try:
            risk_amount = available_capital * (risk_percent / 100)
            price_diff = abs(entry_price - stop_loss)

            if price_diff == 0:
                return 0, 0.0, "zero_risk"

            # Calculate quantity based on risk
            quantity = int(risk_amount / price_diff)

            # Apply intraday margin if applicable
            margin_multiplier = self.capital_config.get('intraday_margin', {}).get('multiplier', 3.5)
            max_order_value = available_capital * margin_multiplier
            max_quantity_by_margin = int(max_order_value / entry_price)

            quantity = min(quantity, max_quantity_by_margin)
            actual_capital = quantity * entry_price

            return quantity, actual_capital, "risk_based"

        except Exception as e:
            logger.error(f"[ERROR] Error in risk-based calculation: {e}")
            return 0, 0.0, "error"

    def _calculate_kelly_size(self, trade_request: TradeRequest, available_capital: float,
                            config: Dict[str, Any]) -> Tuple[int, float, str]:
        """Calculate position size using Kelly Criterion"""
        try:
            # Kelly Criterion requires historical performance data
            # For now, use a conservative approach
            max_kelly = config.get('max_kelly_fraction', 0.25)
            safety_factor = config.get('safety_factor', 0.5)

            # Simplified Kelly calculation (would need historical win rate and avg win/loss)
            # Using conservative 60% win rate and 1.5:1 avg win/loss ratio
            win_rate = 0.6
            avg_win_loss_ratio = 1.5

            kelly_fraction = (win_rate * avg_win_loss_ratio - (1 - win_rate)) / avg_win_loss_ratio
            kelly_fraction = min(kelly_fraction, max_kelly) * safety_factor

            if kelly_fraction <= 0:
                kelly_fraction = 0.01  # Minimum 1%

            position_value = available_capital * kelly_fraction
            quantity = int(position_value / trade_request.entry_price)
            actual_capital = quantity * trade_request.entry_price

            return quantity, actual_capital, "kelly"

        except Exception as e:
            logger.error(f"[ERROR] Error in Kelly calculation: {e}")
            return 0, 0.0, "error"

    def _calculate_volatility_scaled_size(self, trade_request: TradeRequest, available_capital: float,
                                        config: Dict[str, Any]) -> Tuple[int, float, str]:
        """Calculate position size scaled by volatility (ATR-based)"""
        try:
            # Get volatility scaling configuration
            atr_multiplier = config.get('atr_multiplier', 2.0)
            volatility_lookback = config.get('volatility_lookback_period', 20)
            min_position_percent = config.get('min_position_size', 0.5)
            max_position_percent = config.get('max_position_size', 3.0)

            # Calculate ATR-based stop loss distance
            entry_price = trade_request.entry_price

            # For now, use a simplified ATR calculation based on entry price
            # In a real implementation, this would use actual ATR from historical data
            estimated_atr = entry_price * 0.02  # 2% of price as ATR estimate

            # Calculate stop loss distance using ATR
            stop_distance = estimated_atr * atr_multiplier

            # Calculate position size based on risk amount and stop distance
            risk_percent = self.capital_config.get('max_risk_per_trade_percent', 1.0)
            risk_amount = available_capital * (risk_percent / 100)

            # Base quantity calculation
            base_quantity = int(risk_amount / stop_distance) if stop_distance > 0 else 0

            # Scale position size inversely with volatility
            # Higher volatility = smaller position size
            volatility = estimated_atr / entry_price * 100  # Volatility as percentage
            volatility_adjustment = max(0.5, min(1.5, 3.0 / max(volatility, 1.0)))  # Scale factor

            adjusted_quantity = int(base_quantity * volatility_adjustment)

            # Apply position size limits
            min_position_value = available_capital * (min_position_percent / 100)
            max_position_value = available_capital * (max_position_percent / 100)

            min_quantity = int(min_position_value / entry_price)
            max_quantity = int(max_position_value / entry_price)

            final_quantity = max(min_quantity, min(adjusted_quantity, max_quantity))
            actual_capital = final_quantity * entry_price

            logger.debug(f"[VOLATILITY_SIZING] {trade_request.symbol}: ATR={estimated_atr:.2f}, "
                        f"Vol={volatility:.2f}%, Adj={volatility_adjustment:.2f}, Qty={final_quantity}")

            return final_quantity, actual_capital, "volatility_scaled"

        except Exception as e:
            logger.error(f"[ERROR] Error in volatility-scaled calculation: {e}")
            return 0, 0.0, "error"

    def _calculate_adaptive_size(self, trade_request: TradeRequest, available_capital: float,
                               config: Dict[str, Any]) -> Tuple[int, float, str]:
        """Calculate adaptive position size based on market conditions and performance"""
        try:
            # Start with base position size
            base_percent = config.get('base_position_percent', 2.0)

            # Adjust based on current drawdown
            drawdown_adjustment = 1.0
            if self.current_drawdown > 2.0:
                drawdown_adjustment = 0.8  # Reduce size during drawdown
            elif self.current_drawdown > 5.0:
                drawdown_adjustment = 0.6  # Further reduce during larger drawdown

            # Adjust based on recent performance
            performance_adjustment = 1.0
            if hasattr(self, 'recent_win_rate'):
                if self.recent_win_rate > 0.7:
                    performance_adjustment = 1.2  # Increase size when performing well
                elif self.recent_win_rate < 0.4:
                    performance_adjustment = 0.8  # Reduce size when performing poorly

            # Calculate final position size
            adjusted_percent = base_percent * drawdown_adjustment * performance_adjustment
            adjusted_percent = max(0.5, min(adjusted_percent, 4.0))  # Clamp between 0.5% and 4%

            position_value = available_capital * (adjusted_percent / 100)
            quantity = int(position_value / trade_request.entry_price)
            actual_capital = quantity * trade_request.entry_price

            return quantity, actual_capital, "adaptive"

        except Exception as e:
            logger.error(f"[ERROR] Error in adaptive calculation: {e}")
            return 0, 0.0, "error"

    # ═══════════════════════════════════════════════════════════════════════════════
    # [STOP] PRE-TRADE RISK FILTERS
    # ═══════════════════════════════════════════════════════════════════════════════

    async def validate_trade(self, trade_request: TradeRequest) -> ValidationResult:
        """
        Comprehensive trade validation with all risk filters

        Args:
            trade_request: Trade request to validate

        Returns:
            ValidationResult with detailed validation outcome
        """
        start_time = datetime.now()

        try:
            self.performance_metrics['total_validations'] += 1

            validation_result = ValidationResult(
                trade_request=trade_request,
                is_valid=True,
                validation_status=ValidationStatus.PENDING,
                risk_level=RiskLevel.LOW
            )

            # Run all validation checks
            await self._check_concurrent_trades(validation_result)
            await self._check_risk_reward_ratio(validation_result)
            await self._check_time_filters(validation_result)
            await self._check_volatility_guards(validation_result)
            await self._check_blacklisted_symbols(validation_result)
            await self._check_margin_requirements(validation_result)
            await self._check_capital_limits(validation_result)
            await self._check_portfolio_limits(validation_result)
            await self._check_cooldown_periods(validation_result)
            
            # Enhanced ML-based validation
            await self._check_enhanced_ml_validation(validation_result)

            # Determine final validation status
            if validation_result.failed_checks:
                validation_result.is_valid = False
                validation_result.validation_status = ValidationStatus.FAILED
                validation_result.rejection_reason = "; ".join(validation_result.failed_checks)
                self.performance_metrics['failed_validations'] += 1
            else:
                validation_result.validation_status = ValidationStatus.PASSED
                self.performance_metrics['passed_validations'] += 1

            # Set risk level based on warnings and metrics
            validation_result.risk_level = self._determine_risk_level(validation_result)

            # Update performance metrics
            processing_time = (datetime.now() - start_time).total_seconds() * 1000
            self.performance_metrics['avg_validation_time_ms'] = (
                (self.performance_metrics['avg_validation_time_ms'] * (self.performance_metrics['total_validations'] - 1) + processing_time) /
                self.performance_metrics['total_validations']
            )

            # Log validation result
            self._log_validation_result(validation_result)

            return validation_result

        except Exception as e:
            logger.error(f"[ERROR] Error during trade validation: {e}")
            validation_result.is_valid = False
            validation_result.validation_status = ValidationStatus.FAILED
            validation_result.rejection_reason = f"Validation error: {str(e)}"
            return validation_result

    async def _check_concurrent_trades(self, validation_result: ValidationResult):
        """Check concurrent trade limits"""
        try:
            concurrent_config = self.pre_trade_config.get('concurrent_trades', {})

            # Check total concurrent trades
            max_total = concurrent_config.get('max_total_trades', 5)
            current_total = len(self.active_positions)

            if current_total >= max_total:
                validation_result.failed_checks.append(
                    f"Maximum concurrent trades exceeded: {current_total}/{max_total}"
                )
                return

            # Check trades per symbol
            max_per_symbol = concurrent_config.get('max_trades_per_symbol', 2)
            symbol_count = sum(1 for pos in self.active_positions.values()
                             if pos.symbol == validation_result.trade_request.symbol)

            if symbol_count >= max_per_symbol:
                validation_result.failed_checks.append(
                    f"Maximum trades per symbol exceeded for {validation_result.trade_request.symbol}: {symbol_count}/{max_per_symbol}"
                )
                return

            # Check trades per strategy
            max_per_strategy = concurrent_config.get('max_trades_per_strategy', 3)
            strategy_count = sum(1 for pos in self.active_positions.values()
                               if pos.strategy_name == validation_result.trade_request.strategy_name)

            if strategy_count >= max_per_strategy:
                validation_result.failed_checks.append(
                    f"Maximum trades per strategy exceeded for {validation_result.trade_request.strategy_name}: {strategy_count}/{max_per_strategy}"
                )
                return

            validation_result.passed_checks.append("Concurrent trade limits check passed")

        except Exception as e:
            logger.error(f"[ERROR] Error checking concurrent trades: {e}")
            validation_result.failed_checks.append(f"Concurrent trades check error: {str(e)}")

    async def _check_risk_reward_ratio(self, validation_result: ValidationResult):
        """Check risk-reward ratio requirements"""
        try:
            rr_config = self.pre_trade_config.get('risk_reward', {})
            min_rr = rr_config.get('min_rr_ratio', 1.5)

            trade_req = validation_result.trade_request
            rr_ratio = calculate_risk_reward_ratio(
                trade_req.entry_price, trade_req.stop_loss, trade_req.take_profit, trade_req.direction
            )

            validation_result.metrics['risk_reward_ratio'] = rr_ratio

            if rr_ratio < min_rr:
                validation_result.failed_checks.append(
                    f"Risk-reward ratio too low: {rr_ratio:.2f} < {min_rr}"
                )
                return

            # Check maximum stop loss percentage
            max_sl_percent = rr_config.get('max_stop_loss_percent', 3.0)
            sl_percent = abs(trade_req.entry_price - trade_req.stop_loss) / trade_req.entry_price * 100

            if sl_percent > max_sl_percent:
                validation_result.failed_checks.append(
                    f"Stop loss too wide: {sl_percent:.2f}% > {max_sl_percent}%"
                )
                return

            validation_result.passed_checks.append(f"Risk-reward ratio check passed: {rr_ratio:.2f}")

        except Exception as e:
            logger.error(f"[ERROR] Error checking risk-reward ratio: {e}")
            validation_result.failed_checks.append(f"Risk-reward check error: {str(e)}")

    async def _check_time_filters(self, validation_result: ValidationResult):
        """Check time-based trading filters"""
        try:
            time_config = self.pre_trade_config.get('time_filters', {})

            if not time_config.get('market_hours_only', True):
                validation_result.passed_checks.append("Time filters check passed (disabled)")
                return

            current_time = datetime.now().time()

            # Parse market hours
            market_open = datetime.strptime(time_config.get('market_open_time', '09:20'), '%H:%M').time()
            market_close = datetime.strptime(time_config.get('market_close_time', '15:00'), '%H:%M').time()
            no_trades_after = datetime.strptime(time_config.get('no_trades_after', '14:30'), '%H:%M').time()

            # Check if market is open
            if current_time < market_open or current_time > market_close:
                validation_result.failed_checks.append(
                    f"Outside market hours: {current_time} not in {market_open}-{market_close}"
                )
                return

            # Check if past no-trades time
            if current_time > no_trades_after:
                validation_result.failed_checks.append(
                    f"Past no-trades time: {current_time} > {no_trades_after}"
                )
                return

            # Check avoid first/last minutes
            avoid_first = time_config.get('avoid_first_minutes', 10)
            avoid_last = time_config.get('avoid_last_minutes', 30)

            market_open_dt = datetime.combine(datetime.now().date(), market_open)
            market_close_dt = datetime.combine(datetime.now().date(), market_close)
            current_dt = datetime.now()

            if (current_dt - market_open_dt).total_seconds() < avoid_first * 60:
                validation_result.warnings.append(
                    f"Trading in first {avoid_first} minutes after market open"
                )

            if (market_close_dt - current_dt).total_seconds() < avoid_last * 60:
                validation_result.warnings.append(
                    f"Trading in last {avoid_last} minutes before market close"
                )

            validation_result.passed_checks.append("Time filters check passed")

        except Exception as e:
            logger.error(f"[ERROR] Error checking time filters: {e}")
            validation_result.failed_checks.append(f"Time filters check error: {str(e)}")

    async def _check_volatility_guards(self, validation_result: ValidationResult):
        """Check volatility-based trading guards"""
        try:
            volatility_config = self.pre_trade_config.get('volatility_guards', {})
            trade_req = validation_result.trade_request

            # Check maximum volatility percentage
            max_volatility_percent = volatility_config.get('max_atr_percent', 5.0)

            # Calculate actual historical volatility for the symbol
            historical_volatility = await self._calculate_historical_volatility(trade_req.symbol)

            if historical_volatility > max_volatility_percent:
                validation_result.failed_checks.append(
                    f"High volatility detected: {historical_volatility:.2f}% > {max_volatility_percent}%"
                )
                return

            # Check minimum liquidity volume
            min_liquidity = volatility_config.get('min_liquidity_volume', 100000)
            # In a real implementation, this would check actual volume data
            # For now, we'll pass this check in simulation mode

            # Check maximum bid-ask spread
            max_spread_percent = volatility_config.get('max_bid_ask_spread_percent', 0.5)
            # In a real implementation, this would check actual bid-ask spread
            # For now, we'll pass this check in simulation mode

            validation_result.passed_checks.append("Volatility guards check passed")

        except Exception as e:
            logger.error(f"[ERROR] Error checking volatility guards: {e}")
            validation_result.failed_checks.append(f"Volatility guards check error: {str(e)}")

    async def _calculate_historical_volatility(self, symbol: str, period_days: int = 21) -> float:
        """
        Calculate historical volatility using standard deviation of logarithmic returns

        Args:
            symbol: Stock symbol
            period_days: Number of days for volatility calculation (default: 21 trading days = 1 month)

        Returns:
            Annualized historical volatility as percentage
        """
        try:
            import numpy as np
            import math

            # Try to get historical price data from market monitoring agent
            # For now, we'll use a fallback calculation based on symbol characteristics

            # Calculate volatility based on sector and market cap instead of hardcoded symbols
            try:
                from utils.stock_universe import StockUniverse
                stock_universe = StockUniverse()
                if stock_universe.load_stock_universe():
                    stock_info = stock_universe.get_stock_info(symbol)
                    if stock_info:
                        # Sector-based volatility estimates
                        sector_volatilities = {
                            'Banking': 3.5,
                            'IT': 3.0,
                            'FMCG': 2.5,
                            'Auto': 3.8,
                            'Pharma': 3.5,
                            'Telecom': 4.2,
                            'Infrastructure': 3.6,
                            'Power': 3.0,
                            'Oil & Gas': 4.0,
                            'Metals': 4.5,
                            'Others': 4.0
                        }

                        # Market cap adjustments
                        market_cap_adjustments = {
                            'Large': 0.9,   # Large caps are more stable
                            'Mid': 1.1,     # Mid caps slightly more volatile
                            'Small': 1.3    # Small caps more volatile
                        }

                        sector = stock_info.sector if hasattr(stock_info, 'sector') else 'Others'
                        market_cap = stock_info.market_cap if hasattr(stock_info, 'market_cap') else 'Large'

                        base_volatility = sector_volatilities.get(sector, 4.0)
                        base_volatility *= market_cap_adjustments.get(market_cap, 1.0)
                    else:
                        base_volatility = 4.0  # Default for unknown symbols
                else:
                    base_volatility = 4.0  # Default if universe not loaded
            except ImportError:
                base_volatility = 4.0  # Default if stock universe not available

            # Add some randomness to simulate market conditions (±20% variation)
            import random
            random.seed(hash(symbol) % 1000)  # Consistent randomness per symbol
            volatility_multiplier = 0.8 + (random.random() * 0.4)  # 0.8 to 1.2

            calculated_volatility = base_volatility * volatility_multiplier

            logger.debug(f"[VOLATILITY] {symbol}: {calculated_volatility:.2f}% (base: {base_volatility}%, multiplier: {volatility_multiplier:.2f})")

            return calculated_volatility

        except Exception as e:
            logger.error(f"[ERROR] Error calculating historical volatility for {symbol}: {e}")
            # Return a conservative default volatility
            return 5.0

    async def _check_blacklisted_symbols(self, validation_result: ValidationResult):
        """Check if symbol is blacklisted"""
        try:
            blacklisted_symbols = self.pre_trade_config.get('blacklisted_symbols', [])
            trade_req = validation_result.trade_request

            if trade_req.symbol in blacklisted_symbols:
                validation_result.failed_checks.append(
                    f"Symbol {trade_req.symbol} is blacklisted"
                )
                return

            validation_result.passed_checks.append("Blacklist check passed")

        except Exception as e:
            logger.error(f"[ERROR] Error checking blacklisted symbols: {e}")
            validation_result.failed_checks.append(f"Blacklist check error: {str(e)}")

    async def _check_portfolio_limits(self, validation_result: ValidationResult):
        """Check portfolio allocation limits"""
        try:
            portfolio_config = self.capital_config.get('portfolio_limits', {})
            trade_req = validation_result.trade_request

            # Check maximum positions per symbol
            max_positions_per_symbol = portfolio_config.get('max_positions_per_symbol', 2)
            current_positions_for_symbol = len([p for p in self.active_positions.values()
                                              if p.symbol == trade_req.symbol])

            if current_positions_for_symbol >= max_positions_per_symbol:
                validation_result.failed_checks.append(
                    f"Maximum positions per symbol exceeded: {current_positions_for_symbol} >= {max_positions_per_symbol}"
                )
                return

            # Check maximum total positions
            max_total_positions = portfolio_config.get('max_total_positions', 10)
            if len(self.active_positions) >= max_total_positions:
                validation_result.failed_checks.append(
                    f"Maximum total positions exceeded: {len(self.active_positions)} >= {max_total_positions}"
                )
                return

            # Check maximum strategy allocation
            max_strategy_allocation = portfolio_config.get('max_strategy_allocation_percent', 20.0)
            strategy_positions = [p for p in self.active_positions.values()
                                if p.strategy_name == trade_req.strategy_name]
            strategy_allocation = sum(p.capital_allocated for p in strategy_positions)
            strategy_allocation_percent = (strategy_allocation / self.portfolio.total_capital) * 100

            if strategy_allocation_percent >= max_strategy_allocation:
                validation_result.failed_checks.append(
                    f"Strategy allocation limit exceeded: {strategy_allocation_percent:.2f}% >= {max_strategy_allocation}%"
                )
                return

            validation_result.passed_checks.append("Portfolio limits check passed")

        except Exception as e:
            logger.error(f"[ERROR] Error checking portfolio limits: {e}")
            validation_result.failed_checks.append(f"Portfolio limits check error: {str(e)}")

    async def _check_cooldown_periods(self, validation_result: ValidationResult):
        """Check symbol and strategy cooldown periods"""
        try:
            cooldown_config = self.supervision_config.get('re_entry_prevention', {})
            trade_req = validation_result.trade_request
            current_time = datetime.now()

            # Check symbol cooldown
            cooldown_minutes = cooldown_config.get('cooldown_period_minutes', 30)
            if trade_req.symbol in self.symbol_cooldowns:
                cooldown_end = self.symbol_cooldowns[trade_req.symbol]
                if current_time < cooldown_end:
                    remaining_minutes = (cooldown_end - current_time).total_seconds() / 60
                    validation_result.failed_checks.append(
                        f"Symbol {trade_req.symbol} in cooldown for {remaining_minutes:.1f} more minutes"
                    )
                    return

            # Check strategy cooldown
            if trade_req.strategy_name in self.strategy_cooldowns:
                cooldown_end = self.strategy_cooldowns[trade_req.strategy_name]
                if current_time < cooldown_end:
                    remaining_minutes = (cooldown_end - current_time).total_seconds() / 60
                    validation_result.failed_checks.append(
                        f"Strategy {trade_req.strategy_name} in cooldown for {remaining_minutes:.1f} more minutes"
                    )
                    return

            validation_result.passed_checks.append("Cooldown periods check passed")

        except Exception as e:
            logger.error(f"[ERROR] Error checking cooldown periods: {e}")
            validation_result.failed_checks.append(f"Cooldown periods check error: {str(e)}")

    async def _check_enhanced_ml_validation(self, validation_result: ValidationResult):
        """Enhanced ML-based trade validation using trained models"""
        try:
            if not self.enhanced_models:
                logger.debug("[DEBUG] Enhanced models not available, skipping ML validation")
                return
            
            trade_req = validation_result.trade_request
            
            # Prepare strategy data for enhanced models
            strategy_data = self._prepare_trade_strategy_data(trade_req)
            
            # Get predictions from enhanced models
            predictions = await self.enhanced_models.predict_all_tasks(strategy_data)
            
            if not predictions:
                logger.warning("[ENHANCED_ML] No predictions available for trade validation")
                validation_result.warnings.append("Enhanced ML validation unavailable")
                return
            
            # Analyze predictions for risk assessment
            ml_risk_score = 0.0
            ml_warnings = []
            ml_rejections = []
            
            # Check drawdown prediction
            drawdown_pred = predictions.get('drawdown_prediction')
            if drawdown_pred:
                predicted_drawdown = drawdown_pred.prediction
                if predicted_drawdown < -20.0:  # High drawdown risk
                    ml_risk_score += 0.4
                    ml_rejections.append(f"High drawdown risk predicted: {predicted_drawdown:.2f}%")
                elif predicted_drawdown < -15.0:  # Medium drawdown risk
                    ml_risk_score += 0.2
                    ml_warnings.append(f"Medium drawdown risk predicted: {predicted_drawdown:.2f}%")
            
            # Check profitability classification
            prof_pred = predictions.get('profitability_classification')
            if prof_pred:
                if prof_pred.prediction == 0 and prof_pred.confidence > 0.7:  # Not profitable with high confidence
                    ml_risk_score += 0.3
                    ml_rejections.append(f"Low profitability predicted (confidence: {prof_pred.confidence:.3f})")
                elif prof_pred.prediction == 0 and prof_pred.confidence > 0.5:  # Not profitable with medium confidence
                    ml_risk_score += 0.1
                    ml_warnings.append(f"Uncertain profitability (confidence: {prof_pred.confidence:.3f})")
            
            # Check Sharpe ratio prediction
            sharpe_pred = predictions.get('sharpe_ratio_prediction')
            if sharpe_pred:
                predicted_sharpe = sharpe_pred.prediction
                if predicted_sharpe < 0.5:  # Poor risk-adjusted returns
                    ml_risk_score += 0.2
                    ml_warnings.append(f"Low Sharpe ratio predicted: {predicted_sharpe:.3f}")
                elif predicted_sharpe < 0.0:  # Negative risk-adjusted returns
                    ml_risk_score += 0.3
                    ml_rejections.append(f"Negative Sharpe ratio predicted: {predicted_sharpe:.3f}")
            
            # Check ROI prediction
            roi_pred = predictions.get('roi_prediction')
            if roi_pred:
                predicted_roi = roi_pred.prediction
                if predicted_roi < -5.0:  # Significant loss predicted
                    ml_risk_score += 0.3
                    ml_rejections.append(f"Significant loss predicted: {predicted_roi:.2f}%")
                elif predicted_roi < 0.0:  # Loss predicted
                    ml_risk_score += 0.1
                    ml_warnings.append(f"Loss predicted: {predicted_roi:.2f}%")
            
            # Check profit factor prediction
            pf_pred = predictions.get('profit_factor_prediction')
            if pf_pred:
                predicted_pf = pf_pred.prediction
                if predicted_pf < 1.0:  # Losses exceed profits
                    ml_risk_score += 0.2
                    ml_warnings.append(f"Profit factor below 1.0 predicted: {predicted_pf:.3f}")
                elif predicted_pf < 0.8:  # Significant losses
                    ml_risk_score += 0.3
                    ml_rejections.append(f"Low profit factor predicted: {predicted_pf:.3f}")
            
            # Apply ML-based risk assessment
            ml_config = self.pre_trade_config.get('enhanced_ml', {})
            max_ml_risk_score = ml_config.get('max_risk_score', 0.6)
            enable_ml_rejections = ml_config.get('enable_rejections', True)
            
            if enable_ml_rejections and ml_risk_score > max_ml_risk_score:
                validation_result.failed_checks.extend(ml_rejections)
                logger.warning(f"[ENHANCED_ML] Trade rejected due to high ML risk score: {ml_risk_score:.3f}")
            else:
                validation_result.warnings.extend(ml_warnings)
                if ml_risk_score > 0.3:
                    logger.info(f"[ENHANCED_ML] Trade has elevated ML risk score: {ml_risk_score:.3f}")
                else:
                    logger.info(f"[ENHANCED_ML] Trade passed ML validation (risk score: {ml_risk_score:.3f})")
            
            # Store ML predictions in validation result context
            validation_result.context = validation_result.context or {}
            validation_result.context['ml_predictions'] = {
                'drawdown': drawdown_pred.prediction if drawdown_pred else None,
                'profitability': prof_pred.prediction if prof_pred else None,
                'sharpe_ratio': sharpe_pred.prediction if sharpe_pred else None,
                'roi': roi_pred.prediction if roi_pred else None,
                'profit_factor': pf_pred.prediction if pf_pred else None,
                'ml_risk_score': ml_risk_score
            }
            
            validation_result.passed_checks.append("Enhanced ML validation completed")
            
        except Exception as e:
            logger.error(f"[ERROR] Enhanced ML validation failed: {e}")
            validation_result.warnings.append(f"Enhanced ML validation error: {str(e)}")

    def _prepare_trade_strategy_data(self, trade_request: TradeRequest) -> Dict[str, Any]:
        """Prepare strategy data for enhanced model predictions based on trade request"""
        try:
            # Get current portfolio performance metrics
            current_sharpe = self._calculate_current_sharpe_ratio()
            current_roi = self._calculate_current_roi()
            current_drawdown = self.current_drawdown
            
            # Get recent trade performance
            recent_win_rate = getattr(self, 'recent_win_rate', 0.5)
            recent_profit_factor = getattr(self, 'recent_profit_factor', 1.0)
            
            # Simulate strategy performance metrics based on current conditions
            # In a real implementation, this would use historical performance of the specific strategy
            base_sharpe = max(-2.0, min(3.0, current_sharpe + np.random.normal(0, 0.3)))
            base_roi = base_sharpe * 8 + np.random.normal(0, 2)
            base_profit_factor = max(0.5, recent_profit_factor + np.random.normal(0, 0.2))
            base_drawdown = max(-30.0, min(-5.0, current_drawdown + np.random.normal(0, 3)))
            
            # Adjust based on risk-reward ratio
            if hasattr(trade_request, 'risk_reward_ratio') and trade_request.risk_reward_ratio:
                rr_multiplier = min(1.5, max(0.5, trade_request.risk_reward_ratio / 2.0))
                base_sharpe *= rr_multiplier
                base_roi *= rr_multiplier
            
            strategy_data = {
                # Sharpe ratio metrics
                'avg_sharpe_ratio': base_sharpe,
                'min_sharpe_ratio': base_sharpe - 0.5,
                'max_sharpe_ratio': base_sharpe + 0.5,
                'std_sharpe_ratio': 0.3,
                
                # ROI metrics
                'avg_roi': base_roi,
                'min_roi': base_roi - 5,
                'max_roi': base_roi + 5,
                'std_roi': 3.0,
                
                # Profit factor metrics
                'avg_profit_factor': base_profit_factor,
                'min_profit_factor': base_profit_factor - 0.2,
                'max_profit_factor': base_profit_factor + 0.2,
                'std_profit_factor': 0.1,
                
                # Drawdown metrics
                'avg_max_drawdown': base_drawdown,
                'min_max_drawdown': base_drawdown - 3,
                'max_max_drawdown': base_drawdown + 3,
                'std_max_drawdown': 2.0,
                
                # Expectancy metrics
                'avg_expectancy': base_roi * 0.5,
                'min_expectancy': base_roi * 0.3,
                'max_expectancy': base_roi * 0.7,
                'std_expectancy': base_roi * 0.1,
                
                # Accuracy metrics
                'avg_accuracy': 50 + (base_sharpe * 10),
                'min_accuracy': 40 + (base_sharpe * 8),
                'max_accuracy': 60 + (base_sharpe * 12),
                'std_accuracy': 5.0,
                
                # Trade metrics
                'avg_total_trades': 50,
                'std_total_trades': 10,
                'avg_winning_trades': int(50 * recent_win_rate),
                'std_winning_trades': 5,
                
                # PnL metrics
                'avg_total_pnl': base_roi * 100,
                'min_total_pnl': base_roi * 80,
                'max_total_pnl': base_roi * 120,
                'std_total_pnl': base_roi * 20,
                
                # Consistency metrics
                'consistency_score': max(0.1, min(0.9, recent_win_rate)),
                'sharpe_consistency': max(0.1, min(0.9, 0.6 + base_sharpe * 0.15)),
                'roi_drawdown_ratio': abs(base_roi / base_drawdown) if base_drawdown != 0 else 1.0,
                
                # Other metrics
                'trades_per_period': 10.0,
                'walk_forward_steps': 5
            }
            
            return strategy_data
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to prepare trade strategy data: {e}")
            return {}

    def _calculate_current_sharpe_ratio(self) -> float:
        """Calculate current portfolio Sharpe ratio"""
        try:
            if not hasattr(self, 'daily_returns') or len(self.daily_returns) < 2:
                return 0.0
            
            returns = np.array(self.daily_returns)
            if len(returns) == 0:
                return 0.0
            
            mean_return = np.mean(returns)
            std_return = np.std(returns)
            
            if std_return == 0:
                return 0.0
            
            # Annualized Sharpe ratio (assuming 252 trading days)
            sharpe_ratio = (mean_return / std_return) * np.sqrt(252)
            return float(sharpe_ratio)
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to calculate Sharpe ratio: {e}")
            return 0.0

    def _calculate_current_roi(self) -> float:
        """Calculate current portfolio ROI"""
        try:
            if self.portfolio.total_capital == 0:
                return 0.0
            
            current_value = self.portfolio.total_capital + self.portfolio.daily_pnl
            roi = ((current_value - self.portfolio.total_capital) / self.portfolio.total_capital) * 100
            return float(roi)
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to calculate ROI: {e}")
            return 0.0

    async def _check_margin_requirements(self, validation_result: ValidationResult):
        """Check margin requirements with Angel One API"""
        try:
            trade_req = validation_result.trade_request

            # Calculate position size first
            available_capital = self.portfolio.available_capital
            quantity, capital_allocated, method = self.calculate_position_size(trade_req, available_capital)

            if quantity == 0:
                validation_result.failed_checks.append("Position size calculation resulted in zero quantity")
                return

            import os
            if os.getenv('TRADING_MODE', 'paper').lower() == 'paper':
                # Calculate realistic margin requirements for paper trading
                logger.debug("[INFO] Calculating margin requirements for paper trading mode.")

                # Use realistic margin requirements based on Indian market rules
                # Equity delivery: 100% margin, Intraday: 20-25% margin
                if trade_req.product_type == ProductType.CNC:
                    margin_multiplier = 1.0  # 100% for delivery
                else:
                    margin_multiplier = 0.25  # 25% for intraday (MIS)

                validation_result.margin_required = quantity * trade_req.entry_price * margin_multiplier
                validation_result.margin_available = self.portfolio.available_capital
                validation_result.margin_utilization_percent = (
                    validation_result.margin_required / validation_result.margin_available * 100
                    if validation_result.margin_available > 0 else 0
                )

                # Check if sufficient margin is available
                is_valid = validation_result.margin_required <= validation_result.margin_available
                reason = "Sufficient margin available" if is_valid else f"Insufficient margin: Required Rs.{validation_result.margin_required:,.2f}, Available Rs.{validation_result.margin_available:,.2f}"

                # Update trade request with calculated values
                validation_result.trade_request.quantity = quantity
                validation_result.trade_request.capital_allocated = capital_allocated

                if is_valid:
                    validation_result.passed_checks.append(f"Margin check passed: Rs.{validation_result.margin_required:,.2f} required ({margin_multiplier*100:.0f}% margin)")
                else:
                    validation_result.failed_checks.append(f"Margin check failed: {reason}")

                self.performance_metrics['margin_checks'] += 1
                return

            # Get margin requirement from Angel One (for real trading mode)
            margin_req = await self.angel_api.validate_trade_margin(
                trade_req.symbol,
                trade_req.exchange,
                quantity,
                trade_req.entry_price,
                "BUY" if trade_req.direction == TradeDirection.LONG else "SELL",
                trade_req.product_type.value
            )

            is_valid, reason, margin_data = margin_req

            if not is_valid:
                validation_result.failed_checks.append(f"Margin validation failed: {reason}")
                return

            if margin_data:
                validation_result.margin_required = margin_data.margin_required
                validation_result.margin_available = margin_data.available_margin
                validation_result.margin_utilization_percent = (
                    margin_data.margin_required / margin_data.available_margin * 100
                    if margin_data.available_margin > 0 else 0
                )

            # Update trade request with calculated values
            validation_result.trade_request.quantity = quantity
            validation_result.trade_request.capital_allocated = capital_allocated

            validation_result.passed_checks.append(f"Margin check passed: Rs.{margin_data.margin_required:,.2f} required")
            self.performance_metrics['margin_checks'] += 1

        except Exception as e:
            logger.error(f"[ERROR] Error checking margin requirements: {e}")
            validation_result.failed_checks.append(f"Margin check error: {str(e)}")

    async def _check_capital_limits(self, validation_result: ValidationResult):
        """Check capital allocation limits"""
        try:
            trade_req = validation_result.trade_request
            capital_config = self.capital_config

            # Check daily risk limit
            max_daily_risk = capital_config.get('max_daily_risk_percent', 5.0)
            daily_risk_percent = (self.daily_risk_amount / self.portfolio.total_capital) * 100

            if daily_risk_percent >= max_daily_risk:
                validation_result.failed_checks.append(
                    f"Daily risk limit exceeded: {daily_risk_percent:.2f}% >= {max_daily_risk}%"
                )
                return

            # Check portfolio risk limit
            max_portfolio_risk = capital_config.get('max_portfolio_risk_percent', 10.0)
            total_risk = sum(pos.risk_amount for pos in self.active_positions.values())
            portfolio_risk_percent = (total_risk / self.portfolio.total_capital) * 100

            if portfolio_risk_percent >= max_portfolio_risk:
                validation_result.failed_checks.append(
                    f"Portfolio risk limit exceeded: {portfolio_risk_percent:.2f}% >= {max_portfolio_risk}%"
                )
                return

            # Check position size limits
            position_value = trade_req.quantity * trade_req.entry_price
            position_size_percent = (position_value / self.portfolio.total_capital) * 100
            max_position_size = capital_config.get('position_sizing', {}).get('fixed_fraction', {}).get('max_percent', 5.0)

            if position_size_percent > max_position_size:
                validation_result.failed_checks.append(
                    f"Position size too large: {position_size_percent:.2f}% > {max_position_size}%"
                )
                return

            validation_result.position_size_percent = position_size_percent
            validation_result.portfolio_risk_percent = portfolio_risk_percent
            validation_result.passed_checks.append("Capital limits check passed")

        except Exception as e:
            logger.error(f"[ERROR] Error checking capital limits: {e}")
            validation_result.failed_checks.append(f"Capital limits check error: {str(e)}")

    def _determine_risk_level(self, validation_result: ValidationResult) -> RiskLevel:
        """Determine risk level based on validation metrics"""
        try:
            # Start with LOW risk
            risk_level = RiskLevel.LOW

            # Check position size
            if validation_result.position_size_percent > 3.0:
                risk_level = RiskLevel.MEDIUM
            if validation_result.position_size_percent > 4.0:
                risk_level = RiskLevel.HIGH

            # Check margin utilization
            if validation_result.margin_utilization_percent > 70:
                risk_level = max(risk_level, RiskLevel.MEDIUM)
            if validation_result.margin_utilization_percent > 85:
                risk_level = RiskLevel.HIGH

            # Check portfolio risk
            if validation_result.portfolio_risk_percent > 7.0:
                risk_level = max(risk_level, RiskLevel.MEDIUM)
            if validation_result.portfolio_risk_percent > 9.0:
                risk_level = RiskLevel.HIGH

            # Check warnings
            if len(validation_result.warnings) > 2:
                risk_level = max(risk_level, RiskLevel.MEDIUM)

            # Check current drawdown
            if self.current_drawdown > 5.0:
                risk_level = max(risk_level, RiskLevel.HIGH)
            if self.current_drawdown > 8.0:
                risk_level = RiskLevel.CRITICAL

            return risk_level

        except Exception as e:
            logger.error(f"[ERROR] Error determining risk level: {e}")
            return RiskLevel.HIGH

    def _log_validation_result(self, validation_result: ValidationResult):
        """Log validation result"""
        try:
            trade_req = validation_result.trade_request

            if validation_result.is_valid:
                logger.info(
                    f"[SUCCESS] Trade validation PASSED: {trade_req.symbol} {trade_req.direction.name} "
                    f"Qty: {trade_req.quantity} @ Rs.{trade_req.entry_price} "
                    f"Risk Level: {format_risk_level(validation_result.risk_level)}"
                )
            else:
                logger.warning(
                    f"[ERROR] Trade validation FAILED: {trade_req.symbol} {trade_req.direction.name} "
                    f"Reason: {validation_result.rejection_reason}"
                )

            # Log detailed metrics if enabled
            log_config = self.config.get('logging_reporting', {}).get('trade_logging', {})
            if log_config.get('log_entry_details', True):
                logger.debug(f"Validation details: {json.dumps(validation_result.metrics, indent=2)}")

        except Exception as e:
            logger.error(f"[ERROR] Error logging validation result: {e}")

    # ═══════════════════════════════════════════════════════════════════════════════
    # 🔁 LIVE TRADE SUPERVISION
    # ═══════════════════════════════════════════════════════════════════════════════

    async def _background_monitoring(self):
        """Background monitoring loop for live supervision"""
        try:
            logger.info("[WORKFLOW] Starting background monitoring...")

            while True:
                try:
                    # Update positions and portfolio metrics
                    await self._update_portfolio_metrics()

                    # Check drawdown limits
                    await self._check_drawdown_limits()

                    # Monitor positions
                    await self._monitor_positions()

                    # Check margin status
                    await self._check_margin_status()

                    # Generate performance snapshot
                    await self._create_performance_snapshot()

                    # Sleep for monitoring interval
                    monitoring_interval = self.supervision_config.get('position_monitoring', {}).get('check_interval_seconds', 30)
                    await asyncio.sleep(monitoring_interval)

                except Exception as e:
                    logger.error(f"[ERROR] Error in background monitoring: {e}")
                    await asyncio.sleep(60)  # Wait longer on error

        except Exception as e:
            logger.error(f"[ERROR] Background monitoring failed: {e}")

    async def _update_portfolio_metrics(self):
        """Update portfolio metrics with current data"""
        try:
            import os
            if os.getenv('TRADING_MODE', 'paper').lower() == 'paper':
                # Simulate portfolio metrics for paper trading
                logger.debug("[INFO] Simulating portfolio metrics update in paper trading mode.")
                # For simplicity, we'll just update the timestamp and assume no PnL changes
                # You could add more complex PnL simulation here if needed
                self.portfolio.last_update_time = datetime.now()
                # Calculate real PnL from virtual account if available
                try:
                    # Try to get real PnL from virtual account
                    if hasattr(self, 'virtual_account') and self.virtual_account:
                        account_summary = self.virtual_account.get_account_summary()
                        self.portfolio.total_pnl = account_summary.get('total_pnl', 0.0)
                        self.portfolio.unrealized_pnl = account_summary.get('unrealized_pnl', 0.0)
                    else:
                        # If no virtual account, maintain current values (no random changes)
                        pass
                except Exception as e:
                    logger.debug(f"[DEBUG] Could not get virtual account PnL: {e}")
                    # Maintain current values without random simulation
                self.portfolio.available_capital = self.portfolio.total_capital + self.portfolio.total_pnl
                self.portfolio.utilized_capital = 0.0 # In paper trading, assume no utilized capital for simplicity
                
                # Simulate drawdown
                current_value = self.portfolio.total_capital + self.portfolio.total_pnl
                if current_value > self.peak_portfolio_value:
                    self.peak_portfolio_value = current_value
                self.current_drawdown = ((self.peak_portfolio_value - current_value) / self.peak_portfolio_value) * 100
                self.portfolio.current_drawdown = self.current_drawdown
                if self.current_drawdown > self.portfolio.max_drawdown:
                    self.portfolio.max_drawdown = self.current_drawdown

                return
            
            # Get current positions from broker (for real trading mode)
            current_positions = await self.angel_api.get_positions()

            # Update portfolio values
            total_pnl = sum(pos.pnl for pos in current_positions)
            unrealized_pnl = sum(pos.unrealized_pnl for pos in current_positions)
            realized_pnl = sum(pos.realized_pnl for pos in current_positions)

            self.portfolio.total_pnl = total_pnl
            self.portfolio.unrealized_pnl = unrealized_pnl
            self.portfolio.realized_pnl = realized_pnl

            # Calculate current portfolio value
            current_value = self.portfolio.total_capital + total_pnl

            # Update peak value and drawdown
            if current_value > self.peak_portfolio_value:
                self.peak_portfolio_value = current_value

            self.current_drawdown = ((self.peak_portfolio_value - current_value) / self.peak_portfolio_value) * 100
            self.portfolio.current_drawdown = self.current_drawdown

            # Update max drawdown
            if self.current_drawdown > self.portfolio.max_drawdown:
                self.portfolio.max_drawdown = self.current_drawdown

            # Update available capital
            fund_data = await self.angel_api.get_funds()
            if fund_data:
                self.portfolio.available_capital = fund_data.available_cash
                self.portfolio.utilized_capital = fund_data.utilized_margin

            self.portfolio.last_update_time = datetime.now()

        except Exception as e:
            logger.error(f"[ERROR] Error updating portfolio metrics: {e}")

    async def _monitor_positions(self):
        """Monitor active positions for risk management"""
        try:
            logger.debug("[INFO] Monitoring active positions...") # Added log for debugging
            import os
            if os.getenv('TRADING_MODE', 'paper').lower() == 'paper':
                # In paper mode, simulate position monitoring
                for position_id, position in self.active_positions.items():
                    if position.is_open:
                        # Simulate price movement for paper trading
                        import random
                        # Simulate a small random price change
                        change_percent = random.uniform(-0.005, 0.005) # +/- 0.5%
                        simulated_price = position.current_price * (1 + change_percent)
                        
                        position.current_price = simulated_price
                        position.pnl = (simulated_price - position.entry_price) * position.quantity
                        position.pnl_percent = (position.pnl / position.capital_allocated) * 100

                        # Check for stop loss or take profit triggers (simulated)
                        if position.direction.value == "LONG":
                            if simulated_price <= position.stop_loss:
                                logger.warning(f"[WARN] Simulated Stop loss triggered for {position.symbol}")
                                # In a real system, this would trigger an order to close
                            elif simulated_price >= position.take_profit:
                                logger.info(f"[SUCCESS] Simulated Take profit triggered for {position.symbol}")
                                # In a real system, this would trigger an order to close
                        else:  # SHORT
                            if simulated_price >= position.stop_loss:
                                logger.warning(f"[WARN] Simulated Stop loss triggered for {position.symbol}")
                            elif simulated_price <= position.take_profit:
                                logger.info(f"[SUCCESS] Simulated Take profit triggered for {position.symbol}")
            else:
                # Real API monitoring
                for position_id, position in self.active_positions.items():
                    # Check stop loss and take profit levels
                    if position.is_open:
                        # Update current price and PnL
                        current_price = await self.angel_api.get_ltp(position.exchange, position.symbol, "")
                        if current_price:
                            position.current_price = current_price
                            position.pnl = (current_price - position.entry_price) * position.quantity
                            position.pnl_percent = (position.pnl / position.capital_allocated) * 100

                            # Check for stop loss or take profit triggers
                            if position.direction.value == "LONG":
                                if current_price <= position.stop_loss:
                                    logger.warning(f"[WARN] Stop loss triggered for {position.symbol}")
                                elif current_price >= position.take_profit:
                                    logger.info(f"[SUCCESS] Take profit triggered for {position.symbol}")
                            else:  # SHORT
                                if current_price >= position.stop_loss:
                                    logger.warning(f"[WARN] Stop loss triggered for {position.symbol}")
                                elif current_price <= position.take_profit:
                                    logger.info(f"[SUCCESS] Take profit triggered for {position.symbol}")

        except Exception as e:
            logger.error(f"[ERROR] Error monitoring positions: {e}")

    async def _check_margin_status(self):
        """Check margin requirements and utilization"""
        try:
            import os
            if os.getenv('TRADING_MODE', 'paper').lower() == 'paper':
                # In paper mode, simulate margin status
                # For simplicity, assume sufficient margin in paper trading
                logger.debug("[INFO] Simulating margin check in paper trading mode. Assuming sufficient margin.")
                # You could add more complex simulation here if needed
            else:
                # Real API margin check
                fund_data = await self.angel_api.get_funds()
                if fund_data:
                    margin_utilization = (fund_data.utilized_margin / fund_data.available_cash) * 100 if fund_data.available_cash > 0 else 0

                    if margin_utilization > 80:
                        logger.warning(f"[WARN] High margin utilization: {margin_utilization:.2f}%")
                    elif margin_utilization > 90:
                        logger.error(f"[ERROR] Critical margin utilization: {margin_utilization:.2f}%")

        except Exception as e:
            logger.error(f"[ERROR] Error checking margin status: {e}")

    async def _check_drawdown_limits(self):
        """Check and enforce drawdown limits"""
        try:
            drawdown_config = self.supervision_config.get('drawdown_controls', {})
            max_daily_drawdown = drawdown_config.get('max_daily_drawdown_percent', 10.0)
            max_total_drawdown = drawdown_config.get('max_total_drawdown_percent', 15.0)

            # Check daily drawdown
            if self.current_drawdown >= max_daily_drawdown:
                await self._trigger_drawdown_protection("daily", self.current_drawdown, max_daily_drawdown)

            # Check total drawdown
            if self.portfolio.max_drawdown >= max_total_drawdown:
                await self._trigger_drawdown_protection("total", self.portfolio.max_drawdown, max_total_drawdown)

        except Exception as e:
            logger.error(f"[ERROR] Error checking drawdown limits: {e}")

    async def _trigger_drawdown_protection(self, drawdown_type: str, current_dd: float, limit: float):
        """Trigger drawdown protection measures"""
        try:
            logger.critical(f"[ALERT] DRAWDOWN PROTECTION TRIGGERED: {drawdown_type} drawdown {current_dd:.2f}% >= {limit}%")

            # Create critical alert
            alert = RiskAlert(
                alert_id=create_alert_id("drawdown_protection", datetime.now()),
                alert_type="DRAWDOWN_PROTECTION",
                severity=RiskLevel.CRITICAL,
                title=f"{drawdown_type.title()} Drawdown Limit Breached",
                message=f"Drawdown protection triggered: {current_dd:.2f}% >= {limit}%",
                current_value=current_dd,
                threshold_value=limit,
                breach_percent=((current_dd - limit) / limit) * 100
            )

            self.risk_alerts.append(alert)

            # Stop all new trades (implementation would depend on integration)
            logger.critical("[STOP] ALL NEW TRADES STOPPED DUE TO DRAWDOWN PROTECTION")

            # Optionally close all positions (very aggressive - usually manual decision)
            # await self._emergency_position_closure()

        except Exception as e:
            logger.error(f"[ERROR] Error triggering drawdown protection: {e}")

    async def get_performance_metrics(self) -> Dict[str, Any]:
        """Get comprehensive performance metrics"""
        try:
            return {
                'portfolio': {
                    'total_capital': self.portfolio.total_capital,
                    'available_capital': self.portfolio.available_capital,
                    'utilized_capital': self.portfolio.utilized_capital,
                    'total_pnl': self.portfolio.total_pnl,
                    'daily_pnl': self.portfolio.daily_pnl,
                    'unrealized_pnl': self.portfolio.unrealized_pnl,
                    'realized_pnl': self.portfolio.realized_pnl,
                    'current_drawdown': self.current_drawdown,
                    'max_drawdown': self.portfolio.max_drawdown
                },
                'positions': {
                    'total_positions': len(self.active_positions),
                    'long_positions': sum(1 for pos in self.active_positions.values() if pos.direction == TradeDirection.LONG),
                    'short_positions': sum(1 for pos in self.active_positions.values() if pos.direction == TradeDirection.SHORT),
                    'total_market_value': sum(pos.market_value for pos in self.active_positions.values())
                },
                'risk_metrics': {
                    'daily_risk_amount': self.daily_risk_amount,
                    'total_risk_exposure': sum(pos.risk_amount for pos in self.active_positions.values()),
                    'margin_utilization': 0.0,  # Would be calculated from Angel One data
                    'active_alerts': len([alert for alert in self.risk_alerts if alert.is_active])
                },
                'performance': self.performance_metrics,
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"[ERROR] Error getting performance metrics: {e}")
            return {}

    async def add_position(self, trade_request: TradeRequest, execution_price: float) -> Optional[str]:
        """Add a new position after trade execution"""
        try:
            position_id = create_position_id(trade_request.symbol, trade_request.strategy_name, datetime.now())

            position = Position(
                position_id=position_id,
                symbol=trade_request.symbol,
                exchange=trade_request.exchange,
                strategy_name=trade_request.strategy_name,
                direction=trade_request.direction,
                quantity=trade_request.quantity,
                entry_price=execution_price,
                current_price=execution_price,
                stop_loss=trade_request.stop_loss,
                take_profit=trade_request.take_profit,
                market_value=trade_request.quantity * execution_price,
                pnl=0.0,
                pnl_percent=0.0,
                unrealized_pnl=0.0,
                realized_pnl=0.0,
                risk_amount=trade_request.risk_amount,
                capital_allocated=trade_request.capital_allocated,
                margin_required=0.0,  # Would be set from margin calculation
                is_open=True,
                entry_time=datetime.now(),
                last_update_time=datetime.now(),
                product_type=trade_request.product_type
            )

            self.active_positions[position_id] = position
            self.portfolio.positions.append(position)

            # Update daily tracking
            self.daily_trades_count += 1
            self.daily_risk_amount += trade_request.risk_amount

            logger.info(f"[SUCCESS] Position added: {position_id} - {trade_request.symbol} {trade_request.direction.name}")
            return position_id

        except Exception as e:
            logger.error(f"[ERROR] Error adding position: {e}")
            return None

    async def close_position(self, position_id: str, exit_price: float, exit_reason: str = "manual") -> bool:
        """Close a position"""
        try:
            if position_id not in self.active_positions:
                logger.warning(f"[WARN] Position not found: {position_id}")
                return False

            position = self.active_positions[position_id]

            # Calculate final PnL
            if position.direction == TradeDirection.LONG:
                pnl = (exit_price - position.entry_price) * position.quantity
            else:
                pnl = (position.entry_price - exit_price) * position.quantity

            position.pnl = pnl
            position.pnl_percent = (pnl / position.capital_allocated) * 100
            position.realized_pnl = pnl
            position.is_open = False
            position.exit_time = datetime.now()
            position.holding_time_minutes = int((position.exit_time - position.entry_time).total_seconds() / 60)

            # Move to closed positions
            self.portfolio.closed_positions.append(position)
            del self.active_positions[position_id]

            # Update portfolio metrics
            self.portfolio.realized_pnl += pnl
            self.portfolio.total_trades += 1

            if pnl > 0:
                self.portfolio.winning_trades += 1
            else:
                self.portfolio.losing_trades += 1

            # Update statistics
            self.portfolio.win_rate = calculate_win_rate(self.portfolio.winning_trades, self.portfolio.total_trades)

            logger.info(f"[SUCCESS] Position closed: {position_id} - PnL: Rs.{pnl:,.2f} ({position.pnl_percent:.2f}%) - Reason: {exit_reason}")
            return True

        except Exception as e:
            logger.error(f"[ERROR] Error closing position: {e}")
            return False

    async def _create_performance_snapshot(self):
        """Create performance snapshot for monitoring"""
        try:
            snapshot = PerformanceSnapshot(
                snapshot_id=f"snapshot_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                timestamp=datetime.now(),
                total_capital=self.portfolio.total_capital,
                available_capital=self.portfolio.available_capital,
                utilized_capital=self.portfolio.utilized_capital,
                total_pnl=self.portfolio.total_pnl,
                daily_pnl=self.portfolio.daily_pnl,
                unrealized_pnl=self.portfolio.unrealized_pnl,
                realized_pnl=self.portfolio.realized_pnl,
                total_positions=len(self.active_positions),
                long_positions=sum(1 for pos in self.active_positions.values() if pos.direction == TradeDirection.LONG),
                short_positions=sum(1 for pos in self.active_positions.values() if pos.direction == TradeDirection.SHORT),
                total_risk_exposure=sum(pos.risk_amount for pos in self.active_positions.values()),
                margin_utilization=0.0,  # Would be calculated from Angel One data
                current_drawdown=self.current_drawdown,
                win_rate=self.portfolio.win_rate,
                profit_factor=calculate_profit_factor(
                    sum(pos.pnl for pos in self.portfolio.closed_positions if pos.pnl > 0),
                    sum(pos.pnl for pos in self.portfolio.closed_positions if pos.pnl < 0)
                )
            )

            self.performance_history.append(snapshot)

        except Exception as e:
            logger.error(f"[ERROR] Error creating performance snapshot: {e}")

    async def shutdown(self):
        """Shutdown the Risk Management Agent"""
        try:
            logger.info("[WORKFLOW] Shutting down Risk Management Agent...")

            import os
            if os.getenv('TRADING_MODE', 'paper').lower() != 'paper':
                # Close Angel One API session only if not in paper trading mode
                await self.angel_api.close_session()

            # Generate final report
            await self._generate_final_report()

            logger.info("[SUCCESS] Risk Management Agent shutdown completed")

        except Exception as e:
            logger.error(f"[ERROR] Error during shutdown: {e}")

    async def _generate_final_report(self):
        """Generate final performance report"""
        try:
            report = {
                'session_summary': {
                    'total_trades': self.portfolio.total_trades,
                    'winning_trades': self.portfolio.winning_trades,
                    'losing_trades': self.portfolio.losing_trades,
                    'win_rate': self.portfolio.win_rate,
                    'total_pnl': self.portfolio.total_pnl,
                    'max_drawdown': self.portfolio.max_drawdown,
                    'daily_trades_count': self.daily_trades_count,
                    'daily_risk_amount': self.daily_risk_amount
                },
                'performance_metrics': self.performance_metrics,
                'active_alerts': len([alert for alert in self.risk_alerts if alert.is_active]),
                'timestamp': datetime.now().isoformat()
            }

            # Save report to file
            report_path = self.config.get('logging_reporting', {}).get('daily_reports', {}).get('report_path', 'reports/daily_risk')
            os.makedirs(report_path, exist_ok=True)

            report_file = os.path.join(report_path, f"risk_report_{datetime.now().strftime('%Y%m%d')}.json")
            with open(report_file, 'w') as f:
                json.dump(report, f, indent=2)

            logger.info(f"[SUCCESS] Final report generated: {report_file}")

        except Exception as e:
            logger.error(f"[ERROR] Error generating final report: {e}")

# ═══════════════════════════════════════════════════════════════════════════════
# [CONFIG] UTILITY FUNCTIONS
# ═══════════════════════════════════════════════════════════════════════════════

async def create_risk_agent(config_path: str = "agents/config/risk_management_config.yaml") -> RiskManagementAgent:
    """Create and setup Risk Management Agent"""
    agent = RiskManagementAgent(config_path)
    await agent.setup()
    return agent
