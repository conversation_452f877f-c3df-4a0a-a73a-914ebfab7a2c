#!/usr/bin/env python3
"""
Strategy Management for Strategy Evolution Agent

This module handles strategy creation, loading, management,
and conversion between different formats.
"""

import uuid
import yaml
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple

from .data_types import (
    StrategyChromosome, StrategyGene, EvolutionConfig, EvolutionState
)
from .lazy_imports import log_critical


class StrategyManager:
    """Handles strategy creation, loading, and management"""
    
    def __init__(self, evolution_config: EvolutionConfig, config: Dict[str, Any]):
        self.evolution_config = evolution_config
        self.config = config
        self.evolved_strategies_in_config: List[Dict[str, Any]] = []
        self.active_strategies: Dict[str, StrategyChromosome] = {}
        self.evolution_state = EvolutionState()
    
    def set_evolved_strategies_in_config(self, evolved_strategies: List[Dict[str, Any]]):
        """Set the evolved strategies from config"""
        self.evolved_strategies_in_config = evolved_strategies
    
    def set_evolution_state(self, evolution_state: EvolutionState):
        """Set the evolution state"""
        self.evolution_state = evolution_state
    
    async def load_existing_strategies(self):
        """Load existing strategies from the config/strategy_evolution_config.yaml file"""
        try:
            if self.evolved_strategies_in_config:
                # Load only a limited number of strategies for faster startup
                load_limit = self.evolution_config.initial_population_load_limit
                loaded_count = 0
                for strategy_data in self.evolved_strategies_in_config:
                    if loaded_count >= load_limit:
                        break
                    try:
                        chromosome = self._dict_to_chromosome(strategy_data)
                        self.active_strategies[chromosome.strategy_id] = chromosome
                        self.evolution_state.population.append(chromosome)  # Add to population directly
                        loaded_count += 1
                    except Exception as e:
                        pass  # Skip failed strategies silently

        except Exception as e:
            log_critical(f"Failed to load existing strategies - {e}")

    async def initialize_evolution_state(self):
        """Initialize the evolution state"""
        try:
            # If no active strategies (either from config or initial population), create initial population
            if not self.active_strategies:
                await self._create_initial_population()
            else:
                # Use existing strategies as population
                self.evolution_state.population = list(self.active_strategies.values())

            # Update generation stats
            self.evolution_state.update_generation_stats()

        except Exception as e:
            log_critical(f"Failed to initialize evolution state - {e}")
            raise

    async def _create_initial_population(self):
        """Create initial population of strategies"""
        try:
            # Load base strategies from config
            base_strategies = await self._load_base_strategies()

            # Use initial_population_load_limit for initial creation
            initial_creation_limit = self.evolution_config.initial_population_load_limit
            
            # Ensure we don't try to create more than the population size
            target_initial_population_size = min(initial_creation_limit, self.evolution_config.population_size)

            strategies_per_base = max(1, target_initial_population_size // len(base_strategies))

            for base_strategy in base_strategies:
                for i in range(strategies_per_base):
                    if len(self.evolution_state.population) >= target_initial_population_size:
                        break  # Stop if limit reached

                    # Create chromosome from base strategy
                    chromosome = self._create_chromosome_from_strategy(base_strategy, generation=0)

                    # Add some initial variation
                    if i > 0:  # Keep first one as original
                        from .genetic_operations import GeneticOperations
                        genetic_ops = GeneticOperations(self.evolution_config, 0)
                        chromosome = genetic_ops.mutate_chromosome(chromosome, mutation_rate=0.3)

                    self.active_strategies[chromosome.strategy_id] = chromosome
                    self.evolution_state.population.append(chromosome)

                if len(self.evolution_state.population) >= target_initial_population_size:
                    break  # Stop if limit reached

            # Fill remaining slots with random variations up to the limit
            while len(self.evolution_state.population) < target_initial_population_size:
                import random
                base_strategy = random.choice(base_strategies)
                chromosome = self._create_chromosome_from_strategy(base_strategy, generation=0)
                
                from .genetic_operations import GeneticOperations
                genetic_ops = GeneticOperations(self.evolution_config, 0)
                chromosome = genetic_ops.mutate_chromosome(chromosome, mutation_rate=0.5)

                self.active_strategies[chromosome.strategy_id] = chromosome
                self.evolution_state.population.append(chromosome)

        except Exception as e:
            log_critical(f"Failed to create initial population - {e}")
            raise

    async def _load_base_strategies(self) -> List[Dict[str, Any]]:
        """Load base strategies from configuration"""
        try:
            # Load from strategies.yaml
            strategy_config_path = "agents/config/strategies.yaml"

            if Path(strategy_config_path).exists():
                with open(strategy_config_path, 'r', encoding='utf-8') as file:
                    strategy_data = yaml.safe_load(file)

                strategies = strategy_data.get('strategies', [])

                # Filter to only load base templates for initial population
                # Evolved strategies will be loaded separately
                base_strategies = [s for s in strategies if s.get('type') == 'base_template']

                if not base_strategies:
                    # If no base templates found, use all strategies
                    base_strategies = strategies

                return base_strategies
            else:
                # Create default strategies if config doesn't exist
                return self._get_default_strategies()

        except Exception as e:
            log_critical(f"Failed to load base strategies - {e}")
            return self._get_default_strategies()

    def _get_default_strategies(self) -> List[Dict[str, Any]]:
        """Get default strategies for initial population"""
        return [
            {
                'name': 'RSI_Mean_Reversion',
                'long': 'RSI_14 < 30 & Volume > Volume.rolling(20).mean()',
                'short': 'RSI_14 > 70 & Volume > Volume.rolling(20).mean()',
                'capital': 100000
            },
            {
                'name': 'EMA_Crossover',
                'long': 'EMA_5 > EMA_20 & EMA_20 > EMA_50',
                'short': 'EMA_5 < EMA_20 & EMA_20 < EMA_50',
                'capital': 100000
            },
            {
                'name': 'VWAP_Breakout',
                'long': 'Close > VWAP & Volume > Volume.rolling(20).mean() * 1.5',
                'short': 'Close < VWAP & Volume > Volume.rolling(20).mean() * 1.5',
                'capital': 100000
            }
        ]

    def _create_chromosome_from_strategy(self, strategy: Dict[str, Any], generation: int = 0) -> StrategyChromosome:
        """Create a chromosome from a strategy definition"""
        strategy_id = str(uuid.uuid4())
        strategy_name = strategy.get('name', f'Strategy_{strategy_id[:8]}')

        # Extract stock and timeframe from strategy definition or name
        target_stock = strategy.get('target_stock')
        target_timeframe = strategy.get('target_timeframe')

        # If not in strategy definition, try to extract from name
        if not target_stock or not target_timeframe:
            extracted_stock, extracted_timeframe = self._extract_stock_and_timeframe(strategy_name)
            target_stock = target_stock or extracted_stock
            target_timeframe = target_timeframe or extracted_timeframe

        # Extract genes from strategy parameters
        genes = {}

        # Add stock and timeframe as genes for targeting
        if target_stock:
            genes['target_stock'] = StrategyGene('target_stock', target_stock, None, None, 0.0, 'categorical')
        if target_timeframe:
            genes['target_timeframe'] = StrategyGene('target_timeframe', target_timeframe, None, None, 0.0, 'categorical')

        # Extract parameters from strategy definition
        strategy_params = strategy.get('parameters', {})

        # Extract parameters from strategy definition with defaults
        # RSI parameters
        if 'rsi_period' in strategy_params or 'RSI' in str(strategy):
            genes['rsi_period'] = StrategyGene('rsi_period', strategy_params.get('rsi_period', 14), 5, 50, 0.1, 'numeric')
            genes['rsi_oversold'] = StrategyGene('rsi_oversold', 30, 10, 40, 0.1, 'numeric')
            genes['rsi_overbought'] = StrategyGene('rsi_overbought', 70, 60, 90, 0.1, 'numeric')

        # EMA parameters
        if any(k in strategy_params for k in ['ema_fast', 'ema_slow', 'ema_trend']) or 'EMA' in str(strategy):
            genes['ema_fast'] = StrategyGene('ema_fast', strategy_params.get('ema_fast', 5), 3, 15, 0.1, 'numeric')
            genes['ema_slow'] = StrategyGene('ema_slow', strategy_params.get('ema_slow', 20), 15, 50, 0.1, 'numeric')
            genes['ema_trend'] = StrategyGene('ema_trend', strategy_params.get('ema_trend', 50), 30, 100, 0.1, 'numeric')

        # Volume parameters
        if 'volume_period' in strategy_params or 'Volume' in str(strategy):
            genes['volume_multiplier'] = StrategyGene('volume_multiplier', strategy_params.get('volume_multiplier', 1.5), 1.0, 3.0, 0.1, 'numeric')
            genes['volume_period'] = StrategyGene('volume_period', strategy_params.get('volume_period', 20), 10, 50, 0.1, 'numeric')

        # Bollinger Bands parameters
        if any(k in strategy_params for k in ['bb_period', 'bb_std']) or 'BB' in str(strategy) or 'Bollinger' in str(strategy):
            genes['bb_period'] = StrategyGene('bb_period', strategy_params.get('bb_period', 20), 10, 50, 0.1, 'numeric')
            genes['bb_std'] = StrategyGene('bb_std', strategy_params.get('bb_std', 2.0), 1.5, 3.0, 0.1, 'numeric')

        # VWAP parameters
        if 'vwap_deviation' in strategy_params or 'VWAP' in str(strategy):
            genes['vwap_deviation'] = StrategyGene('vwap_deviation', strategy_params.get('vwap_deviation', 0.002), 0.001, 0.01, 0.1, 'numeric')

        # Risk management parameters from strategy definition
        risk_mgmt = strategy.get('risk_management', {})
        genes['stop_loss_pct'] = StrategyGene('stop_loss_pct', risk_mgmt.get('stop_loss_pct', 0.02), 0.005, 0.05, 0.1, 'numeric')
        genes['take_profit_pct'] = StrategyGene('take_profit_pct', risk_mgmt.get('take_profit_pct', 0.04), 0.01, 0.10, 0.1, 'numeric')

        # Position sizing parameters
        position_sizing = strategy.get('position_sizing', {})
        genes['position_size_pct'] = StrategyGene('position_size_pct', position_sizing.get('percentage', 0.05), 0.01, 0.15, 0.1, 'numeric')

        # Market timing parameters
        genes['min_volume_ratio'] = StrategyGene('min_volume_ratio', strategy_params.get('volume_multiplier', 1.2), 0.8, 2.5, 0.1, 'numeric')
        genes['volatility_filter'] = StrategyGene('volatility_filter', True, None, None, 0.1, 'boolean')

        chromosome = StrategyChromosome(
            strategy_id=strategy_id,
            strategy_name=strategy_name,
            genes=genes,
            generation=generation,
            creation_timestamp=datetime.now()
        )

        # Store target stock and timeframe for focused testing
        if target_stock:
            chromosome.symbol_performance[target_stock] = 0.0

        return chromosome

    def _extract_stock_and_timeframe(self, strategy_name: str) -> Tuple[Optional[str], Optional[str]]:
        """Extract stock symbol and timeframe from strategy name"""
        try:
            # Pattern: StrategyType_StockSymbol_Timeframe (e.g., "Bollinger_Bounce_360ONE_1min")
            parts = strategy_name.split('_')

            if len(parts) >= 3:
                # Look for timeframe pattern (ends with 'min', 'h', 'd')
                timeframe = None
                stock = None

                for i, part in enumerate(parts):
                    if part.endswith(('min', 'h', 'd')):
                        timeframe = part
                        # Stock symbol is typically the part before timeframe
                        if i > 0:
                            stock = parts[i-1]
                        break

                return stock, timeframe

            return None, None

        except Exception as e:
            return None, None

    def _dict_to_chromosome(self, strategy_data: Dict[str, Any]) -> StrategyChromosome:
        """Convert dictionary to StrategyChromosome"""
        genes = {}

        for gene_name, gene_data in strategy_data.get('genes', {}).items():
            genes[gene_name] = StrategyGene(
                name=gene_name,
                value=gene_data['value'],
                gene_type=gene_data.get('type', 'numeric')
            )

        return StrategyChromosome(
            strategy_id=strategy_data['strategy_id'],
            strategy_name=strategy_data['strategy_name'],
            genes=genes,
            fitness_score=strategy_data.get('fitness_score', 0.0),
            generation=strategy_data.get('generation', 0),
            parent_ids=strategy_data.get('parent_ids', []),
            creation_timestamp=datetime.fromisoformat(strategy_data.get('creation_timestamp', datetime.now().isoformat()))
        )

    def get_best_strategies(self, count: int = 10) -> List[StrategyChromosome]:
        """Get the best performing strategies"""
        sorted_strategies = sorted(self.active_strategies.values(), 
                                 key=lambda x: x.fitness_score, reverse=True)
        return sorted_strategies[:count]

    def get_strategy_by_id(self, strategy_id: str) -> Optional[StrategyChromosome]:
        """Get strategy by ID"""
        return self.active_strategies.get(strategy_id)

    def add_strategy(self, chromosome: StrategyChromosome):
        """Add a strategy to the active strategies"""
        self.active_strategies[chromosome.strategy_id] = chromosome

    def remove_strategy(self, strategy_id: str):
        """Remove a strategy from active strategies"""
        if strategy_id in self.active_strategies:
            del self.active_strategies[strategy_id]

    def get_active_strategies(self) -> Dict[str, StrategyChromosome]:
        """Get all active strategies"""
        return self.active_strategies

    def clear_strategies(self):
        """Clear all strategies"""
        self.active_strategies.clear()
        self.evolution_state.population.clear()
