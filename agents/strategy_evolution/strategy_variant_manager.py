#!/usr/bin/env python3
"""
Strategy Variant Manager Module

This module handles the creation, management, and conversion of strategy variants including:
- Variant creation from different sources
- Format conversions (variant to backtesting format, variant to YAML format)
- Mock variant generation for testing
- Parameter space management
"""

import uuid
import json
import yaml
from typing import Dict, List, Any, Optional
from datetime import datetime
from pathlib import Path

from agents.strategy_evolution.evolution_config import StrategyVariant, StrategyStatus
from agents.strategy_evolution.evolution_logger import logger


class StrategyVariantManager:
    """
    Manages strategy variant creation, conversion, and formatting
    """
    
    def __init__(self, evolution_config):
        self.evolution_config = evolution_config
        logger.info("[VARIANT_MGR] Strategy Variant Manager initialized")
    
    def create_variant_from_gpu_results(self, base_strategy: Dict[str, Any], stock_name: str, 
                                      timeframe: str, gpu_result: Dict[str, Any], variant_idx: int) -> StrategyVariant:
        """Create variant from GPU results with parameter variations"""
        try:
            # Add parameter variations based on variant index and GPU results
            stop_loss = 0.015 + (variant_idx * 0.005)  # 1.5%, 2.0%, 2.5%
            take_profit = stop_loss * 2  # 2:1 risk-reward
            
            return StrategyVariant(
                strategy_id=str(uuid.uuid4()),
                base_strategy_name=base_strategy['name'],
                stock_name=stock_name,
                timeframe=timeframe,
                ranking=100,
                entry_conditions={
                    'oversold_threshold': 25 + (variant_idx * 5),  # 25, 30, 35
                    'overbought_threshold': 75 - (variant_idx * 5)  # 75, 70, 65
                },
                exit_conditions={'long_exit': 'rsi_14 > 60', 'short_exit': 'rsi_14 < 40'},
                intraday_rules={},
                risk_reward_ratios=[[1, 2]],
                risk_management={'stop_loss': stop_loss, 'take_profit': take_profit},
                position_sizing={'risk_per_trade': 0.02}
            )
            
        except Exception as e:
            logger.error(f"Error creating variant from GPU results: {e}")
            return None
    
    def create_fast_variant(self, base_strategy: Dict[str, Any], stock_name: str, 
                           timeframe: str, gpu_result: Dict[str, Any], variant_idx: int) -> StrategyVariant:
        """Create variant quickly without complex parameter optimization"""
        try:
            stop_loss = 0.015 + (variant_idx * 0.005)  # 1.5%, 2.0%
            take_profit = stop_loss * 2  # 2:1 risk-reward
            
            return StrategyVariant(
                strategy_id=str(uuid.uuid4()),
                base_strategy_name=base_strategy['name'],
                stock_name=stock_name,
                timeframe=timeframe,
                ranking=100,
                entry_conditions={
                    'oversold_threshold': 25 + (variant_idx * 5),  # 25, 30
                    'overbought_threshold': 75 - (variant_idx * 5)  # 75, 70
                },
                exit_conditions={'long_exit': 'rsi_14 > 60', 'short_exit': 'rsi_14 < 40'},
                intraday_rules={},
                risk_reward_ratios=[[1, 2]],
                risk_management={'stop_loss': stop_loss, 'take_profit': take_profit},
                position_sizing={'risk_per_trade': 0.02}
            )
            
        except Exception as e:
            logger.error(f"Error creating fast variant: {e}")
            return None
    
    def create_variant_from_trial(self, trial, base_strategy: Dict[str, Any],
                                 stock_name: str, timeframe: str) -> StrategyVariant:
        """Create strategy variant from Optuna trial parameters using proper strategy structure"""
        try:
            # Sample parameters for optimization
            risk_reward_ratio = [
                trial.suggest_float('risk_pct', 0.5, 3.0),
                trial.suggest_float('reward_pct', 1.0, 5.0)
            ]

            stop_loss_value = trial.suggest_float('stop_loss', 0.005, 0.03)
            take_profit_value = risk_reward_ratio[1] / risk_reward_ratio[0] * stop_loss_value

            # Extract proper entry/exit conditions from base strategy
            entry_conditions = {
                'long': base_strategy.get('entry', {}).get('long', 'rsi_14 < 30'),
                'short': base_strategy.get('entry', {}).get('short', 'rsi_14 > 70')
            }
            
            exit_conditions = {
                'long_exit': base_strategy.get('exit', {}).get('long', 'rsi_14 > 70'),
                'short_exit': base_strategy.get('exit', {}).get('short', 'rsi_14 < 30')
            }

            # Create variant with optimized parameters
            variant = StrategyVariant(
                strategy_id=str(uuid.uuid4()),
                base_strategy_name=base_strategy['name'],
                stock_name=stock_name,
                timeframe=timeframe,
                ranking=100,  # Will be updated after fitness evaluation
                entry_conditions=entry_conditions,
                exit_conditions=exit_conditions,
                intraday_rules=base_strategy.get('intraday_rules', {
                    'no_trades_after': '15:00',
                    'square_off_time': '15:20'
                }),
                risk_reward_ratios=[risk_reward_ratio],
                risk_management={
                    'stop_loss_type': 'percentage',
                    'stop_loss_value': stop_loss_value,
                    'take_profit_type': 'percentage',
                    'take_profit_value': take_profit_value
                },
                position_sizing=base_strategy.get('position_sizing', {
                    'method': 'fixed_amount',
                    'amount': 10000
                })
            )

            return variant

        except Exception as e:
            logger.error(f"Error creating variant from trial: {e}")
            return None
    
    def variant_to_backtesting_format(self, variant: StrategyVariant) -> Dict[str, Any]:
        """Convert strategy variant to backtesting format compatible with existing strategies.yaml structure"""
        try:
            # Load the base strategy from strategies.yaml to get the correct structure
            strategies_path = "agents/config/strategies.yaml"
            if Path(strategies_path).exists():
                with open(strategies_path, 'r', encoding='utf-8') as file:
                    data = yaml.safe_load(file)
                    strategies = data.get('strategies', [])
                    
                    # Find matching base strategy
                    base_strategy = None
                    for strategy in strategies:
                        if strategy.get('name') == variant.base_strategy_name:
                            base_strategy = strategy.copy()  # Make a copy to avoid modifying original
                            break
                    
                    if base_strategy:
                        # Use the existing strategy structure and only modify specific parameters
                        base_strategy['name'] = f"{variant.base_strategy_name}_evolved_{variant.stock_name}"
                        base_strategy['ranking'] = variant.ranking
                        
                        # Update risk management if provided
                        if variant.risk_management:
                            if 'risk_management' not in base_strategy:
                                base_strategy['risk_management'] = {}
                            base_strategy['risk_management'].update(variant.risk_management)
                        
                        # Update position sizing if provided
                        if variant.position_sizing:
                            if 'position_sizing' not in base_strategy:
                                base_strategy['position_sizing'] = {}
                            base_strategy['position_sizing'].update(variant.position_sizing)
                        
                        # Update risk reward ratios if provided
                        if variant.risk_reward_ratios:
                            base_strategy['risk_reward_ratios'] = variant.risk_reward_ratios

                        return base_strategy
                    else:
                        logger.warning(f"Base strategy {variant.base_strategy_name} not found in strategies.yaml")
        except Exception as e:
            logger.warning(f"Could not load base strategy structure: {e}")
        
        # Fallback: create a minimal strategy structure based on RSI_Reversal template
        return {
            'name': f"{variant.base_strategy_name}_evolved_{variant.stock_name}",
            'ranking': variant.ranking,
            'timeframe': [variant.timeframe],
            'entry': {
                'long': variant.entry_conditions.get('long', 'rsi_14 < 30 and close > ema_10'),
                'short': variant.entry_conditions.get('short', 'rsi_14 > 70 and close < ema_10')
            },
            'exit': {
                'long': variant.exit_conditions.get('long_exit', 'rsi_14 > 60 or close < ema_10'),
                'short': variant.exit_conditions.get('short_exit', 'rsi_14 < 40 or close > ema_10')
            },
            'risk_reward_ratios': variant.risk_reward_ratios or [[1, 2], [1.5, 2]],
            'risk_management': variant.risk_management or {
                'stop_loss_type': 'percentage',
                'stop_loss_value': 0.01,
                'take_profit_type': 'percentage', 
                'take_profit_value': 0.02
            },
            'position_sizing': variant.position_sizing or {
                'max_capital_multiplier': 3.5,
                'max_qty_formula': 'risk_per_trade / stock_price',
                'type': 'dynamic_risk_based'
            },
            'intraday_rules': variant.intraday_rules or {
                'exit_all_at': '15:10',
                'no_trade_after': '14:30'
            },
            'stock_name': variant.stock_name
        }
    
    def variant_to_yaml_format(self, variant: StrategyVariant) -> Dict[str, Any]:
        """Convert strategy variant to YAML format for strategies.yaml with enhanced metadata"""
        try:
            # Calculate best risk-reward ratio from performance
            best_risk_reward = "1:2"  # Default
            if variant.risk_reward_ratios:
                # Find the ratio with best performance
                best_risk_reward = variant.risk_reward_ratios[0] if isinstance(variant.risk_reward_ratios, list) else str(variant.risk_reward_ratios)

            # Extract key performance metrics for display
            performance_summary = {}
            if variant.performance_metrics:
                try:
                    metrics = json.loads(variant.performance_metrics) if isinstance(variant.performance_metrics, str) else variant.performance_metrics
                    performance_summary = {
                        'sharpe_ratio': round(metrics.get('sharpe_ratio', 0), 3),
                        'max_drawdown': round(metrics.get('max_drawdown', 0), 2),
                        'win_rate': round(metrics.get('win_rate', 0), 3),
                        'total_trades': int(metrics.get('total_trades', 0))
                    }
                except json.JSONDecodeError as e:
                    logger.error(f"JSONDecodeError in variant_to_yaml_format for strategy {variant.strategy_id}: {e}")
                except Exception as e:
                    logger.error(f"Error processing performance_metrics in variant_to_yaml_format for strategy {variant.strategy_id}: {e}")

            return {
                'name': f"{variant.base_strategy_name}_{variant.stock_name}_{variant.timeframe}",
                'ranking': int(variant.ranking),
                'timeframe': [variant.timeframe],
                'stock_name': variant.stock_name,
                'best_risk_reward': best_risk_reward,
                'performance_summary': performance_summary,
                'confidence_score': round(variant.confidence_score, 3) if variant.confidence_score else 0.0,
                'market_regime': variant.market_regime or 'neutral',
                'last_updated': variant.last_updated.strftime('%Y-%m-%d %H:%M:%S') if variant.last_updated else datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'entry': variant.entry_conditions,
                'exit': variant.exit_conditions,
                'intraday_rules': variant.intraday_rules,
                'risk_reward_ratios': variant.risk_reward_ratios,
                'risk_management': variant.risk_management,
                'position_sizing': variant.position_sizing
            }
            
        except Exception as e:
            logger.error(f"Error converting variant to YAML format: {e}")
            return {}
    
    def get_parameter_space(self, base_strategy: Dict[str, Any]) -> Dict[str, Any]:
        """Get parameter space for optimization based on strategy type"""
        try:
            strategy_name = base_strategy.get('name', '').lower()

            # Base parameter space
            param_space = {
                'risk_reward_ratio': {
                    'type': 'categorical',
                    'choices': [[1.0, 2.0], [1.5, 3.0], [2.0, 4.0], [2.5, 5.0]]
                }
            }

            # Strategy-specific parameters
            if 'rsi' in strategy_name:
                param_space.update({
                    'rsi_period': {'type': 'int', 'low': 10, 'high': 30},
                    'rsi_oversold': {'type': 'int', 'low': 20, 'high': 35},
                    'rsi_overbought': {'type': 'int', 'low': 65, 'high': 80}
                })
            elif 'ema' in strategy_name or 'sma' in strategy_name:
                param_space.update({
                    'fast_period': {'type': 'int', 'low': 5, 'high': 20},
                    'slow_period': {'type': 'int', 'low': 20, 'high': 50}
                })
            elif 'macd' in strategy_name:
                param_space.update({
                    'fast_period': {'type': 'int', 'low': 8, 'high': 15},
                    'slow_period': {'type': 'int', 'low': 20, 'high': 30},
                    'signal_period': {'type': 'int', 'low': 7, 'high': 12}
                })
            elif 'bollinger' in strategy_name:
                param_space.update({
                    'period': {'type': 'int', 'low': 15, 'high': 25},
                    'std_dev': {'type': 'float', 'low': 1.5, 'high': 2.5}
                })

            return param_space
            
        except Exception as e:
            logger.error(f"Error getting parameter space: {e}")
            return {'risk_reward_ratio': {'type': 'categorical', 'choices': [[1.0, 2.0]]}}
