#!/usr/bin/env python3
"""
YAML Manager Module

This module handles all operations related to strategies.yaml file including:
- Loading and saving YAML configurations
- Creating rotated backups
- Updating strategies with enhanced variants
- Managing YAML file structure
"""

import yaml
from pathlib import Path
from typing import Dict, List, Any
from datetime import datetime

from agents.strategy_evolution.evolution_logger import logger


class YAMLManager:
    """
    Manages strategies.yaml file operations and backups
    """
    
    def __init__(self, evolution_config):
        self.evolution_config = evolution_config
        self.strategies_path = "agents/config/strategies.yaml"
        logger.info("[YAML_MGR] YAML Manager initialized")
    
    def load_config(self, config_path: str) -> Dict[str, Any]:
        """Load configuration from YAML file"""
        try:
            if Path(config_path).exists():
                with open(config_path, 'r', encoding='utf-8') as file:
                    return yaml.safe_load(file)
            else:
                logger.warning(f"Config file not found: {config_path}, using defaults")
                return self.get_default_config()
        except Exception as e:
            logger.error(f"Error loading config: {e}")
            return self.get_default_config()
    
    def get_default_config(self) -> Dict[str, Any]:
        """Get default configuration"""
        return {
            'evolution': {
                'population_size': 50,
                'elite_size': 10,
                'max_generations': 100,
                'objectives': [
                    {'name': 'sharpe_ratio', 'weight': 0.4, 'direction': 'maximize'},
                    {'name': 'max_drawdown', 'weight': 0.3, 'direction': 'minimize'},
                    {'name': 'win_rate', 'weight': 0.3, 'direction': 'maximize'}
                ]
            },
            'storage': {
                'database_path': 'data/evolved_strategies.db',
                'backup_interval_hours': 24
            }
        }
    
    def create_rotated_backup(self, data: Dict[str, Any], max_backups: int = 5) -> str:
        """Create backup with rotation to prevent folder flooding"""
        try:
            backup_dir = Path("agents/config/backups")
            backup_dir.mkdir(exist_ok=True)

            # Create new backup
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_path = backup_dir / f"strategies_backup_{timestamp}.yaml"

            with open(backup_path, 'w', encoding='utf-8') as file:
                yaml.dump(data, file, default_flow_style=False, indent=2)

            # Clean up old backups (keep only the most recent max_backups)
            backup_files = sorted(backup_dir.glob("strategies_backup_*.yaml"),
                                key=lambda x: x.stat().st_mtime, reverse=True)

            # Remove excess backups
            for old_backup in backup_files[max_backups:]:
                try:
                    old_backup.unlink()
                except Exception as e:
                    logger.warning(f"Failed to remove old backup {old_backup}: {e}")

            return str(backup_path)

        except Exception as e:
            logger.error(f"Failed to create rotated backup: {e}")
            # Fallback to old method
            backup_path = f"agents/config/strategies_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.yaml"
            with open(backup_path, 'w', encoding='utf-8') as file:
                yaml.dump(data, file, default_flow_style=False, indent=2)
            return backup_path
    
    def load_strategies_yaml(self) -> Dict[str, Any]:
        """Load strategies.yaml file"""
        try:
            if not Path(self.strategies_path).exists():
                logger.warning(f"Strategies file not found: {self.strategies_path}")
                return {'strategies': []}
                
            with open(self.strategies_path, 'r', encoding='utf-8') as file:
                return yaml.safe_load(file) or {'strategies': []}
                
        except Exception as e:
            logger.error(f"Error loading strategies.yaml: {e}")
            return {'strategies': []}
    
    def save_strategies_yaml(self, data: Dict[str, Any]) -> bool:
        """Save strategies.yaml file"""
        try:
            with open(self.strategies_path, 'w', encoding='utf-8') as file:
                yaml.dump(data, file, default_flow_style=False, indent=2)
            return True
            
        except Exception as e:
            logger.error(f"Error saving strategies.yaml: {e}")
            return False
    
    async def update_strategies_yaml(self, enhanced_strategies: List[Dict[str, Any]]) -> bool:
        """Update strategies.yaml with enhanced strategies"""
        try:
            if not enhanced_strategies:
                logger.warning("No enhanced strategies provided for YAML update")
                return False

            # Load existing strategies
            data = self.load_strategies_yaml()
            existing_strategies = data.get('strategies', [])

            # Create backup with rotation (keep only last 5 backups)
            backup_path = self.create_rotated_backup(data)
            logger.info(f"📋 Created backup at {backup_path}")

            # Add enhanced strategies (avoid duplicates)
            existing_names = {s.get('name', '') for s in existing_strategies}
            new_strategies = []

            for enhanced_strategy in enhanced_strategies:
                strategy_name = enhanced_strategy.get('name', '')
                if strategy_name and strategy_name not in existing_names:
                    new_strategies.append(enhanced_strategy)
                elif strategy_name:
                    logger.debug(f"Skipping duplicate strategy: {strategy_name}")

            if not new_strategies:
                logger.warning("No new strategies to add (all were duplicates)")
                return False

            # Update the strategies list
            all_strategies = existing_strategies + new_strategies

            # Sort by ranking (descending)
            all_strategies.sort(key=lambda x: x.get('ranking', 0), reverse=True)

            # Update data
            data['strategies'] = all_strategies

            # Write updated strategies.yaml
            success = self.save_strategies_yaml(data)
            
            if success:
                logger.info(f"✅ Updated strategies.yaml with {len(new_strategies)} new enhanced strategies")
                print()  # Add line gap after YAML update
                return True
            else:
                logger.error("Failed to save updated strategies.yaml")
                return False

        except Exception as e:
            logger.error(f"Error updating strategies.yaml: {e}")
            return False
    
    def validate_yaml_structure(self, data: Dict[str, Any]) -> bool:
        """Validate the structure of strategies.yaml data"""
        try:
            if not isinstance(data, dict):
                logger.error("YAML data is not a dictionary")
                return False
                
            if 'strategies' not in data:
                logger.error("YAML data missing 'strategies' key")
                return False
                
            strategies = data['strategies']
            if not isinstance(strategies, list):
                logger.error("'strategies' is not a list")
                return False
                
            # Validate each strategy
            for i, strategy in enumerate(strategies):
                if not isinstance(strategy, dict):
                    logger.error(f"Strategy {i} is not a dictionary")
                    return False
                    
                required_fields = ['name', 'ranking']
                for field in required_fields:
                    if field not in strategy:
                        logger.error(f"Strategy {i} missing required field: {field}")
                        return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating YAML structure: {e}")
            return False
    
    def get_strategy_by_name(self, strategy_name: str) -> Dict[str, Any]:
        """Get a specific strategy by name from strategies.yaml"""
        try:
            data = self.load_strategies_yaml()
            strategies = data.get('strategies', [])
            
            for strategy in strategies:
                if strategy.get('name') == strategy_name:
                    return strategy
                    
            logger.warning(f"Strategy not found: {strategy_name}")
            return {}
            
        except Exception as e:
            logger.error(f"Error getting strategy by name: {e}")
            return {}
    
    def get_all_strategy_names(self) -> List[str]:
        """Get all strategy names from strategies.yaml"""
        try:
            data = self.load_strategies_yaml()
            strategies = data.get('strategies', [])
            
            return [strategy.get('name', '') for strategy in strategies if strategy.get('name')]
            
        except Exception as e:
            logger.error(f"Error getting strategy names: {e}")
            return []
    
    def remove_strategy_by_name(self, strategy_name: str) -> bool:
        """Remove a strategy by name from strategies.yaml"""
        try:
            data = self.load_strategies_yaml()
            strategies = data.get('strategies', [])
            
            # Create backup before modification
            backup_path = self.create_rotated_backup(data)
            logger.info(f"Created backup before removal: {backup_path}")
            
            # Filter out the strategy to remove
            original_count = len(strategies)
            strategies = [s for s in strategies if s.get('name') != strategy_name]
            
            if len(strategies) == original_count:
                logger.warning(f"Strategy not found for removal: {strategy_name}")
                return False
            
            # Update data and save
            data['strategies'] = strategies
            success = self.save_strategies_yaml(data)
            
            if success:
                logger.info(f"✅ Removed strategy: {strategy_name}")
                return True
            else:
                logger.error(f"Failed to save after removing strategy: {strategy_name}")
                return False
                
        except Exception as e:
            logger.error(f"Error removing strategy: {e}")
            return False
    
    def cleanup_old_backups(self, max_age_days: int = 30) -> int:
        """Clean up old backup files"""
        try:
            backup_dir = Path("agents/config/backups")
            if not backup_dir.exists():
                return 0
                
            cutoff_time = datetime.now().timestamp() - (max_age_days * 24 * 3600)
            removed_count = 0
            
            for backup_file in backup_dir.glob("strategies_backup_*.yaml"):
                if backup_file.stat().st_mtime < cutoff_time:
                    try:
                        backup_file.unlink()
                        removed_count += 1
                    except Exception as e:
                        logger.warning(f"Failed to remove old backup {backup_file}: {e}")
            
            if removed_count > 0:
                logger.info(f"Cleaned up {removed_count} old backup files")
                
            return removed_count
            
        except Exception as e:
            logger.error(f"Error cleaning up old backups: {e}")
            return 0
