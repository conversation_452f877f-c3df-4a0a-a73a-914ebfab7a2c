import os
import yaml
import polars as pl
from pathlib import Path
from typing import Dict, List, Any
from agents.strategy_evolution.evolution_config import EvolutionConfig
from agents.strategy_evolution.evolution_logger import logger

class StrategyDataLoader:
    """
    Handles loading of base strategies and discovery of the stock universe.
    Ensures real data is used and throws errors if data is not found.
    """
    def __init__(self, evolution_config: EvolutionConfig):
        self.evolution_config = evolution_config

    async def load_base_strategies(self) -> List[Dict[str, Any]]:
        """
        Load base strategies from strategies.yaml with dynamic strategy selection.
        Throws FileNotFoundError if strategies.yaml is not found.
        """
        strategies_path = "agents/config/strategies.yaml"
        if not Path(strategies_path).exists():
            error_message = f"Strategies file not found: {strategies_path}. Cannot load base strategies without real data."
            logger.error(error_message)
            raise FileNotFoundError(error_message)

        with open(strategies_path, 'r', encoding='utf-8') as file:
            data = yaml.safe_load(file)
            all_strategies = data.get('strategies', [])

        if not all_strategies:
            error_message = f"No strategies found in {strategies_path}. Cannot proceed with evolution."
            logger.error(error_message)
            raise ValueError(error_message)

        gpu_config = self.evolution_config.gpu_config
        max_strategies = gpu_config.get('strategy_batch_size', 150)

        strategy_selection_mode = self.evolution_config.strategy_selection.get('mode', 'ranking_based')
        base_strategies = []

        if strategy_selection_mode == 'base_only':
            base_strategies = [s for s in all_strategies if s.get('ranking', 100) == 0]
        elif strategy_selection_mode == 'top_performers':
            sorted_strategies = sorted(all_strategies, key=lambda x: x.get('ranking', 0), reverse=True)
            base_strategies = sorted_strategies[:max_strategies]
        elif strategy_selection_mode == 'diverse_selection':
            ranking_ranges = [
                (80, 100), (60, 79), (40, 59), (0, 39)
            ]
            strategies_per_range = max_strategies // len(ranking_ranges)
            for min_rank, max_rank in ranking_ranges:
                range_strategies = [s for s in all_strategies if min_rank <= s.get('ranking', 0) <= max_rank]
                range_strategies.sort(key=lambda x: x.get('ranking', 0), reverse=True)
                base_strategies.extend(range_strategies[:strategies_per_range])
        else: # Default: ranking_based
            min_ranking = self.evolution_config.strategy_selection.get('min_ranking', 0)
            candidate_strategies = [s for s in all_strategies if s.get('ranking', 0) >= min_ranking]
            candidate_strategies.sort(key=lambda x: x.get('ranking', 0), reverse=True)
            base_strategies = candidate_strategies[:max_strategies]

        if not base_strategies:
            logger.warning("No strategies found with current selection criteria, using top 50 strategies as fallback.")
            sorted_strategies = sorted(all_strategies, key=lambda x: x.get('ranking', 0), reverse=True)
            base_strategies = sorted_strategies[:50]
            if not base_strategies:
                error_message = "No strategies available even with fallback. Cannot proceed with evolution."
                logger.error(error_message)
                raise ValueError(error_message)

        logger.info(f"[LOAD] Loaded {len(base_strategies)} strategies for evolution (mode: {strategy_selection_mode})")
        return base_strategies

    async def discover_stock_universe(self) -> List[str]:
        """
        Discover available stocks from data files.
        Throws FileNotFoundError if the features directory or parquet files are not found.
        """
        logger.info("[DISCOVERY] Discovering stock universe...")
        data_dir = Path("data/features")
        if not data_dir.exists():
            error_message = "Features directory 'data/features' not found. Cannot discover stock universe without real data."
            logger.error(error_message)
            raise FileNotFoundError(error_message)

        stock_files = list(data_dir.glob("*.parquet"))
        if not stock_files:
            error_message = "No .parquet stock data files found in 'data/features'. Cannot discover stock universe."
            logger.error(error_message)
            raise FileNotFoundError(error_message)
        
        stocks = []
        for file_path in stock_files:
            parts = file_path.stem.split('_')
            if len(parts) >= 3 and parts[0] == 'features':
                stock_name = parts[1]
                stocks.append(stock_name.upper())
            else:
                logger.warning(f"Skipping file with unexpected name format: {file_path.name}")

        stocks = sorted(list(set(stocks)))
        if not stocks:
            error_message = "No valid stock names could be extracted from feature files. Cannot proceed with evolution."
            logger.error(error_message)
            raise ValueError(error_message)

        filtered_stocks = self._filter_stocks_by_criteria(stocks)
        print(f"📊 Discovered {len(filtered_stocks)} stocks for evolution testing")
        logger.info(f"[DISCOVERY] Discovered {len(filtered_stocks)} stocks.")
        return filtered_stocks

    def _filter_stocks_by_criteria(self, stocks: List[str]) -> List[str]:
        """
        Filter stocks based on selection criteria.
        Throws FileNotFoundError if the features data directory is not found.
        """
        criteria = self.evolution_config.stock_selection_criteria
        features_data_dir = Path("data/features")

        if not features_data_dir.exists():
            error_message = f"Features data directory '{features_data_dir}' not found. Cannot apply stock selection criteria."
            logger.error(error_message)
            raise FileNotFoundError(error_message)

        filtered_stocks = []
        # For now, we are lenient and add all stocks.
        # Real filtering logic would involve loading metadata for each stock and checking criteria.
        # Since we don't have that metadata here, we'll pass all discovered stocks.
        for stock_name in stocks:
            filtered_stocks.append(stock_name)
        
        if len(filtered_stocks) < len(stocks):
            logger.info(f"📊 Filtered out {len(stocks) - len(filtered_stocks)} stocks based on criteria")
        
        return filtered_stocks
