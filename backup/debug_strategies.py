#!/usr/bin/env python3
"""
Debug script to check strategy loading and processing
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

try:
    from agents.signal_agent import SignalAgent
except ImportError:
    # Try different import path
    import os
    os.chdir('/media/jmk/BKP/Documents/Equity')
    from agents.signal_agent import SignalAgent

def main():
    print("=== STRATEGY DEBUG ===")
    
    # Initialize SignalAgent
    strategies_file = "agents/config/strategies.yaml"
    agent = SignalAgent(strategies_file)
    
    # Get all strategies
    strategies = agent.get_strategies()
    
    print(f"Total strategies loaded: {len(strategies)}")
    print("\nStrategy details:")
    for i, strategy in enumerate(strategies, 1):
        name = strategy.get('name', 'Unnamed')
        ranking = strategy.get('ranking', 0)
        entry_long = strategy.get('entry', {}).get('long', 'NOT_DEFINED')
        entry_short = strategy.get('entry', {}).get('short', 'NOT_DEFINED')
        
        print(f"{i:2d}. {name} (ranking: {ranking})")
        print(f"    Long:  {entry_long}")
        print(f"    Short: {entry_short}")
        print()

if __name__ == "__main__":
    main()