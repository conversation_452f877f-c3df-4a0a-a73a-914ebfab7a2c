#!/usr/bin/env python3
"""
Multi-Agent Trading System Test Suite

This module provides comprehensive testing for the multi-agent trading system
implementation, covering all major components and scenarios.

Test Categories:
🧪 Unit Tests - Individual component testing
🔄 Integration Tests - Multi-component interaction testing
🚨 Scenario Tests - Real-world scenario simulation
📊 Performance Tests - System performance validation
🛡️ Recovery Tests - System recovery and fault tolerance
"""

import os
import sys
import asyncio
import logging
import pytest
import tempfile
import shutil
from datetime import datetime, timedelta
from pathlib import Path
from unittest.mock import Mock, AsyncMock, patch
import json

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

# Import system components
from agents.execution.worker_state_manager import WorkerStateManager, WorkerState
from agents.execution.execution_worker_pool import ExecutionWorkerPool
from agents.live_trading_orchestrator import LiveTradingOrchestrator, SystemConfiguration
from agents.state_persistence.persistence_manager import PersistenceManager, PersistenceConfig, PersistenceBackend
from agents.performance_analysis.enhanced_performance_agent import EnhancedPerformanceAgent
from agents.system_recovery.recovery_manager import SystemRecoveryManager

# Configure logging for tests
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TestMultiAgentSystem:
    """Comprehensive test suite for multi-agent trading system"""
    
    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for test data"""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def test_config(self, temp_dir):
        """Create test configuration"""
        return {
            'max_trades': 3,
            'trading_mode': 'paper',
            'symbol_universe': ['RELIANCE-EQ', 'TCS-EQ', 'HDFCBANK-EQ'],
            'base_path': temp_dir
        }
    
    @pytest.fixture
    async def worker_state_manager(self, test_config):
        """Create and initialize worker state manager for testing"""
        # Set environment variables for testing
        os.environ['MAX_TRADES'] = str(test_config['max_trades'])
        os.environ['STATE_PERSISTENCE_PATH'] = test_config['base_path']
        
        manager = WorkerStateManager()
        await manager.initialize(test_config['symbol_universe'])
        yield manager
        await manager.shutdown()
    
    @pytest.fixture
    async def persistence_manager(self, test_config):
        """Create and initialize persistence manager for testing"""
        config = PersistenceConfig(
            backend=PersistenceBackend.FILE,
            base_path=test_config['base_path']
        )
        manager = PersistenceManager(config)
        await manager.initialize()
        yield manager
        await manager.shutdown()
    
    @pytest.fixture
    async def performance_agent(self):
        """Create and initialize performance agent for testing"""
        agent = EnhancedPerformanceAgent()
        await agent.initialize()
        yield agent
        await agent.shutdown()
    
    @pytest.fixture
    async def recovery_manager(self):
        """Create recovery manager for testing"""
        manager = SystemRecoveryManager()
        yield manager
    
    # ═══════════════════════════════════════════════════════════════════════════════
    # UNIT TESTS
    # ═══════════════════════════════════════════════════════════════════════════════
    
    @pytest.mark.asyncio
    async def test_worker_state_manager_initialization(self, worker_state_manager):
        """Test worker state manager initialization"""
        logger.info("[TEST] Testing worker state manager initialization...")
        
        # Check system status
        status = worker_state_manager.get_system_status()
        
        assert status['status'] == 'running'
        assert status['max_trades'] == 3
        assert status['total_workers'] == 3
        assert len(status['worker_states']) > 0
        
        logger.info("[SUCCESS] Worker state manager initialization test passed")
    
    @pytest.mark.asyncio
    async def test_worker_assignment_and_trade_execution(self, worker_state_manager):
        """Test worker assignment and trade execution flow"""
        logger.info("[TEST] Testing worker assignment and trade execution...")
        
        # Get available worker
        worker = worker_state_manager.get_available_worker('RELIANCE-EQ')
        assert worker is not None
        assert worker.current_state == WorkerState.IDLE
        
        # Assign trade
        success = await worker_state_manager.assign_trade_to_worker(
            worker.worker_id, 'test_trade_001', 'RELIANCE-EQ'
        )
        assert success
        
        # Check worker state changed to ACTIVE
        updated_worker = worker_state_manager.get_worker_details(worker.worker_id)
        assert updated_worker['state'] == 'ACTIVE'
        
        # Complete trade
        success = await worker_state_manager.complete_worker_trade(worker.worker_id, 150.0)
        assert success
        
        # Check worker state changed (IDLE or COOLDOWN depending on config)
        final_worker = worker_state_manager.get_worker_details(worker.worker_id)
        assert final_worker['state'] in ['IDLE', 'COOLDOWN']
        
        logger.info("[SUCCESS] Worker assignment and trade execution test passed")
    
    @pytest.mark.asyncio
    async def test_over_trading_prevention(self, worker_state_manager):
        """Test over-trading prevention mechanism"""
        logger.info("[TEST] Testing over-trading prevention...")
        
        # Try to assign trades to all workers
        assigned_workers = []
        for i in range(5):  # Try to assign more trades than MAX_TRADES
            worker = worker_state_manager.get_available_worker('RELIANCE-EQ')
            if worker:
                success = await worker_state_manager.assign_trade_to_worker(
                    worker.worker_id, f'test_trade_{i:03d}', 'RELIANCE-EQ'
                )
                if success:
                    assigned_workers.append(worker.worker_id)
        
        # Should only be able to assign up to MAX_TRADES
        assert len(assigned_workers) <= 3  # MAX_TRADES = 3
        
        # Try to get another available worker - should return None
        no_worker = worker_state_manager.get_available_worker('TCS-EQ')
        assert no_worker is None
        
        logger.info(f"[SUCCESS] Over-trading prevention test passed - assigned {len(assigned_workers)} trades")
    
    @pytest.mark.asyncio
    async def test_state_persistence(self, persistence_manager, worker_state_manager):
        """Test state persistence functionality"""
        logger.info("[TEST] Testing state persistence...")
        
        # Create test snapshot
        from agents.state_persistence.persistence_manager import create_state_snapshot
        
        system_status = worker_state_manager.get_system_status()
        worker_details = worker_state_manager.get_worker_details()
        
        snapshot = await create_state_snapshot(
            system_state=system_status,
            worker_states=worker_details.get('workers', {}),
            system_metrics={'test_run': True}
        )
        
        # Save snapshot
        success = await persistence_manager.save_state_snapshot(snapshot)
        assert success
        
        # Load snapshot
        loaded_snapshot = await persistence_manager.load_latest_snapshot()
        assert loaded_snapshot is not None
        assert loaded_snapshot.system_state['max_trades'] == system_status['max_trades']
        
        logger.info("[SUCCESS] State persistence test passed")
    
    @pytest.mark.asyncio
    async def test_performance_tracking(self, performance_agent):
        """Test performance tracking functionality"""
        logger.info("[TEST] Testing performance tracking...")
        
        # Record some test trades
        await performance_agent.record_trade_completion(
            worker_id='worker_001',
            trade_id='test_trade_001',
            pnl=150.0,
            symbol='RELIANCE-EQ',
            strategy='test_strategy',
            duration_minutes=45.0
        )
        
        await performance_agent.record_trade_completion(
            worker_id='worker_001',
            trade_id='test_trade_002',
            pnl=-75.0,
            symbol='TCS-EQ',
            strategy='test_strategy',
            duration_minutes=30.0
        )
        
        # Get performance data
        performance_data = await performance_agent.get_system_performance()
        
        assert 'worker_performance' in performance_data
        assert 'worker_001' in performance_data['worker_performance']
        
        worker_perf = performance_data['worker_performance']['worker_001']
        assert worker_perf['total_trades'] == 2
        assert worker_perf['total_pnl'] == 75.0  # 150 - 75
        assert worker_perf['win_rate'] == 0.5  # 1 win out of 2 trades
        
        logger.info("[SUCCESS] Performance tracking test passed")
    
    @pytest.mark.asyncio
    async def test_system_recovery(self, recovery_manager, worker_state_manager):
        """Test system recovery functionality"""
        logger.info("[TEST] Testing system recovery...")
        
        # Inject dependencies
        recovery_manager.inject_dependencies(worker_state_manager=worker_state_manager)
        
        # Perform health check
        health_status = await recovery_manager.perform_health_check()
        assert 'health_score' in health_status
        assert health_status['health_score'] >= 0.0
        
        # Perform startup recovery
        recovery_report = await recovery_manager.perform_startup_recovery()
        assert recovery_report is not None
        assert recovery_report.recovery_status.value in ['completed', 'partial_recovery', 'failed']
        
        logger.info(f"[SUCCESS] System recovery test passed - Status: {recovery_report.recovery_status.value}")
    
    # ═══════════════════════════════════════════════════════════════════════════════
    # INTEGRATION TESTS
    # ═══════════════════════════════════════════════════════════════════════════════
    
    @pytest.mark.asyncio
    async def test_full_system_integration(self, test_config, temp_dir):
        """Test full system integration"""
        logger.info("[TEST] Testing full system integration...")
        
        # Set environment variables
        os.environ['MAX_TRADES'] = str(test_config['max_trades'])
        os.environ['STATE_PERSISTENCE_PATH'] = temp_dir
        os.environ['TRADING_MODE'] = 'paper'
        
        # Create system configuration
        config = SystemConfiguration(
            max_trades=test_config['max_trades'],
            trading_mode='paper',
            symbol_universe=test_config['symbol_universe']
        )
        
        # Create orchestrator (this will test the full integration)
        orchestrator = LiveTradingOrchestrator(config)
        
        try:
            # Initialize system
            success = await orchestrator.initialize()
            assert success, "System initialization should succeed"
            
            # Get system status
            status = orchestrator.get_system_status()
            assert status['orchestrator_running'] == True
            assert status['total_workers'] == test_config['max_trades']
            
            logger.info("[SUCCESS] Full system integration test passed")
            
        finally:
            # Cleanup
            await orchestrator.shutdown()
    
    # ═══════════════════════════════════════════════════════════════════════════════
    # SCENARIO TESTS
    # ═══════════════════════════════════════════════════════════════════════════════
    
    @pytest.mark.asyncio
    async def test_system_restart_scenario(self, test_config, temp_dir):
        """Test system restart with active positions scenario"""
        logger.info("[TEST] Testing system restart scenario...")
        
        # Set environment variables
        os.environ['MAX_TRADES'] = str(test_config['max_trades'])
        os.environ['STATE_PERSISTENCE_PATH'] = temp_dir
        
        # First run - create system and simulate active trades
        config = SystemConfiguration(
            max_trades=test_config['max_trades'],
            trading_mode='paper',
            symbol_universe=test_config['symbol_universe']
        )
        
        orchestrator1 = LiveTradingOrchestrator(config)
        
        try:
            # Initialize and start first instance
            await orchestrator1.initialize()
            
            # Simulate some activity (this would create state to persist)
            status1 = orchestrator1.get_system_status()
            assert status1['orchestrator_running'] == True
            
            # Shutdown first instance (simulating crash/restart)
            await orchestrator1.shutdown()
            
        except Exception as e:
            logger.error(f"First instance error: {e}")
            await orchestrator1.shutdown()
        
        # Second run - restart system and test recovery
        orchestrator2 = LiveTradingOrchestrator(config)
        
        try:
            # Initialize second instance (should recover from persisted state)
            success = await orchestrator2.initialize()
            assert success, "System restart should succeed"
            
            # Check that system recovered properly
            status2 = orchestrator2.get_system_status()
            assert status2['orchestrator_running'] == True
            assert status2['total_workers'] == test_config['max_trades']
            
            logger.info("[SUCCESS] System restart scenario test passed")
            
        finally:
            await orchestrator2.shutdown()
    
    @pytest.mark.asyncio
    async def test_worker_error_recovery(self, worker_state_manager):
        """Test worker error recovery scenario"""
        logger.info("[TEST] Testing worker error recovery...")
        
        # Get a worker and simulate error
        worker = worker_state_manager.get_available_worker('RELIANCE-EQ')
        assert worker is not None
        
        # Simulate worker error
        await worker_state_manager.handle_worker_error(worker.worker_id, "Simulated error for testing")
        
        # Check worker is in error state
        worker_details = worker_state_manager.get_worker_details(worker.worker_id)
        assert worker_details['state'] == 'ERROR'
        
        # Wait a bit for recovery (in real system, this would be automatic)
        await asyncio.sleep(1)
        
        # Force recovery
        from agents.execution.worker_state_manager import WorkerState
        success = await worker_state_manager.force_worker_state_change(
            worker.worker_id, WorkerState.IDLE, "Test recovery"
        )
        assert success
        
        # Check worker recovered
        recovered_worker = worker_state_manager.get_worker_details(worker.worker_id)
        assert recovered_worker['state'] == 'IDLE'
        
        logger.info("[SUCCESS] Worker error recovery test passed")


# ═══════════════════════════════════════════════════════════════════════════════
# PERFORMANCE TESTS
# ═══════════════════════════════════════════════════════════════════════════════

class TestSystemPerformance:
    """Performance and load testing for the multi-agent system"""
    
    @pytest.mark.asyncio
    async def test_concurrent_worker_operations(self):
        """Test concurrent worker operations performance"""
        logger.info("[PERF] Testing concurrent worker operations...")
        
        # Set up test environment
        os.environ['MAX_TRADES'] = '10'
        
        manager = WorkerStateManager()
        test_symbols = [f'SYMBOL_{i:03d}-EQ' for i in range(20)]
        
        try:
            await manager.initialize(test_symbols)
            
            # Simulate concurrent operations
            start_time = datetime.now()
            
            tasks = []
            for i in range(50):  # 50 concurrent operations
                task = asyncio.create_task(self._simulate_trade_cycle(manager, f'trade_{i:03d}'))
                tasks.append(task)
            
            # Wait for all operations to complete
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            # Check results
            successful_operations = sum(1 for r in results if r is True)
            
            logger.info(f"[PERF] Completed {successful_operations}/{len(tasks)} operations in {duration:.2f}s")
            
            # Performance assertions
            assert duration < 30.0, "Operations should complete within 30 seconds"
            assert successful_operations >= len(tasks) * 0.8, "At least 80% of operations should succeed"
            
        finally:
            await manager.shutdown()
    
    async def _simulate_trade_cycle(self, manager, trade_id):
        """Simulate a complete trade cycle"""
        try:
            # Try to get available worker
            worker = manager.get_available_worker('RELIANCE-EQ')
            if not worker:
                return False
            
            # Assign trade
            success = await manager.assign_trade_to_worker(worker.worker_id, trade_id, 'RELIANCE-EQ')
            if not success:
                return False
            
            # Simulate trade duration
            await asyncio.sleep(0.1)
            
            # Complete trade
            success = await manager.complete_worker_trade(worker.worker_id, 100.0)
            return success
            
        except Exception as e:
            logger.error(f"Trade cycle error: {e}")
            return False


# ═══════════════════════════════════════════════════════════════════════════════
# TEST RUNNER
# ═══════════════════════════════════════════════════════════════════════════════

async def run_all_tests():
    """Run all tests manually (for development)"""
    logger.info("=" * 80)
    logger.info("STARTING MULTI-AGENT TRADING SYSTEM TEST SUITE")
    logger.info("=" * 80)
    
    # This would run all tests manually if needed
    # For now, use pytest to run the tests
    
    logger.info("Use 'pytest tests/test_multi_agent_system.py -v' to run all tests")


if __name__ == "__main__":
    asyncio.run(run_all_tests())
