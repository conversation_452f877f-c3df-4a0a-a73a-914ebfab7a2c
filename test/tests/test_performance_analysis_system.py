#!/usr/bin/env python3
"""
Comprehensive Test Suite for Enhanced Performance Analysis System

Tests all components of the performance analysis system including:
- Performance Analysis Gateway
- Data Ingestion modules
- Advanced Metrics calculations
- ML-powered predictions
- Integration with other agents
- Real-time processing and alerts
"""

import pytest
import asyncio
import numpy as np
import polars as pl
from datetime import datetime, timedelta
from unittest.mock import Mock, AsyncMock, patch
import tempfile
import os
from pathlib import Path

# Import system components
from agents.performance_analysis_gateway import PerformanceAnalysisGateway
from agents.performance_analysis.data_ingestion import (
    StreamProcessor, TradeReconciler, DataValidator, OrderMatcher,
    StreamConfig, ReconciliationConfig, ValidationRule, ValidationSeverity
)
from agents.performance_analysis.advanced_metrics import (
    CoreMetricsCalculator, RiskMetricsCalculator, DrawdownAnalyzer
)
from agents.signal_generation.integration import (
    PerformanceFeedbackHandler, PerformanceAlert, AlertSeverity
)
from agents.strategy_evolution.integration import (
    PerformancePredictionHandler, PerformancePrediction, PredictionType
)
from agents.ai_training.integration import (
    PerformanceModelTrainer, ModelType, ModelConfig
)

class TestPerformanceAnalysisGateway:
    """Test suite for Performance Analysis Gateway"""
    
    @pytest.fixture
    async def gateway(self):
        """Create test gateway instance"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_path = os.path.join(temp_dir, "test_config.yaml")
            
            # Create minimal test config
            test_config = """
gateway_config:
  risk_free_rate: 0.06
  benchmark_return: 0.12
  initial_capital: 100000
  api_port: 8080
  websocket_port: 8081
  health_check_interval_seconds: 60
  metrics_retention_days: 30

stream_config:
  buffer_size: 1000
  batch_size: 100
  flush_interval_seconds: 5
  max_out_of_order_delay_seconds: 30
  enable_deduplication: true

reconciliation_config:
  time_tolerance_seconds: 30
  price_tolerance_percent: 0.1
  quantity_tolerance_percent: 0.0
  min_confidence_score: 0.8

backfill_config:
  batch_size: 1000
  max_concurrent_tasks: 3
  retry_attempts: 3
  temp_storage_path: "{temp_dir}/backfill"
""".format(temp_dir=temp_dir)
            
            with open(config_path, 'w') as f:
                f.write(test_config)
            
            gateway = PerformanceAnalysisGateway(config_path)
            yield gateway
            
            # Cleanup
            if gateway.is_running:
                await gateway.stop()

    @pytest.mark.asyncio
    async def test_gateway_initialization(self, gateway):
        """Test gateway initialization"""
        assert gateway is not None
        assert not gateway.is_running
        assert gateway.config is not None
        assert gateway.stream_processor is not None
        assert gateway.trade_reconciler is not None
        assert gateway.data_validator is not None

    @pytest.mark.asyncio
    async def test_trade_data_ingestion(self, gateway):
        """Test trade data ingestion"""
        trade_data = {
            'trade_id': 'TEST_001',
            'symbol': 'RELIANCE',
            'side': 'BUY',
            'quantity': 100,
            'price': 2500.0,
            'timestamp': datetime.now().isoformat(),
            'strategy': 'test_strategy'
        }
        
        result = await gateway.ingest_trade_data(trade_data, 'test_source')
        
        assert result['status'] == 'success'
        assert 'event_id' in result

    @pytest.mark.asyncio
    async def test_performance_metrics_calculation(self, gateway):
        """Test performance metrics calculation"""
        # Create sample trades data
        trades_data = [
            {
                'trade_id': f'TEST_{i:03d}',
                'symbol': 'RELIANCE',
                'side': 'BUY' if i % 2 == 0 else 'SELL',
                'quantity': 100,
                'price': 2500.0 + np.random.normal(0, 50),
                'entry_time': datetime.now() - timedelta(days=30-i),
                'exit_time': datetime.now() - timedelta(days=30-i) + timedelta(hours=2),
                'pnl': np.random.normal(100, 200),
                'strategy': 'test_strategy'
            }
            for i in range(50)
        ]
        
        result = await gateway.calculate_performance_metrics(trades_data)
        
        assert result['status'] == 'success'
        assert 'core_metrics' in result
        assert 'risk_metrics' in result
        assert 'drawdown_metrics' in result

class TestDataIngestionModules:
    """Test suite for Data Ingestion modules"""
    
    @pytest.fixture
    def stream_processor(self):
        """Create test stream processor"""
        config = StreamConfig(
            buffer_size=1000,
            batch_size=10,
            flush_interval_seconds=1
        )
        return StreamProcessor(config)

    @pytest.fixture
    def trade_reconciler(self):
        """Create test trade reconciler"""
        config = ReconciliationConfig(
            time_tolerance_seconds=30,
            price_tolerance_percent=0.1
        )
        return TradeReconciler(config)

    @pytest.fixture
    def data_validator(self):
        """Create test data validator"""
        return DataValidator()

    def test_stream_processor_initialization(self, stream_processor):
        """Test stream processor initialization"""
        assert stream_processor is not None
        assert stream_processor.config.buffer_size == 1000
        assert len(stream_processor.event_buffer) == 0

    @pytest.mark.asyncio
    async def test_stream_event_ingestion(self, stream_processor):
        """Test stream event ingestion"""
        from agents.performance_analysis.data_ingestion.stream_processor import StreamEvent
        
        event = StreamEvent(
            event_id='test_001',
            event_type='trade',
            source='test',
            timestamp=datetime.now(),
            data={'symbol': 'RELIANCE', 'price': 2500.0}
        )
        
        success = await stream_processor.ingest_event(event)
        assert success
        assert len(stream_processor.event_buffer) == 1

    def test_data_validation(self, data_validator):
        """Test data validation"""
        trade_data = {
            'trade_id': 'TEST_001',
            'symbol': 'RELIANCE',
            'side': 'BUY',
            'quantity': 100,
            'price': 2500.0,
            'timestamp': datetime.now().isoformat()
        }
        
        report = data_validator.validate_record(trade_data, 'trade')
        
        assert report is not None
        assert report.overall_result.value in ['passed', 'fixed']

    def test_trade_reconciliation(self, trade_reconciler):
        """Test trade reconciliation"""
        from agents.performance_analysis.data_ingestion.trade_reconciler import TradeRecord, DataSource
        
        # Add test trades
        trade1 = TradeRecord(
            source=DataSource.EXECUTION_AGENT,
            trade_id='TEST_001',
            symbol='RELIANCE',
            side='BUY',
            quantity=100,
            price=2500.0,
            timestamp=datetime.now()
        )
        
        trade2 = TradeRecord(
            source=DataSource.ANGEL_ONE,
            trade_id='TEST_001',
            symbol='RELIANCE',
            side='BUY',
            quantity=100,
            price=2500.0,
            timestamp=datetime.now()
        )
        
        trade_reconciler.add_trade(trade1)
        trade_reconciler.add_trade(trade2)
        
        matches = trade_reconciler.reconcile_trades()
        assert len(matches) > 0

class TestAdvancedMetrics:
    """Test suite for Advanced Metrics calculations"""
    
    @pytest.fixture
    def sample_trades_df(self):
        """Create sample trades DataFrame"""
        np.random.seed(42)
        
        trades_data = []
        for i in range(100):
            entry_time = datetime.now() - timedelta(days=100-i)
            exit_time = entry_time + timedelta(hours=np.random.randint(1, 24))
            pnl = np.random.normal(50, 150)  # Mean profit of 50 with std 150
            
            trades_data.append({
                'trade_id': f'TEST_{i:03d}',
                'symbol': 'RELIANCE',
                'side': 'BUY' if i % 2 == 0 else 'SELL',
                'quantity': 100,
                'price': 2500.0 + np.random.normal(0, 50),
                'entry_time': entry_time,
                'exit_time': exit_time,
                'pnl': pnl,
                'strategy': f'strategy_{i % 3}'
            })
        
        return pl.DataFrame(trades_data)

    def test_core_metrics_calculation(self, sample_trades_df):
        """Test core metrics calculation"""
        calculator = CoreMetricsCalculator()
        metrics = calculator.calculate_metrics(sample_trades_df)
        
        assert metrics.total_trades == 100
        assert metrics.win_rate >= 0
        assert metrics.win_rate <= 100
        assert metrics.total_pnl is not None
        assert metrics.sharpe_ratio is not None

    def test_risk_metrics_calculation(self, sample_trades_df):
        """Test risk metrics calculation"""
        calculator = RiskMetricsCalculator()
        metrics = calculator.calculate_risk_metrics(sample_trades_df)
        
        assert metrics.sortino_ratio is not None
        assert metrics.var_95 <= 0  # VaR should be negative
        assert metrics.var_99 <= metrics.var_95  # 99% VaR should be worse than 95%
        assert metrics.max_drawdown >= 0

    def test_drawdown_analysis(self, sample_trades_df):
        """Test drawdown analysis"""
        analyzer = DrawdownAnalyzer()
        
        # Create equity curve
        cumulative_pnl = sample_trades_df.select(pl.col('pnl').cumsum().alias('equity'))
        timestamps = sample_trades_df.select(pl.col('entry_time').alias('timestamp'))
        equity_curve = pl.concat([timestamps, cumulative_pnl], how='horizontal')
        
        metrics, periods = analyzer.analyze_drawdowns(equity_curve)
        
        assert metrics.max_drawdown_percent >= 0
        assert metrics.total_drawdown_periods >= 0
        assert isinstance(periods, list)

class TestMLIntegration:
    """Test suite for ML integration components"""
    
    @pytest.fixture
    def mock_signal_agent(self):
        """Create mock signal generation agent"""
        agent = Mock()
        agent.strategies = {
            'test_strategy': {
                'enabled': True,
                'position_sizing': {'base_size_percent': 2.0},
                'risk_management': {'stop_loss_percent': 2.0},
                'signal_validation': {'min_confidence': 0.6}
            }
        }
        return agent

    @pytest.fixture
    def mock_evolution_agent(self):
        """Create mock strategy evolution agent"""
        agent = Mock()
        agent.evolution_state = Mock()
        agent.evolution_state.population = []
        agent.genetic_operations = Mock()
        agent.genetic_operations.mutation_rate = 0.1
        agent.genetic_operations.elite_size = 10
        agent.genetic_operations.population_size = 100
        return agent

    @pytest.fixture
    def mock_ai_agent(self):
        """Create mock AI training agent"""
        agent = Mock()
        agent.config = {'models_directory': tempfile.mkdtemp()}
        return agent

    def test_performance_feedback_handler(self, mock_signal_agent):
        """Test performance feedback handler"""
        handler = PerformanceFeedbackHandler(mock_signal_agent)
        
        assert handler is not None
        assert handler.signal_agent == mock_signal_agent
        assert len(handler.response_configs) > 0

    @pytest.mark.asyncio
    async def test_performance_alert_handling(self, mock_signal_agent):
        """Test performance alert handling"""
        handler = PerformanceFeedbackHandler(mock_signal_agent)
        
        alert = PerformanceAlert(
            alert_id='test_alert_001',
            alert_type='low_win_rate_anomaly',
            severity=AlertSeverity.WARNING,
            strategy='test_strategy',
            symbol='RELIANCE',
            metric_name='win_rate',
            current_value=0.3,
            threshold_value=0.4,
            message='Win rate below threshold',
            timestamp=datetime.now()
        )
        
        result = await handler.handle_performance_alert(alert)
        assert result is True

    def test_performance_prediction_handler(self, mock_evolution_agent):
        """Test performance prediction handler"""
        handler = PerformancePredictionHandler(mock_evolution_agent)
        
        assert handler is not None
        assert handler.evolution_agent == mock_evolution_agent

    @pytest.mark.asyncio
    async def test_performance_prediction_handling(self, mock_evolution_agent):
        """Test performance prediction handling"""
        handler = PerformancePredictionHandler(mock_evolution_agent)
        
        prediction_data = {
            'prediction_id': 'pred_001',
            'strategy_id': 'test_strategy',
            'prediction_type': 'sharpe_ratio',
            'predicted_value': 0.3,
            'confidence': 0.8,
            'time_horizon_days': 30,
            'timestamp': datetime.now().isoformat(),
            'model_version': 'v1.0'
        }
        
        result = await handler.handle_performance_prediction(prediction_data)
        assert result is True

    def test_performance_model_trainer(self, mock_ai_agent):
        """Test performance model trainer"""
        trainer = PerformanceModelTrainer(mock_ai_agent)
        
        assert trainer is not None
        assert trainer.ai_training_agent == mock_ai_agent
        assert len(trainer.model_configs) > 0

    @pytest.mark.asyncio
    async def test_model_training(self, mock_ai_agent):
        """Test model training"""
        trainer = PerformanceModelTrainer(mock_ai_agent)
        
        # Create sample training data
        training_data = pl.DataFrame({
            'total_trades': [10, 20, 30, 40, 50],
            'win_rate': [0.6, 0.5, 0.7, 0.4, 0.8],
            'profit_factor': [1.5, 1.2, 2.0, 0.8, 2.5],
            'volatility': [0.1, 0.15, 0.08, 0.2, 0.05],
            'sharpe_ratio': [1.2, 0.8, 1.8, 0.3, 2.1],
            'roi_percent': [0.12, 0.08, 0.18, 0.03, 0.21],
            'max_drawdown_percent': [0.05, 0.08, 0.03, 0.15, 0.02]
        })
        
        # Mock the training process to avoid actual ML training in tests
        with patch.object(trainer, '_train_single_model') as mock_train:
            mock_train.return_value = {
                'status': 'success',
                'accuracy': 0.85,
                'model_type': 'sharpe_ratio'
            }
            
            result = await trainer.train_performance_models(training_data)
            
            assert result['status'] == 'success'

class TestIntegrationScenarios:
    """Test suite for end-to-end integration scenarios"""
    
    @pytest.mark.asyncio
    async def test_full_pipeline_integration(self):
        """Test full pipeline from data ingestion to ML predictions"""
        # This would test the complete flow:
        # 1. Data ingestion
        # 2. Validation and reconciliation
        # 3. Metrics calculation
        # 4. Anomaly detection
        # 5. Alert generation
        # 6. ML prediction
        # 7. Response execution
        
        # For now, we'll create a simplified test
        assert True  # Placeholder for full integration test

    @pytest.mark.asyncio
    async def test_performance_degradation_scenario(self):
        """Test scenario where strategy performance degrades"""
        # Simulate performance degradation and test system response
        assert True  # Placeholder

    @pytest.mark.asyncio
    async def test_market_regime_change_scenario(self):
        """Test scenario where market regime changes"""
        # Simulate market regime change and test adaptation
        assert True  # Placeholder

# Test configuration
@pytest.fixture(scope="session")
def event_loop():
    """Create event loop for async tests"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v", "--tb=short"])
