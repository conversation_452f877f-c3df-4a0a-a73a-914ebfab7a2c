#!/usr/bin/env python3
"""
Test Runner for Signal Generation System
Comprehensive test execution with coverage reporting
"""

import pytest
import sys
import os
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))


def run_all_tests():
    """Run all signal generation tests"""
    test_dir = Path(__file__).parent
    
    # Test configuration
    pytest_args = [
        str(test_dir),
        "-v",  # Verbose output
        "--tb=short",  # Short traceback format
        "--strict-markers",  # Strict marker checking
        "--disable-warnings",  # Disable warnings for cleaner output
        "-x",  # Stop on first failure
    ]
    
    # Add coverage if available
    try:
        import pytest_cov
        pytest_args.extend([
            "--cov=agents.signal_generation",
            "--cov=agents.signal_generation_gateway",
            "--cov-report=html:tests/coverage_html",
            "--cov-report=term-missing",
            "--cov-fail-under=80"
        ])
        print("Running tests with coverage reporting...")
    except ImportError:
        print("Running tests without coverage (install pytest-cov for coverage)")
    
    # Run tests
    exit_code = pytest.main(pytest_args)
    
    if exit_code == 0:
        print("\n✅ All tests passed!")
    else:
        print(f"\n❌ Tests failed with exit code: {exit_code}")
    
    return exit_code


def run_specific_component_tests(component_name):
    """Run tests for a specific component"""
    test_file = Path(__file__).parent / f"test_{component_name}.py"
    
    if not test_file.exists():
        print(f"❌ Test file not found: {test_file}")
        return 1
    
    pytest_args = [
        str(test_file),
        "-v",
        "--tb=short"
    ]
    
    print(f"Running tests for {component_name}...")
    exit_code = pytest.main(pytest_args)
    
    if exit_code == 0:
        print(f"\n✅ {component_name} tests passed!")
    else:
        print(f"\n❌ {component_name} tests failed!")
    
    return exit_code


def run_integration_tests():
    """Run integration tests"""
    test_files = [
        "test_signal_generation_gateway.py",
        "test_integration_manager.py"
    ]
    
    for test_file in test_files:
        test_path = Path(__file__).parent / test_file
        if test_path.exists():
            print(f"Running integration test: {test_file}")
            exit_code = pytest.main([str(test_path), "-v"])
            if exit_code != 0:
                return exit_code
    
    return 0


def run_performance_tests():
    """Run performance tests"""
    print("Running performance tests...")
    
    # Performance test configuration
    pytest_args = [
        str(Path(__file__).parent),
        "-v",
        "-m", "performance",  # Only run tests marked with @pytest.mark.performance
        "--tb=short"
    ]
    
    return pytest.main(pytest_args)


def main():
    """Main test runner entry point"""
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "all":
            return run_all_tests()
        elif command == "integration":
            return run_integration_tests()
        elif command == "performance":
            return run_performance_tests()
        elif command.startswith("component:"):
            component_name = command.split(":", 1)[1]
            return run_specific_component_tests(component_name)
        else:
            print(f"Unknown command: {command}")
            print("Available commands:")
            print("  all - Run all tests")
            print("  integration - Run integration tests")
            print("  performance - Run performance tests")
            print("  component:<name> - Run tests for specific component")
            return 1
    else:
        return run_all_tests()


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
