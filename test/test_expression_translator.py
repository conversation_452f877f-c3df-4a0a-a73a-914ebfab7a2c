#!/usr/bin/env python3
import re
import sys
from pathlib import Path
import polars as pl
# Add project root to sys.path to import agents
PROJECT_ROOT = Path(__file__).resolve().parents[1]
if str(PROJECT_ROOT) not in sys.path:
    sys.path.insert(0, str(PROJECT_ROOT))
from agents.signal_generation.signal_agent import SignalAgent

# We will exercise the private translation indirectly by calling generate_signals

def make_df():
    # minimal DataFrame with columns referenced in expressions
    return pl.DataFrame({
        'close': [1,2,3,4,5,6,7,8,9,10],
        'open':  [1,2,2,3,4,5,7,7,8,9],
        'volume':[10,11,12,13,14,15,16,17,18,19],
        'adx_14':[10,20,30,40,15,25,35,45,20,10],
        'plusdi_14':[10,15,20,25,10,20,30,40,20,10],
        'minusdi_14':[5,10,15,10,15,10,5,10,15,20],
    })


def test_parentheses_and_precedence():
    agent = SignalAgent()
    strategy = {
        'name': 'TEST_PRECEDENCE',
        'entry_long': 'close > open & close > close.shift(1)',
        'entry_short': 'close < open & close < close.shift(1)',
        'exit_long': 'close < close.shift(1)',
        'exit_short': 'close > close.shift(1)',
    }
    df = make_df()
    signals = agent.generate_signals(df, strategy, ['entry_long','entry_short','exit_long','exit_short'])
    # Ensure Series are boolean and no exception occurred, and both entries have some True values
    assert signals['entry_long'].dtype == pl.Boolean
    assert signals['entry_short'].dtype == pl.Boolean


def test_rolling_translation_mean():
    agent = SignalAgent()
    strategy = {
        'name': 'TEST_ROLLING',
        'entry_long': 'volume > volume.rolling(3).mean() & close > open',
        'entry_short': 'volume > volume.rolling(3).mean() & close < open',
        'exit_long': 'close < close.shift(1)',
        'exit_short': 'close > close.shift(1)',
    }
    df = make_df()
    signals = agent.generate_signals(df, strategy, ['entry_long','entry_short'])
    assert signals['entry_long'].dtype == pl.Boolean
    assert signals['entry_short'].dtype == pl.Boolean

if __name__ == '__main__':
    test_parentheses_and_precedence()
    test_rolling_translation_mean()
    print('Expression translator tests passed.')

