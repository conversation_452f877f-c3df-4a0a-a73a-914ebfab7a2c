cktesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 15min <PERSON><PERSON><PERSON><PERSON>_Scalping_Base: entry_long=3599, entry_short=3785, exit_long=50858, exit_short=50757
12:55:45.280 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 15min <PERSON><PERSON><PERSON>ci_Scalping_Base: final_entries=7384, final_exits=57366, conflicts_removed=7384
12:55:45.280 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'RSI_Intraday_Base' signal 'entry_long': (pl.col("rsi_14") < 30) & (pl.col("volume") > pl.col("volume").rolling_mean(window_size=20))
12:55:45.281 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'RSI_Intraday_Base' signal 'entry_short': (pl.col("rsi_14") > 70) & (pl.col("volume") > pl.col("volume").rolling_mean(window_size=20))
12:55:45.282 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'RSI_Intraday_Base' signal 'exit_long': pl.col("rsi_14") > 50
12:55:45.282 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'RSI_Intraday_Base' signal 'exit_short': pl.col("rsi_14") < 50
12:55:45.285 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 15min RSI_Intraday_Base: entry_long=1579, entry_short=2057, exit_long=32283, exit_short=32467
12:55:45.290 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 15min RSI_Intraday_Base: final_entries=3636, final_exits=61114, conflicts_removed=3636
12:55:45.291 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'EMA_Crossover_Intraday_Base' signal 'entry_long': (pl.col("ema_5") > pl.col("ema_20")) & (pl.col("volume") > pl.col("volume").rolling_mean(window_size=20))
12:55:45.292 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'EMA_Crossover_Intraday_Base' signal 'entry_short': (pl.col("ema_5") < pl.col("ema_20")) & (pl.col("volume") > pl.col("volume").rolling_mean(window_size=20))
12:55:45.293 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'EMA_Crossover_Intraday_Base' signal 'exit_long': pl.col("ema_5") < pl.col("ema_20")
12:55:45.293 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'EMA_Crossover_Intraday_Base' signal 'exit_short': pl.col("ema_5") > pl.col("ema_20")
12:55:45.295 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 15min EMA_Crossover_Intraday_Base: entry_long=9691, entry_short=9676, exit_long=32764, exit_short=31982
12:55:45.300 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 15min EMA_Crossover_Intraday_Base: final_entries=19367, final_exits=45379, conflicts_removed=19367
12:55:45.300 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'VWAP_Intraday_Base' signal 'entry_long': (pl.col("close") > pl.col("vwap")) & (pl.col("volume") > pl.col("volume").rolling_mean(window_size=20))
12:55:45.301 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'VWAP_Intraday_Base' signal 'entry_short': (pl.col("close") < pl.col("vwap")) & (pl.col("volume") > pl.col("volume").rolling_mean(window_size=20))
12:55:45.302 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'VWAP_Intraday_Base' signal 'exit_long': pl.col("close") < pl.col("vwap")
12:55:45.302 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'VWAP_Intraday_Base' signal 'exit_short': pl.col("close") > pl.col("vwap")
12:55:45.305 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 15min VWAP_Intraday_Base: entry_long=17926, entry_short=1441, exit_long=4745, exit_short=60005
12:55:45.310 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 15min VWAP_Intraday_Base: final_entries=19367, final_exits=45383, conflicts_removed=19367
12:55:45.310 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'MACD_Crossover_Intraday_Base' signal 'entry_long': (pl.col("macd_line") > pl.col("signal_line")) & (pl.col("macd_hist") > 0) & (pl.col("volume") > pl.col("volume").rolling_mean(window_size=20))
12:55:45.312 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'MACD_Crossover_Intraday_Base' signal 'entry_short': (pl.col("macd_line") < pl.col("signal_line")) & (pl.col("macd_hist") < 0) & (pl.col("volume") > pl.col("volume").rolling_mean(window_size=20))
12:55:45.313 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'MACD_Crossover_Intraday_Base' signal 'exit_long': pl.col("macd_line") < pl.col("signal_line")
12:55:45.313 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'MACD_Crossover_Intraday_Base' signal 'exit_short': pl.col("macd_line") > pl.col("signal_line")
12:55:45.316 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 15min MACD_Crossover_Intraday_Base: entry_long=10070, entry_short=9295, exit_long=33018, exit_short=31707
12:55:45.319 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 15min MACD_Crossover_Intraday_Base: final_entries=19365, final_exits=45360, conflicts_removed=19365
12:55:45.319 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Bollinger_Bands_Intraday_Base' signal 'entry_long': (pl.col("close") < pl.col("bollinger_lower")) & (pl.col("close").shift(1) >= pl.col("bollinger_lower").shift(1)) & (pl.col("volume") > pl.col("volume").rolling_mean(window_size=20))
12:55:45.320 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Bollinger_Bands_Intraday_Base' signal 'entry_short': (pl.col("close") > pl.col("bollinger_upper")) & (pl.col("close").shift(1) <= pl.col("bollinger_upper").shift(1)) & (pl.col("volume") > pl.col("volume").rolling_mean(window_size=20))
12:55:45.321 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Bollinger_Bands_Intraday_Base' signal 'exit_long': pl.col("close") > pl.col("bollinger_middle")
12:55:45.321 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Bollinger_Bands_Intraday_Base' signal 'exit_short': pl.col("close") < pl.col("bollinger_middle")
12:55:45.324 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 15min Bollinger_Bands_Intraday_Base: entry_long=1176, entry_short=1278, exit_long=31932, exit_short=32807
12:55:45.328 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 15min Bollinger_Bands_Intraday_Base: final_entries=2454, final_exits=62285, conflicts_removed=2454
12:55:45.328 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Stochastic_Intraday_Base' signal 'entry_long': (pl.col("stochastic_k") < 20) & (pl.col("stochastic_d") < 20) & (pl.col("stochastic_k") > pl.col("stochastic_d")) & (pl.col("volume") > pl.col("volume").rolling_mean(window_size=20))
12:55:45.329 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Stochastic_Intraday_Base' signal 'entry_short': (pl.col("stochastic_k") > 80) & (pl.col("stochastic_d") > 80) & (pl.col("stochastic_k") < pl.col("stochastic_d")) & (pl.col("volume") > pl.col("volume").rolling_mean(window_size=20))
12:55:45.330 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Stochastic_Intraday_Base' signal 'exit_long': pl.col("stochastic_k") > 50
12:55:45.330 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Stochastic_Intraday_Base' signal 'exit_short': pl.col("stochastic_k") < 50
12:55:45.333 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 15min Stochastic_Intraday_Base: entry_long=1155, entry_short=1244, exit_long=31387, exit_short=33348
12:55:45.336 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 15min Stochastic_Intraday_Base: final_entries=2399, final_exits=62336, conflicts_removed=2399
12:55:45.336 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Parabolic_SAR_Intraday_Base' signal 'entry_long': (pl.col("sar_indicator") < pl.col("close")) & (pl.col("sar_indicator").shift(1) >= pl.col("close").shift(1)) & (pl.col("volume") > pl.col("volume").rolling_mean(window_size=20))
12:55:45.337 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Parabolic_SAR_Intraday_Base' signal 'entry_short': (pl.col("sar_indicator") > pl.col("close")) & (pl.col("sar_indicator").shift(1) <= pl.col("close").shift(1)) & (pl.col("volume") > pl.col("volume").rolling_mean(window_size=20))
12:55:45.338 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Parabolic_SAR_Intraday_Base' signal 'exit_long': pl.col("sar_indicator") > pl.col("close")
12:55:45.338 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Parabolic_SAR_Intraday_Base' signal 'exit_short': pl.col("sar_indicator") < pl.col("close")
12:55:45.341 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 15min Parabolic_SAR_Intraday_Base: entry_long=1293, entry_short=1089, exit_long=32791, exit_short=31953
12:55:45.345 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 15min Parabolic_SAR_Intraday_Base: final_entries=2382, final_exits=62362, conflicts_removed=2382
12:55:45.345 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'ADX_Intraday_Base' signal 'entry_long': (pl.col("adx_14") > 20) & (pl.col("plusdi_14") > pl.col("minusdi_14")) & (pl.col("volume") > pl.col("volume").rolling_mean(window_size=20))
12:55:45.346 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'ADX_Intraday_Base' signal 'entry_short': (pl.col("adx_14") > 20) & (pl.col("plusdi_14") < pl.col("minusdi_14")) & (pl.col("volume") > pl.col("volume").rolling_mean(window_size=20))
12:55:45.347 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'ADX_Intraday_Base' signal 'exit_long': pl.col("plusdi_14") < pl.col("minusdi_14")
12:55:45.348 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'ADX_Intraday_Base' signal 'exit_short': pl.col("plusdi_14") > pl.col("minusdi_14")
12:55:45.350 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 15min ADX_Intraday_Base: entry_long=7082, entry_short=6729, exit_long=32277, exit_short=32459
12:55:45.355 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 15min ADX_Intraday_Base: final_entries=13811, final_exits=50925, conflicts_removed=13811
12:55:45.355 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Fibonacci_Intraday_Base' signal 'entry_long': (pl.col("close") > pl.col("fibonacci_38_2")) & (pl.col("close").shift(1) <= pl.col("fibonacci_38_2").shift(1)) & (pl.col("volume") > pl.col("volume").rolling_mean(window_size=20))
12:55:45.356 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Fibonacci_Intraday_Base' signal 'entry_short': (pl.col("close") < pl.col("fibonacci_61_8")) & (pl.col("close").shift(1) >= pl.col("fibonacci_61_8").shift(1)) & (pl.col("volume") > pl.col("volume").rolling_mean(window_size=20))
12:55:45.357 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Fibonacci_Intraday_Base' signal 'exit_long': pl.col("close") < pl.col("fibonacci_23_6")
12:55:45.357 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Fibonacci_Intraday_Base' signal 'exit_short': pl.col("close") > pl.col("fibonacci_78_6")
12:55:45.359 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 15min Fibonacci_Intraday_Base: entry_long=1561, entry_short=1505, exit_long=50858, exit_short=50757
12:55:45.364 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 15min Fibonacci_Intraday_Base: final_entries=3066, final_exits=61684, conflicts_removed=3066
12:55:45.364 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Momentum_Intraday_Base' signal 'entry_long': (pl.col("close") > pl.col("open")) & (pl.col("close") > pl.col("close").shift(1)) & (pl.col("volume") > pl.col("volume").rolling_mean(window_size=10) * 1.2)
12:55:45.365 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Momentum_Intraday_Base' signal 'entry_short': (pl.col("close") < pl.col("open")) & (pl.col("close") < pl.col("close").shift(1)) & (pl.col("volume") > pl.col("volume").rolling_mean(window_size=10) * 1.2)
12:55:45.366 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Momentum_Intraday_Base' signal 'exit_long': pl.col("close") < pl.col("close").shift(1)
12:55:45.366 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Momentum_Intraday_Base' signal 'exit_short': pl.col("close") > pl.col("close").shift(1)
12:55:45.369 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 15min Momentum_Intraday_Base: entry_long=8362, entry_short=8422, exit_long=31402, exit_short=30411
12:55:45.377 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 15min Momentum_Intraday_Base: final_entries=16784, final_exits=45029, conflicts_removed=16784
12:55:45.377 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Price_Action_Intraday_Base' signal 'entry_long': (pl.col("close") > pl.col("open")) & (pl.col("high") - pl.col("close") < pl.col("close") - pl.col("low"))
12:55:45.378 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Price_Action_Intraday_Base' signal 'entry_short': (pl.col("close") < pl.col("open")) & (pl.col("close") - pl.col("low") < pl.col("high") - pl.col("close"))
12:55:45.378 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Price_Action_Intraday_Base' signal 'exit_long': pl.col("close") < pl.col("open")
12:55:45.378 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Price_Action_Intraday_Base' signal 'exit_short': pl.col("close") > pl.col("open")
12:55:45.381 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 15min Price_Action_Intraday_Base: entry_long=23936, entry_short=24262, exit_long=31818, exit_short=30034
12:55:45.384 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 15min Price_Action_Intraday_Base: final_entries=48198, final_exits=13654, conflicts_removed=48198
12:55:45.384 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Gap_Trading_Intraday_Base' signal 'entry_long': (pl.col("open") < pl.col("close").shift(1) * 0.99) & (pl.col("close") > pl.col("open"))
12:55:45.384 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Gap_Trading_Intraday_Base' signal 'entry_short': (pl.col("open") > pl.col("close").shift(1) * 1.01) & (pl.col("close") < pl.col("open"))
12:55:45.385 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Gap_Trading_Intraday_Base' signal 'exit_long': pl.col("close") > pl.col("open").shift(1)
12:55:45.385 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Gap_Trading_Intraday_Base' signal 'exit_short': pl.col("close") < pl.col("open").shift(1)
12:55:45.387 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 15min Gap_Trading_Intraday_Base: entry_long=145, entry_short=312, exit_long=30570, exit_short=32214
12:55:45.394 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 15min Gap_Trading_Intraday_Base: final_entries=457, final_exits=62331, conflicts_removed=453
12:55:45.394 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Reversal_Intraday_Base' signal 'entry_long': (pl.col("close") < pl.col("low").shift(1)) & (pl.col("close") > pl.col("open"))
12:55:45.394 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Reversal_Intraday_Base' signal 'entry_short': (pl.col("close") > pl.col("high").shift(1)) & (pl.col("close") < pl.col("open"))
12:55:45.395 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Reversal_Intraday_Base' signal 'exit_long': pl.col("close") > pl.col("high").shift(1)
12:55:45.395 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Reversal_Intraday_Base' signal 'exit_short': pl.col("close") < pl.col("low").shift(1)
12:55:45.398 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 15min Reversal_Intraday_Base: entry_long=139, entry_short=294, exit_long=12604, exit_short=13004
12:55:45.405 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 15min Reversal_Intraday_Base: final_entries=433, final_exits=25175, conflicts_removed=433
12:55:45.405 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Trend_Following_Intraday_Base' signal 'entry_long': (pl.col("close") > pl.col("ema_50")) & (pl.col("close").shift(1) < pl.col("ema_50").shift(1))
12:55:45.405 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Trend_Following_Intraday_Base' signal 'entry_short': (pl.col("close") < pl.col("ema_50")) & (pl.col("close").shift(1) > pl.col("ema_50").shift(1))
12:55:45.405 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Trend_Following_Intraday_Base' signal 'exit_long': pl.col("close") < pl.col("ema_50")
12:55:45.406 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Trend_Following_Intraday_Base' signal 'exit_short': pl.col("close") > pl.col("ema_50")
12:55:45.408 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 15min Trend_Following_Intraday_Base: entry_long=2705, entry_short=2705, exit_long=32123, exit_short=32624
12:55:45.412 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 15min Trend_Following_Intraday_Base: final_entries=5410, final_exits=59337, conflicts_removed=5410
12:55:45.412 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] Momentum_Scalping_Base: entry_signals=16784, exit_signals=45029, data_length=64750
12:55:45.445 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] Momentum_Scalping_Base RR 1:2: vectorbt generated 9039 trades
12:55:45.445 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] EMA_9_Scalping_Base: entry_signals=64749, exit_signals=0, data_length=64750
12:55:45.477 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] EMA_9_Scalping_Base RR 1:2: vectorbt generated 2898 trades
12:55:45.477 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] Three_MA_Crossover_Scalping_Base: entry_signals=53487, exit_signals=11258, data_length=64750
12:55:45.508 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] Three_MA_Crossover_Scalping_Base RR 1:2: vectorbt generated 5564 trades
12:55:45.509 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] Range_Trading_Scalping_Base: entry_signals=3796, exit_signals=60943, data_length=64750
12:55:45.541 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] Range_Trading_Scalping_Base RR 1:2: vectorbt generated 3740 trades
12:55:45.541 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] Breakout_Scalping_Base: entry_signals=25608, exit_signals=36205, data_length=64750
12:55:45.576 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] Breakout_Scalping_Base RR 1:2: vectorbt generated 15652 trades
12:55:45.576 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] Volume_Scalping_Base: entry_signals=11940, exit_signals=49912, data_length=64750
12:55:45.609 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] Volume_Scalping_Base RR 1:2: vectorbt generated 6492 trades
12:55:45.609 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] MACD_Crossover_Scalping_Base: entry_signals=64717, exit_signals=8, data_length=64750
12:55:45.640 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] MACD_Crossover_Scalping_Base RR 1:2: vectorbt generated 2897 trades
12:55:45.640 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] Bollinger_Bands_Scalping_Base: entry_signals=3796, exit_signals=60943, data_length=64750
12:55:45.671 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] Bollinger_Bands_Scalping_Base RR 1:2: vectorbt generated 3740 trades
12:55:45.671 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] VWAP_Scalping_Base: entry_signals=195, exit_signals=64555, data_length=64750
12:55:45.702 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] VWAP_Scalping_Base RR 1:2: vectorbt generated 142 trades
12:55:45.702 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] Parabolic_SAR_Scalping_Base: entry_signals=4725, exit_signals=60019, data_length=64750
12:55:45.733 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] Parabolic_SAR_Scalping_Base RR 1:2: vectorbt generated 4376 trades
12:55:45.733 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] Stochastic_Scalping_Base: entry_signals=7633, exit_signals=57102, data_length=64750
12:55:45.764 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] Stochastic_Scalping_Base RR 1:2: vectorbt generated 3609 trades
12:55:45.764 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] STC_Scalping_Base: entry_signals=4975, exit_signals=48318, data_length=64750
12:55:45.794 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] STC_Scalping_Base RR 1:2: vectorbt generated 4975 trades
12:55:45.795 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] ADX_Scalping_Base: entry_signals=46567, exit_signals=18169, data_length=64750
12:55:45.826 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] ADX_Scalping_Base RR 1:2: vectorbt generated 3113 trades
12:55:45.826 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] Fibonacci_Scalping_Base: entry_signals=7384, exit_signals=57366, data_length=64750
12:55:45.857 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] Fibonacci_Scalping_Base RR 1:2: vectorbt generated 6889 trades
12:55:45.858 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] RSI_Intraday_Base: entry_signals=3636, exit_signals=61114, data_length=64750
12:55:45.888 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] RSI_Intraday_Base RR 1:2: vectorbt generated 2027 trades
12:55:45.888 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] EMA_Crossover_Intraday_Base: entry_signals=19367, exit_signals=45379, data_length=64750
12:55:45.920 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] EMA_Crossover_Intraday_Base RR 1:2: vectorbt generated 8711 trades
12:55:45.920 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] VWAP_Intraday_Base: entry_signals=19367, exit_signals=45383, data_length=64750
12:55:45.952 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] VWAP_Intraday_Base RR 1:2: vectorbt generated 8711 trades
12:55:45.952 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] MACD_Crossover_Intraday_Base: entry_signals=19365, exit_signals=45360, data_length=64750
12:55:45.983 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] MACD_Crossover_Intraday_Base RR 1:2: vectorbt generated 8709 trades
12:55:45.983 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] Bollinger_Bands_Intraday_Base: entry_signals=2454, exit_signals=62285, data_length=64750
12:55:46.013 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] Bollinger_Bands_Intraday_Base RR 1:2: vectorbt generated 2428 trades
12:55:46.013 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] Stochastic_Intraday_Base: entry_signals=2399, exit_signals=62336, data_length=64750
12:55:46.042 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] Stochastic_Intraday_Base RR 1:2: vectorbt generated 1740 trades
12:55:46.042 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] Parabolic_SAR_Intraday_Base: entry_signals=2382, exit_signals=62362, data_length=64750
12:55:46.071 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] Parabolic_SAR_Intraday_Base RR 1:2: vectorbt generated 2203 trades
12:55:46.071 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] ADX_Intraday_Base: entry_signals=13811, exit_signals=50925, data_length=64750
12:55:46.102 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] ADX_Intraday_Base RR 1:2: vectorbt generated 6532 trades
12:55:46.102 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] Fibonacci_Intraday_Base: entry_signals=3066, exit_signals=61684, data_length=64750
12:55:46.131 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] Fibonacci_Intraday_Base RR 1:2: vectorbt generated 2866 trades
12:55:46.131 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] Momentum_Intraday_Base: entry_signals=16784, exit_signals=45029, data_length=64750
12:55:46.163 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] Momentum_Intraday_Base RR 1:2: vectorbt generated 9039 trades
12:55:46.163 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] Price_Action_Intraday_Base: entry_signals=48198, exit_signals=13654, data_length=64750
12:55:46.196 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] Price_Action_Intraday_Base RR 1:2: vectorbt generated 12059 trades
12:55:46.196 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] Gap_Trading_Intraday_Base: entry_signals=457, exit_signals=62331, data_length=64750
12:55:46.224 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] Gap_Trading_Intraday_Base RR 1:2: vectorbt generated 456 trades
12:55:46.225 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] Reversal_Intraday_Base: entry_signals=433, exit_signals=25175, data_length=64750
12:55:46.253 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] Reversal_Intraday_Base RR 1:2: vectorbt generated 433 trades
12:55:46.253 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] Trend_Following_Intraday_Base: entry_signals=5410, exit_signals=59337, data_length=64750
12:55:46.283 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] Trend_Following_Intraday_Base RR 1:2: vectorbt generated 3808 trades
12:55:46.283 - utils.real_gpu_accelerator - INFO - 🚀 GPU vectorized backtest completed in 0.871s
12:55:46.283 - agents.backtesting.enhanced_backtesting_kimi - INFO - Processing SUPREMEIND (1min) on GPU...
12:55:48.589 - agents.backtesting.kimi.file_io - INFO - Found 'timestamp' column, converting to 'datetime'.
12:55:48.840 - agents.backtesting.kimi.file_io - INFO - Successfully converted 'timestamp' to 'datetime'.
12:55:48.907 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Momentum_Scalping_Base' signal 'entry_long': (pl.col("close") > pl.col("open")) & (pl.col("close") > pl.col("close").shift(1)) & (pl.col("volume") > pl.col("volume").rolling_mean(window_size=10) * 1.2)
12:55:48.918 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Momentum_Scalping_Base' signal 'entry_short': (pl.col("close") < pl.col("open")) & (pl.col("close") < pl.col("close").shift(1)) & (pl.col("volume") > pl.col("volume").rolling_mean(window_size=10) * 1.2)
12:55:48.928 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Momentum_Scalping_Base' signal 'exit_long': pl.col("close") < pl.col("close").shift(1)
12:55:48.928 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Momentum_Scalping_Base' signal 'exit_short': pl.col("close") > pl.col("close").shift(1)
12:55:48.962 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 1min Momentum_Scalping_Base: entry_long=95392, entry_short=103473, exit_long=349820, exit_short=350630
12:55:49.084 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 1min Momentum_Scalping_Base: final_entries=198865, final_exits=501585, conflicts_removed=198865
12:55:49.084 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'EMA_9_Scalping_Base' signal 'entry_long': pl.col("close") > pl.col("ema_9")
12:55:49.085 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'EMA_9_Scalping_Base' signal 'entry_short': pl.col("close") < pl.col("ema_9")
12:55:49.085 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'EMA_9_Scalping_Base' signal 'exit_long': pl.col("close") < pl.col("ema_9")
12:55:49.086 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'EMA_9_Scalping_Base' signal 'exit_short': pl.col("close") > pl.col("ema_9")
12:55:49.118 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 1min EMA_9_Scalping_Base: entry_long=486682, entry_short=478098, exit_long=478098, exit_short=486682
12:55:49.156 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 1min EMA_9_Scalping_Base: final_entries=964780, final_exits=0, conflicts_removed=964780
12:55:49.156 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Three_MA_Crossover_Scalping_Base' signal 'entry_long': (pl.col("ema_5") > pl.col("ema_10")) & (pl.col("ema_10") > pl.col("ema_20"))
12:55:49.157 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Three_MA_Crossover_Scalping_Base' signal 'entry_short': (pl.col("ema_5") < pl.col("ema_10")) & (pl.col("ema_10") < pl.col("ema_20"))
12:55:49.157 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Three_MA_Crossover_Scalping_Base' signal 'exit_long': pl.col("ema_5") < pl.col("ema_10")
12:55:49.158 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Three_MA_Crossover_Scalping_Base' signal 'exit_short': pl.col("ema_5") > pl.col("ema_10")
12:55:49.190 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 1min Three_MA_Crossover_Scalping_Base: entry_long=387583, entry_short=395468, exit_long=484200, exit_short=480561
12:55:49.228 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 1min Three_MA_Crossover_Scalping_Base: final_entries=783051, final_exits=181710, conflicts_removed=783051
12:55:49.228 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Range_Trading_Scalping_Base' signal 'entry_long': (pl.col("close") < pl.col("bollinger_lower")) & (pl.col("close").shift(1) >= pl.col("bollinger_lower").shift(1))
12:55:49.229 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Range_Trading_Scalping_Base' signal 'entry_short': (pl.col("close") > pl.col("bollinger_upper")) & (pl.col("close").shift(1) <= pl.col("bollinger_upper").shift(1))
12:55:49.230 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Range_Trading_Scalping_Base' signal 'exit_long': pl.col("close") > pl.col("bollinger_middle")
12:55:49.230 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Range_Trading_Scalping_Base' signal 'exit_short': pl.col("close") < pl.col("bollinger_middle")
12:55:49.265 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 1min Range_Trading_Scalping_Base: entry_long=27285, entry_short=24158, exit_long=481512, exit_short=480749
12:55:49.325 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 1min Range_Trading_Scalping_Base: final_entries=51443, final_exits=910818, conflicts_removed=51443
12:55:49.325 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Breakout_Scalping_Base' signal 'entry_long': pl.col("close") > pl.col("high").shift(1)
12:55:49.326 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Breakout_Scalping_Base' signal 'entry_short': pl.col("close") < pl.col("low").shift(1)
12:55:49.326 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Breakout_Scalping_Base' signal 'exit_long': pl.col("close") < pl.col("close").shift(1)
12:55:49.326 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Breakout_Scalping_Base' signal 'exit_short': pl.col("close") > pl.col("close").shift(1)
12:55:49.361 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 1min Breakout_Scalping_Base: entry_long=191689, entry_short=194144, exit_long=349820, exit_short=350630
12:55:49.489 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 1min Breakout_Scalping_Base: final_entries=385833, final_exits=314617, conflicts_removed=385833
12:55:49.489 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Volume_Scalping_Base' signal 'entry_long': (pl.col("volume") > pl.col("volume").rolling_mean(window_size=20) * 1.5) & (pl.col("close") > pl.col("open"))
12:55:49.499 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Volume_Scalping_Base' signal 'entry_short': (pl.col("volume") > pl.col("volume").rolling_mean(window_size=20) * 1.5) & (pl.col("close") < pl.col("open"))
12:55:49.509 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Volume_Scalping_Base' signal 'exit_long': pl.col("close") < pl.col("open")
12:55:49.510 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Volume_Scalping_Base' signal 'exit_short': pl.col("close") > pl.col("open")
12:55:49.544 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 1min Volume_Scalping_Base: entry_long=74491, entry_short=80212, exit_long=340451, exit_short=339854
12:55:49.623 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 1min Volume_Scalping_Base: final_entries=154703, final_exits=525602, conflicts_removed=154703
12:55:49.623 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'MACD_Crossover_Scalping_Base' signal 'entry_long': (pl.col("macd_line") > pl.col("signal_line")) & (pl.col("macd_hist") > 0)
12:55:49.624 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'MACD_Crossover_Scalping_Base' signal 'entry_short': (pl.col("macd_line") < pl.col("signal_line")) & (pl.col("macd_hist") < 0)
12:55:49.625 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'MACD_Crossover_Scalping_Base' signal 'exit_long': pl.col("macd_line") < pl.col("signal_line")
12:55:49.625 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'MACD_Crossover_Scalping_Base' signal 'exit_short': pl.col("macd_line") > pl.col("signal_line")
12:55:49.658 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 1min MACD_Crossover_Scalping_Base: entry_long=482421, entry_short=482686, exit_long=482694, exit_short=482421
12:55:49.696 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 1min MACD_Crossover_Scalping_Base: final_entries=965107, final_exits=8, conflicts_removed=965107
12:55:49.696 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Bollinger_Bands_Scalping_Base' signal 'entry_long': (pl.col("close") < pl.col("bollinger_lower")) & (pl.col("close").shift(1) >= pl.col("bollinger_lower").shift(1))
12:55:49.697 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Bollinger_Bands_Scalping_Base' signal 'entry_short': (pl.col("close") > pl.col("bollinger_upper")) & (pl.col("close").shift(1) <= pl.col("bollinger_upper").shift(1))
12:55:49.697 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Bollinger_Bands_Scalping_Base' signal 'exit_long': pl.col("close") > pl.col("bollinger_middle")
12:55:49.698 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Bollinger_Bands_Scalping_Base' signal 'exit_short': pl.col("close") < pl.col("bollinger_middle")
12:55:49.731 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 1min Bollinger_Bands_Scalping_Base: entry_long=27285, entry_short=24158, exit_long=481512, exit_short=480749
12:55:49.788 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 1min Bollinger_Bands_Scalping_Base: final_entries=51443, final_exits=910818, conflicts_removed=51443
12:55:49.788 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'VWAP_Scalping_Base' signal 'entry_long': (pl.col("close") > pl.col("vwap")) & (pl.col("close").shift(1) <= pl.col("vwap").shift(1))
12:55:49.789 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'VWAP_Scalping_Base' signal 'entry_short': (pl.col("close") < pl.col("vwap")) & (pl.col("close").shift(1) >= pl.col("vwap").shift(1))
12:55:49.790 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'VWAP_Scalping_Base' signal 'exit_long': pl.col("close") < pl.col("vwap")
12:55:49.790 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'VWAP_Scalping_Base' signal 'exit_short': pl.col("close") > pl.col("vwap")
12:55:49.823 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 1min VWAP_Scalping_Base: entry_long=427, entry_short=426, exit_long=71283, exit_short=893857
12:55:49.880 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 1min VWAP_Scalping_Base: final_entries=853, final_exits=964287, conflicts_removed=853
12:55:49.880 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Parabolic_SAR_Scalping_Base' signal 'entry_long': (pl.col("sar_indicator") < pl.col("close")) & (pl.col("sar_indicator").shift(1) >= pl.col("close").shift(1))
12:55:49.881 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Parabolic_SAR_Scalping_Base' signal 'entry_short': (pl.col("sar_indicator") > pl.col("close")) & (pl.col("sar_indicator").shift(1) <= pl.col("close").shift(1))
12:55:49.881 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Parabolic_SAR_Scalping_Base' signal 'exit_long': pl.col("sar_indicator") > pl.col("close")
12:55:49.882 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Parabolic_SAR_Scalping_Base' signal 'exit_short': pl.col("sar_indicator") < pl.col("close")
12:55:49.915 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 1min Parabolic_SAR_Scalping_Base: entry_long=35720, entry_short=35592, exit_long=480693, exit_short=483503
12:55:49.953 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 1min Parabolic_SAR_Scalping_Base: final_entries=71312, final_exits=892884, conflicts_removed=71312
12:55:49.953 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Stochastic_Scalping_Base' signal 'entry_long': (pl.col("stochastic_k") < 20) & (pl.col("stochastic_d") < 20) & (pl.col("stochastic_k") > pl.col("stochastic_d"))
12:55:49.954 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Stochastic_Scalping_Base' signal 'entry_short': (pl.col("stochastic_k") > 80) & (pl.col("stochastic_d") > 80) & (pl.col("stochastic_k") < pl.col("stochastic_d"))
12:55:49.954 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Stochastic_Scalping_Base' signal 'exit_long': pl.col("stochastic_k") > 50
12:55:49.955 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Stochastic_Scalping_Base' signal 'exit_short': pl.col("stochastic_k") < 50
12:55:49.987 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 1min Stochastic_Scalping_Base: entry_long=59794, entry_short=66389, exit_long=497230, exit_short=465712
12:55:50.025 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 1min Stochastic_Scalping_Base: final_entries=126183, final_exits=836759, conflicts_removed=126183
12:55:50.025 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'STC_Scalping_Base' signal 'entry_long': (pl.col("stc_line") < 25) & (pl.col("stc_line").shift(1) >= 25)
12:55:50.026 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'STC_Scalping_Base' signal 'entry_short': (pl.col("stc_line") > 75) & (pl.col("stc_line").shift(1) <= 75)
12:55:50.027 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'STC_Scalping_Base' signal 'exit_long': pl.col("stc_line") > 50
12:55:50.027 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'STC_Scalping_Base' signal 'exit_short': pl.col("stc_line") < 50
12:55:50.060 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 1min STC_Scalping_Base: entry_long=38315, entry_short=38423, exit_long=401886, exit_short=400125
12:55:50.118 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 1min STC_Scalping_Base: final_entries=76738, final_exits=725273, conflicts_removed=76738
12:55:50.118 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'ADX_Scalping_Base' signal 'entry_long': (pl.col("adx_14") > 20) & (pl.col("plusdi_14") > pl.col("minusdi_14"))
12:55:50.119 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'ADX_Scalping_Base' signal 'entry_short': (pl.col("adx_14") > 20) & (pl.col("plusdi_14") < pl.col("minusdi_14"))
12:55:50.119 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'ADX_Scalping_Base' signal 'exit_long': pl.col("plusdi_14") < pl.col("minusdi_14")
12:55:50.120 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'ADX_Scalping_Base' signal 'exit_short': pl.col("plusdi_14") > pl.col("minusdi_14")
12:55:50.153 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 1min ADX_Scalping_Base: entry_long=322570, entry_short=392744, exit_long=521556, exit_short=443570
12:55:50.191 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 1min ADX_Scalping_Base: final_entries=715314, final_exits=249812, conflicts_removed=715314
12:55:50.191 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Fibonacci_Scalping_Base' signal 'entry_long': (pl.col("close") > pl.col("fibonacci_38_2")) & (pl.col("close").shift(1) <= pl.col("fibonacci_38_2").shift(1))
12:55:50.192 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Fibonacci_Scalping_Base' signal 'entry_short': (pl.col("close") < pl.col("fibonacci_61_8")) & (pl.col("close").shift(1) >= pl.col("fibonacci_61_8").shift(1))
12:55:50.192 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Fibonacci_Scalping_Base' signal 'exit_long': pl.col("close") < pl.col("fibonacci_23_6")
12:55:50.193 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Fibonacci_Scalping_Base' signal 'exit_short': pl.col("close") > pl.col("fibonacci_78_6")
12:55:50.226 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 1min Fibonacci_Scalping_Base: entry_long=65154, entry_short=64039, exit_long=697837, exit_short=739911
12:55:50.284 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 1min Fibonacci_Scalping_Base: final_entries=129193, final_exits=834996, conflicts_removed=129193
12:55:50.285 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'RSI_Intraday_Base' signal 'entry_long': (pl.col("rsi_14") < 30) & (pl.col("volume") > pl.col("volume").rolling_mean(window_size=20))
12:55:50.299 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'RSI_Intraday_Base' signal 'entry_short': (pl.col("rsi_14") > 70) & (pl.col("volume") > pl.col("volume").rolling_mean(window_size=20))
12:55:50.314 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'RSI_Intraday_Base' signal 'exit_long': pl.col("rsi_14") > 50
12:55:50.314 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'RSI_Intraday_Base' signal 'exit_short': pl.col("rsi_14") < 50
12:55:50.347 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 1min RSI_Intraday_Base: entry_long=19901, entry_short=20346, exit_long=480764, exit_short=484376
12:55:50.406 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 1min RSI_Intraday_Base: final_entries=40247, final_exits=924893, conflicts_removed=40247
12:55:50.406 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'EMA_Crossover_Intraday_Base' signal 'entry_long': (pl.col("ema_5") > pl.col("ema_20")) & (pl.col("volume") > pl.col("volume").rolling_mean(window_size=20))
12:55:50.421 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'EMA_Crossover_Intraday_Base' signal 'entry_short': (pl.col("ema_5") < pl.col("ema_20")) & (pl.col("volume") > pl.col("volume").rolling_mean(window_size=20))
12:55:50.431 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'EMA_Crossover_Intraday_Base' signal 'exit_long': pl.col("ema_5") < pl.col("ema_20")
12:55:50.431 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'EMA_Crossover_Intraday_Base' signal 'exit_short': pl.col("ema_5") > pl.col("ema_20")
12:55:50.464 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 1min EMA_Crossover_Intraday_Base: entry_long=135579, entry_short=137556, exit_long=486873, exit_short=478190
12:55:50.523 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 1min EMA_Crossover_Intraday_Base: final_entries=273135, final_exits=691928, conflicts_removed=273135
12:55:50.523 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'VWAP_Intraday_Base' signal 'entry_long': (pl.col("close") > pl.col("vwap")) & (pl.col("volume") > pl.col("volume").rolling_mean(window_size=20))
12:55:50.538 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'VWAP_Intraday_Base' signal 'entry_short': (pl.col("close") < pl.col("vwap")) & (pl.col("volume") > pl.col("volume").rolling_mean(window_size=20))
12:55:50.552 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'VWAP_Intraday_Base' signal 'exit_long': pl.col("close") < pl.col("vwap")
12:55:50.553 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'VWAP_Intraday_Base' signal 'exit_short': pl.col("close") > pl.col("vwap")
12:55:50.587 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 1min VWAP_Intraday_Base: entry_long=255016, entry_short=18143, exit_long=71283, exit_short=893857
12:55:50.666 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 1min VWAP_Intraday_Base: final_entries=273159, final_exits=691981, conflicts_removed=273159
12:55:50.666 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'MACD_Crossover_Intraday_Base' signal 'entry_long': (pl.col("macd_line") > pl.col("signal_line")) & (pl.col("macd_hist") > 0) & (pl.col("volume") > pl.col("volume").rolling_mean(window_size=20))
12:55:50.679 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'MACD_Crossover_Intraday_Base' signal 'entry_short': (pl.col("macd_line") < pl.col("signal_line")) & (pl.col("macd_hist") < 0) & (pl.col("volume") > pl.col("volume").rolling_mean(window_size=20))
12:55:50.694 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'MACD_Crossover_Intraday_Base' signal 'exit_long': pl.col("macd_line") < pl.col("signal_line")
12:55:50.694 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'MACD_Crossover_Intraday_Base' signal 'exit_short': pl.col("macd_line") > pl.col("signal_line")
12:55:50.728 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 1min MACD_Crossover_Intraday_Base: entry_long=136672, entry_short=136484, exit_long=482694, exit_short=482421
12:55:50.766 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 1min MACD_Crossover_Intraday_Base: final_entries=273156, final_exits=691959, conflicts_removed=273156
12:55:50.766 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Bollinger_Bands_Intraday_Base' signal 'entry_long': (pl.col("close") < pl.col("bollinger_lower")) & (pl.col("close").shift(1) >= pl.col("bollinger_lower").shift(1)) & (pl.col("volume") > pl.col("volume").rolling_mean(window_size=20))
12:55:50.776 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Bollinger_Bands_Intraday_Base' signal 'entry_short': (pl.col("close") > pl.col("bollinger_upper")) & (pl.col("close").shift(1) <= pl.col("bollinger_upper").shift(1)) & (pl.col("volume") > pl.col("volume").rolling_mean(window_size=20))
12:55:50.786 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Bollinger_Bands_Intraday_Base' signal 'exit_long': pl.col("close") > pl.col("bollinger_middle")
12:55:50.786 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Bollinger_Bands_Intraday_Base' signal 'exit_short': pl.col("close") < pl.col("bollinger_middle")
12:55:50.820 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 1min Bollinger_Bands_Intraday_Base: entry_long=17907, entry_short=15148, exit_long=481512, exit_short=480749
12:55:50.878 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 1min Bollinger_Bands_Intraday_Base: final_entries=33055, final_exits=929206, conflicts_removed=33055
12:55:50.878 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Stochastic_Intraday_Base' signal 'entry_long': (pl.col("stochastic_k") < 20) & (pl.col("stochastic_d") < 20) & (pl.col("stochastic_k") > pl.col("stochastic_d")) & (pl.col("volume") > pl.col("volume").rolling_mean(window_size=20))
12:55:50.888 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Stochastic_Intraday_Base' signal 'entry_short': (pl.col("stochastic_k") > 80) & (pl.col("stochastic_d") > 80) & (pl.col("stochastic_k") < pl.col("stochastic_d")) & (pl.col("volume") > pl.col("volume").rolling_mean(window_size=20))
12:55:50.898 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Stochastic_Intraday_Base' signal 'exit_long': pl.col("stochastic_k") > 50
12:55:50.899 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Stochastic_Intraday_Base' signal 'exit_short': pl.col("stochastic_k") < 50
12:55:50.935 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 1min Stochastic_Intraday_Base: entry_long=18211, entry_short=21212, exit_long=497230, exit_short=465712
12:55:50.993 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 1min Stochastic_Intraday_Base: final_entries=39423, final_exits=923519, conflicts_removed=39423
12:55:50.993 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Parabolic_SAR_Intraday_Base' signal 'entry_long': (pl.col("sar_indicator") < pl.col("close")) & (pl.col("sar_indicator").shift(1) >= pl.col("close").shift(1)) & (pl.col("volume") > pl.col("volume").rolling_mean(window_size=20))
12:55:51.003 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Parabolic_SAR_Intraday_Base' signal 'entry_short': (pl.col("sar_indicator") > pl.col("close")) & (pl.col("sar_indicator").shift(1) <= pl.col("close").shift(1)) & (pl.col("volume") > pl.col("volume").rolling_mean(window_size=20))
12:55:51.013 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Parabolic_SAR_Intraday_Base' signal 'exit_long': pl.col("sar_indicator") > pl.col("close")
12:55:51.013 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Parabolic_SAR_Intraday_Base' signal 'exit_short': pl.col("sar_indicator") < pl.col("close")
12:55:51.046 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 1min Parabolic_SAR_Intraday_Base: entry_long=17721, entry_short=19633, exit_long=480693, exit_short=483503
12:55:51.125 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 1min Parabolic_SAR_Intraday_Base: final_entries=37354, final_exits=926842, conflicts_removed=37354
12:55:51.125 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'ADX_Intraday_Base' signal 'entry_long': (pl.col("adx_14") > 20) & (pl.col("plusdi_14") > pl.col("minusdi_14")) & (pl.col("volume") > pl.col("volume").rolling_mean(window_size=20))
12:55:51.135 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'ADX_Intraday_Base' signal 'entry_short': (pl.col("adx_14") > 20) & (pl.col("plusdi_14") < pl.col("minusdi_14")) & (pl.col("volume") > pl.col("volume").rolling_mean(window_size=20))
12:55:51.144 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'ADX_Intraday_Base' signal 'exit_long': pl.col("plusdi_14") < pl.col("minusdi_14")
12:55:51.145 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'ADX_Intraday_Base' signal 'exit_short': pl.col("plusdi_14") > pl.col("minusdi_14")
12:55:51.178 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 1min ADX_Intraday_Base: entry_long=92363, entry_short=110560, exit_long=521556, exit_short=443570
12:55:51.237 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 1min ADX_Intraday_Base: final_entries=202923, final_exits=762203, conflicts_removed=202923
12:55:51.237 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Fibonacci_Intraday_Base' signal 'entry_long': (pl.col("close") > pl.col("fibonacci_38_2")) & (pl.col("close").shift(1) <= pl.col("fibonacci_38_2").shift(1)) & (pl.col("volume") > pl.col("volume").rolling_mean(window_size=20))
12:55:51.252 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Fibonacci_Intraday_Base' signal 'entry_short': (pl.col("close") < pl.col("fibonacci_61_8")) & (pl.col("close").shift(1) >= pl.col("fibonacci_61_8").shift(1)) & (pl.col("volume") > pl.col("volume").rolling_mean(window_size=20))
12:55:51.266 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Fibonacci_Intraday_Base' signal 'exit_long': pl.col("close") < pl.col("fibonacci_23_6")
12:55:51.267 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Fibonacci_Intraday_Base' signal 'exit_short': pl.col("close") > pl.col("fibonacci_78_6")
12:55:51.301 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 1min Fibonacci_Intraday_Base: entry_long=25554, entry_short=28335, exit_long=697837, exit_short=739911
12:55:51.359 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 1min Fibonacci_Intraday_Base: final_entries=53889, final_exits=910300, conflicts_removed=53889
12:55:51.359 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Momentum_Intraday_Base' signal 'entry_long': (pl.col("close") > pl.col("open")) & (pl.col("close") > pl.col("close").shift(1)) & (pl.col("volume") > pl.col("volume").rolling_mean(window_size=10) * 1.2)
12:55:51.370 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Momentum_Intraday_Base' signal 'entry_short': (pl.col("close") < pl.col("open")) & (pl.col("close") < pl.col("close").shift(1)) & (pl.col("volume") > pl.col("volume").rolling_mean(window_size=10) * 1.2)
12:55:51.380 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Momentum_Intraday_Base' signal 'exit_long': pl.col("close") < pl.col("close").shift(1)
12:55:51.381 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Momentum_Intraday_Base' signal 'exit_short': pl.col("close") > pl.col("close").shift(1)
12:55:51.414 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 1min Momentum_Intraday_Base: entry_long=95392, entry_short=103473, exit_long=349820, exit_short=350630
12:55:51.537 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 1min Momentum_Intraday_Base: final_entries=198865, final_exits=501585, conflicts_removed=198865
12:55:51.537 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Price_Action_Intraday_Base' signal 'entry_long': (pl.col("close") > pl.col("open")) & (pl.col("high") - pl.col("close") < pl.col("close") - pl.col("low"))
12:55:51.539 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Price_Action_Intraday_Base' signal 'entry_short': (pl.col("close") < pl.col("open")) & (pl.col("close") - pl.col("low") < pl.col("high") - pl.col("close"))
12:55:51.540 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Price_Action_Intraday_Base' signal 'exit_long': pl.col("close") < pl.col("open")
12:55:51.540 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Price_Action_Intraday_Base' signal 'exit_short': pl.col("close") > pl.col("open")
12:55:51.573 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 1min Price_Action_Intraday_Base: entry_long=286023, entry_short=280409, exit_long=340451, exit_short=339854
12:55:51.611 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 1min Price_Action_Intraday_Base: final_entries=566432, final_exits=113873, conflicts_removed=566432
12:55:51.611 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Gap_Trading_Intraday_Base' signal 'entry_long': (pl.col("open") < pl.col("close").shift(1) * 0.99) & (pl.col("close") > pl.col("open"))
12:55:51.612 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Gap_Trading_Intraday_Base' signal 'entry_short': (pl.col("open") > pl.col("close").shift(1) * 1.01) & (pl.col("close") < pl.col("open"))
12:55:51.613 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Gap_Trading_Intraday_Base' signal 'exit_long': pl.col("close") > pl.col("open").shift(1)
12:55:51.613 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Gap_Trading_Intraday_Base' signal 'exit_short': pl.col("close") < pl.col("open").shift(1)
12:55:51.646 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 1min Gap_Trading_Intraday_Base: entry_long=145, entry_short=293, exit_long=391924, exit_short=394177
12:55:51.747 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 1min Gap_Trading_Intraday_Base: final_entries=438, final_exits=785663, conflicts_removed=438
12:55:51.747 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Reversal_Intraday_Base' signal 'entry_long': (pl.col("close") < pl.col("low").shift(1)) & (pl.col("close") > pl.col("open"))
12:55:51.748 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Reversal_Intraday_Base' signal 'entry_short': (pl.col("close") > pl.col("high").shift(1)) & (pl.col("close") < pl.col("open"))
12:55:51.749 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Reversal_Intraday_Base' signal 'exit_long': pl.col("close") > pl.col("high").shift(1)
12:55:51.749 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Reversal_Intraday_Base' signal 'exit_short': pl.col("close") < pl.col("low").shift(1)
12:55:51.782 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 1min Reversal_Intraday_Base: entry_long=4615, entry_short=4703, exit_long=191689, exit_short=194144
12:55:51.881 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 1min Reversal_Intraday_Base: final_entries=9318, final_exits=376515, conflicts_removed=9318
12:55:51.882 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Trend_Following_Intraday_Base' signal 'entry_long': (pl.col("close") > pl.col("ema_50")) & (pl.col("close").shift(1) < pl.col("ema_50").shift(1))
12:55:51.883 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Trend_Following_Intraday_Base' signal 'entry_short': (pl.col("close") < pl.col("ema_50")) & (pl.col("close").shift(1) > pl.col("ema_50").shift(1))
12:55:51.883 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Trend_Following_Intraday_Base' signal 'exit_long': pl.col("close") < pl.col("ema_50")
12:55:51.883 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Trend_Following_Intraday_Base' signal 'exit_short': pl.col("close") > pl.col("ema_50")
12:55:51.916 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 1min Trend_Following_Intraday_Base: entry_long=50942, entry_short=50946, exit_long=487736, exit_short=477365
12:55:51.974 - agents.backtesting.enhanced_backtesting_kimi - INFO - [SIGNAL_DEBUG] SUPREMEIND 1min Trend_Following_Intraday_Base: final_entries=101888, final_exits=863213, conflicts_removed=101888
12:55:51.975 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] Momentum_Scalping_Base: entry_signals=198865, exit_signals=501585, data_length=965140
12:55:52.100 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] Momentum_Scalping_Base RR 1:2: vectorbt generated 128518 trades
12:55:52.100 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] EMA_9_Scalping_Base: entry_signals=964780, exit_signals=0, data_length=965140
12:55:52.181 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] EMA_9_Scalping_Base RR 1:2: vectorbt generated 4878 trades
12:55:52.182 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] Three_MA_Crossover_Scalping_Base: entry_signals=783051, exit_signals=181710, data_length=965140
12:55:52.285 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] Three_MA_Crossover_Scalping_Base RR 1:2: vectorbt generated 67404 trades
12:55:52.285 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] Range_Trading_Scalping_Base: entry_signals=51443, exit_signals=910818, data_length=965140
12:55:52.380 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] Range_Trading_Scalping_Base RR 1:2: vectorbt generated 51048 trades
12:55:52.380 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] Breakout_Scalping_Base: entry_signals=385833, exit_signals=314617, data_length=965140
12:55:52.524 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] Breakout_Scalping_Base RR 1:2: vectorbt generated 164683 trades
12:55:52.525 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] Volume_Scalping_Base: entry_signals=154703, exit_signals=525602, data_length=965140
12:55:52.636 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] Volume_Scalping_Base RR 1:2: vectorbt generated 99595 trades
12:55:52.636 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] MACD_Crossover_Scalping_Base: entry_signals=965107, exit_signals=8, data_length=965140
12:55:52.716 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] MACD_Crossover_Scalping_Base RR 1:2: vectorbt generated 4877 trades
12:55:52.716 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] Bollinger_Bands_Scalping_Base: entry_signals=51443, exit_signals=910818, data_length=965140
12:55:52.808 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] Bollinger_Bands_Scalping_Base RR 1:2: vectorbt generated 51048 trades
12:55:52.808 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] VWAP_Scalping_Base: entry_signals=853, exit_signals=964287, data_length=965140
12:55:52.884 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] VWAP_Scalping_Base RR 1:2: vectorbt generated 634 trades
12:55:52.884 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] Parabolic_SAR_Scalping_Base: entry_signals=71312, exit_signals=892884, data_length=965140
12:55:52.982 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] Parabolic_SAR_Scalping_Base RR 1:2: vectorbt generated 66439 trades
12:55:52.982 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] Stochastic_Scalping_Base: entry_signals=126183, exit_signals=836759, data_length=965140
12:55:53.082 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] Stochastic_Scalping_Base RR 1:2: vectorbt generated 59561 trades
12:55:53.082 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] STC_Scalping_Base: entry_signals=76738, exit_signals=725273, data_length=965140
12:55:53.185 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] STC_Scalping_Base RR 1:2: vectorbt generated 76738 trades
12:55:53.186 - utils.real_gpu_accelerator - INFO - [GPU_DEBUG] ADX_Scalping_Base: entry_signals=715314, exit_signals=249812, data_length=965140