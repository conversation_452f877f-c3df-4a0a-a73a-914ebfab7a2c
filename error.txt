You probably got here by using a Python standard library function instead of the native expressions API.
Here are some things you might want to try:
- instead of `pl.col('a') and pl.col('b')`, use `pl.col('a') & pl.col('b')`
- instead of `pl.col('a') in [y, z]`, use `pl.col('a').is_in([y, z])`
- instead of `max(pl.col('a'), pl.col('b'))`, use `pl.max_horizontal(pl.col('a'), pl.col('b'))`

10:06:55.317 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'ADX_Intraday_Base' signal 'entry_short': pl.col("adx_14") > 25 & pl.col("plusdi_14") < pl.col("minusdi_14") & pl.col("volume") > pl.col("volume").rolling("datetime", period="20i").mean()
10:06:55.317 - agents.signal_generation.signal_agent - ERROR - Error evaluating expression 'pl.col("adx_14") > 25 & pl.col("plusdi_14") < pl.col("minusdi_14") & pl.col("volume") > pl.col("volume").rolling("datetime", period="20i").mean()' for ADX_Intraday_Base: the truth value of an Expr is ambiguous

You probably got here by using a Python standard library function instead of the native expressions API.
Here are some things you might want to try:
- instead of `pl.col('a') and pl.col('b')`, use `pl.col('a') & pl.col('b')`
- instead of `pl.col('a') in [y, z]`, use `pl.col('a').is_in([y, z])`
- instead of `max(pl.col('a'), pl.col('b'))`, use `pl.max_horizontal(pl.col('a'), pl.col('b'))`

10:06:55.320 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'ADX_Intraday_Base' signal 'exit_long': pl.col("plusdi_14") < pl.col("minusdi_14")
10:06:55.320 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'ADX_Intraday_Base' signal 'exit_short': pl.col("plusdi_14") > pl.col("minusdi_14")
10:06:55.334 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Fibonacci_Intraday_Base' signal 'entry_long': pl.col("close") > pl.col("fibonacci_38_2") & pl.col("close").shift(1) <= pl.col("fibonacci_38_2").shift(1) & pl.col("volume") > pl.col("volume").rolling("datetime", period="20i").mean()
10:06:55.334 - agents.signal_generation.signal_agent - ERROR - Error evaluating expression 'pl.col("close") > pl.col("fibonacci_38_2") & pl.col("close").shift(1) <= pl.col("fibonacci_38_2").shift(1) & pl.col("volume") > pl.col("volume").rolling("datetime", period="20i").mean()' for Fibonacci_Intraday_Base: the truth value of an Expr is ambiguous

You probably got here by using a Python standard library function instead of the native expressions API.
Here are some things you might want to try:
- instead of `pl.col('a') and pl.col('b')`, use `pl.col('a') & pl.col('b')`
- instead of `pl.col('a') in [y, z]`, use `pl.col('a').is_in([y, z])`
- instead of `max(pl.col('a'), pl.col('b'))`, use `pl.max_horizontal(pl.col('a'), pl.col('b'))`

10:06:55.337 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Fibonacci_Intraday_Base' signal 'entry_short': pl.col("close") < pl.col("fibonacci_61_8") & pl.col("close").shift(1) >= pl.col("fibonacci_61_8").shift(1) & pl.col("volume") > pl.col("volume").rolling("datetime", period="20i").mean()
10:06:55.337 - agents.signal_generation.signal_agent - ERROR - Error evaluating expression 'pl.col("close") < pl.col("fibonacci_61_8") & pl.col("close").shift(1) >= pl.col("fibonacci_61_8").shift(1) & pl.col("volume") > pl.col("volume").rolling("datetime", period="20i").mean()' for Fibonacci_Intraday_Base: the truth value of an Expr is ambiguous

You probably got here by using a Python standard library function instead of the native expressions API.
Here are some things you might want to try:
- instead of `pl.col('a') and pl.col('b')`, use `pl.col('a') & pl.col('b')`
- instead of `pl.col('a') in [y, z]`, use `pl.col('a').is_in([y, z])`
- instead of `max(pl.col('a'), pl.col('b'))`, use `pl.max_horizontal(pl.col('a'), pl.col('b'))`

10:06:55.340 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Fibonacci_Intraday_Base' signal 'exit_long': pl.col("close") < pl.col("fibonacci_23_6")
10:06:55.341 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Fibonacci_Intraday_Base' signal 'exit_short': pl.col("close") > pl.col("fibonacci_78_6")
10:06:55.354 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Momentum_Intraday_Base' signal 'entry_long': pl.col("close") > pl.col("open") & pl.col("close") > pl.col("close").shift(1) & pl.col("volume") > pl.col("volume").rolling("datetime", period="10i").mean() * 1.5
10:06:55.355 - agents.signal_generation.signal_agent - ERROR - Error evaluating expression 'pl.col("close") > pl.col("open") & pl.col("close") > pl.col("close").shift(1) & pl.col("volume") > pl.col("volume").rolling("datetime", period="10i").mean() * 1.5' for Momentum_Intraday_Base: the truth value of an Expr is ambiguous

You probably got here by using a Python standard library function instead of the native expressions API.
Here are some things you might want to try:
- instead of `pl.col('a') and pl.col('b')`, use `pl.col('a') & pl.col('b')`
- instead of `pl.col('a') in [y, z]`, use `pl.col('a').is_in([y, z])`
- instead of `max(pl.col('a'), pl.col('b'))`, use `pl.max_horizontal(pl.col('a'), pl.col('b'))`

10:06:55.357 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Momentum_Intraday_Base' signal 'entry_short': pl.col("close") < pl.col("open") & pl.col("close") < pl.col("close").shift(1) & pl.col("volume") > pl.col("volume").rolling("datetime", period="10i").mean() * 1.5
10:06:55.358 - agents.signal_generation.signal_agent - ERROR - Error evaluating expression 'pl.col("close") < pl.col("open") & pl.col("close") < pl.col("close").shift(1) & pl.col("volume") > pl.col("volume").rolling("datetime", period="10i").mean() * 1.5' for Momentum_Intraday_Base: the truth value of an Expr is ambiguous

You probably got here by using a Python standard library function instead of the native expressions API.
Here are some things you might want to try:
- instead of `pl.col('a') and pl.col('b')`, use `pl.col('a') & pl.col('b')`
- instead of `pl.col('a') in [y, z]`, use `pl.col('a').is_in([y, z])`
- instead of `max(pl.col('a'), pl.col('b'))`, use `pl.max_horizontal(pl.col('a'), pl.col('b'))`

10:06:55.361 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Momentum_Intraday_Base' signal 'exit_long': pl.col("close") < pl.col("close").shift(1)
10:06:55.362 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Momentum_Intraday_Base' signal 'exit_short': pl.col("close") > pl.col("close").shift(1)
10:06:55.392 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Price_Action_Intraday_Base' signal 'entry_long': pl.col("close") > pl.col("open") & pl.col("high") - pl.col("close") < pl.col("close") - pl.col("low")
10:06:55.392 - agents.signal_generation.signal_agent - ERROR - Error evaluating expression 'pl.col("close") > pl.col("open") & pl.col("high") - pl.col("close") < pl.col("close") - pl.col("low")' for Price_Action_Intraday_Base: the truth value of an Expr is ambiguous

You probably got here by using a Python standard library function instead of the native expressions API.
Here are some things you might want to try:
- instead of `pl.col('a') and pl.col('b')`, use `pl.col('a') & pl.col('b')`
- instead of `pl.col('a') in [y, z]`, use `pl.col('a').is_in([y, z])`
- instead of `max(pl.col('a'), pl.col('b'))`, use `pl.max_horizontal(pl.col('a'), pl.col('b'))`

10:06:55.395 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Price_Action_Intraday_Base' signal 'entry_short': pl.col("close") < pl.col("open") & pl.col("close") - pl.col("low") < pl.col("high") - pl.col("close")
10:06:55.395 - agents.signal_generation.signal_agent - ERROR - Error evaluating expression 'pl.col("close") < pl.col("open") & pl.col("close") - pl.col("low") < pl.col("high") - pl.col("close")' for Price_Action_Intraday_Base: the truth value of an Expr is ambiguous

You probably got here by using a Python standard library function instead of the native expressions API.
Here are some things you might want to try:
- instead of `pl.col('a') and pl.col('b')`, use `pl.col('a') & pl.col('b')`
- instead of `pl.col('a') in [y, z]`, use `pl.col('a').is_in([y, z])`
- instead of `max(pl.col('a'), pl.col('b'))`, use `pl.max_horizontal(pl.col('a'), pl.col('b'))`

10:06:55.399 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Price_Action_Intraday_Base' signal 'exit_long': pl.col("close") < pl.col("open")
10:06:55.399 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Price_Action_Intraday_Base' signal 'exit_short': pl.col("close") > pl.col("open")
10:06:55.413 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Gap_Trading_Intraday_Base' signal 'entry_long': pl.col("open") < pl.col("close").shift(1) * 0.99 & pl.col("close") > pl.col("open")
10:06:55.413 - agents.signal_generation.signal_agent - ERROR - Error evaluating expression 'pl.col("open") < pl.col("close").shift(1) * 0.99 & pl.col("close") > pl.col("open")' for Gap_Trading_Intraday_Base: the truth value of an Expr is ambiguous

You probably got here by using a Python standard library function instead of the native expressions API.
Here are some things you might want to try:
- instead of `pl.col('a') and pl.col('b')`, use `pl.col('a') & pl.col('b')`
- instead of `pl.col('a') in [y, z]`, use `pl.col('a').is_in([y, z])`
- instead of `max(pl.col('a'), pl.col('b'))`, use `pl.max_horizontal(pl.col('a'), pl.col('b'))`

10:06:55.416 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Gap_Trading_Intraday_Base' signal 'entry_short': pl.col("open") > pl.col("close").shift(1) * 1.01 & pl.col("close") < pl.col("open")
10:06:55.416 - agents.signal_generation.signal_agent - ERROR - Error evaluating expression 'pl.col("open") > pl.col("close").shift(1) * 1.01 & pl.col("close") < pl.col("open")' for Gap_Trading_Intraday_Base: the truth value of an Expr is ambiguous

You probably got here by using a Python standard library function instead of the native expressions API.
Here are some things you might want to try:
- instead of `pl.col('a') and pl.col('b')`, use `pl.col('a') & pl.col('b')`
- instead of `pl.col('a') in [y, z]`, use `pl.col('a').is_in([y, z])`
- instead of `max(pl.col('a'), pl.col('b'))`, use `pl.max_horizontal(pl.col('a'), pl.col('b'))`

10:06:55.419 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Gap_Trading_Intraday_Base' signal 'exit_long': pl.col("close") > pl.col("open").shift(1)
10:06:55.420 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Gap_Trading_Intraday_Base' signal 'exit_short': pl.col("close") < pl.col("open").shift(1)
10:06:55.449 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Reversal_Intraday_Base' signal 'entry_long': pl.col("close") < pl.col("low").shift(1) & pl.col("close") > pl.col("open")
10:06:55.449 - agents.signal_generation.signal_agent - ERROR - Error evaluating expression 'pl.col("close") < pl.col("low").shift(1) & pl.col("close") > pl.col("open")' for Reversal_Intraday_Base: the truth value of an Expr is ambiguous

You probably got here by using a Python standard library function instead of the native expressions API.
Here are some things you might want to try:
- instead of `pl.col('a') and pl.col('b')`, use `pl.col('a') & pl.col('b')`
- instead of `pl.col('a') in [y, z]`, use `pl.col('a').is_in([y, z])`
- instead of `max(pl.col('a'), pl.col('b'))`, use `pl.max_horizontal(pl.col('a'), pl.col('b'))`

10:06:55.452 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Reversal_Intraday_Base' signal 'entry_short': pl.col("close") > pl.col("high").shift(1) & pl.col("close") < pl.col("open")
10:06:55.453 - agents.signal_generation.signal_agent - ERROR - Error evaluating expression 'pl.col("close") > pl.col("high").shift(1) & pl.col("close") < pl.col("open")' for Reversal_Intraday_Base: the truth value of an Expr is ambiguous

You probably got here by using a Python standard library function instead of the native expressions API.
Here are some things you might want to try:
- instead of `pl.col('a') and pl.col('b')`, use `pl.col('a') & pl.col('b')`
- instead of `pl.col('a') in [y, z]`, use `pl.col('a').is_in([y, z])`
- instead of `max(pl.col('a'), pl.col('b'))`, use `pl.max_horizontal(pl.col('a'), pl.col('b'))`

10:06:55.456 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Reversal_Intraday_Base' signal 'exit_long': pl.col("close") > pl.col("high").shift(1)
10:06:55.456 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Reversal_Intraday_Base' signal 'exit_short': pl.col("close") < pl.col("low").shift(1)
10:06:55.485 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Trend_Following_Intraday_Base' signal 'entry_long': pl.col("close") > pl.col("ema_50") & pl.col("close").shift(1) < pl.col("ema_50").shift(1)
10:06:55.485 - agents.signal_generation.signal_agent - ERROR - Error evaluating expression 'pl.col("close") > pl.col("ema_50") & pl.col("close").shift(1) < pl.col("ema_50").shift(1)' for Trend_Following_Intraday_Base: the truth value of an Expr is ambiguous

You probably got here by using a Python standard library function instead of the native expressions API.
Here are some things you might want to try:
- instead of `pl.col('a') and pl.col('b')`, use `pl.col('a') & pl.col('b')`
- instead of `pl.col('a') in [y, z]`, use `pl.col('a').is_in([y, z])`
- instead of `max(pl.col('a'), pl.col('b'))`, use `pl.max_horizontal(pl.col('a'), pl.col('b'))`

10:06:55.488 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Trend_Following_Intraday_Base' signal 'entry_short': pl.col("close") < pl.col("ema_50") & pl.col("close").shift(1) > pl.col("ema_50").shift(1)
10:06:55.488 - agents.signal_generation.signal_agent - ERROR - Error evaluating expression 'pl.col("close") < pl.col("ema_50") & pl.col("close").shift(1) > pl.col("ema_50").shift(1)' for Trend_Following_Intraday_Base: the truth value of an Expr is ambiguous

You probably got here by using a Python standard library function instead of the native expressions API.
Here are some things you might want to try:
- instead of `pl.col('a') and pl.col('b')`, use `pl.col('a') & pl.col('b')`
- instead of `pl.col('a') in [y, z]`, use `pl.col('a').is_in([y, z])`
- instead of `max(pl.col('a'), pl.col('b'))`, use `pl.max_horizontal(pl.col('a'), pl.col('b'))`

10:06:55.491 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Trend_Following_Intraday_Base' signal 'exit_long': pl.col("close") < pl.col("ema_50")
10:06:55.492 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Trend_Following_Intraday_Base' signal 'exit_short': pl.col("close") > pl.col("ema_50")
10:06:55.551 - utils.real_gpu_accelerator - INFO - No trades generated for strategy Momentum_Scalping_Base with RR 1:2. Skipping stats calculation.
10:06:55.596 - utils.real_gpu_accelerator - INFO - No trades generated for strategy EMA_9_Scalping_Base with RR 1:2. Skipping stats calculation.
10:06:55.639 - utils.real_gpu_accelerator - INFO - No trades generated for strategy Three_MA_Crossover_Scalping_Base with RR 1:2. Skipping stats calculation.
10:06:55.684 - utils.real_gpu_accelerator - INFO - No trades generated for strategy Range_Trading_Scalping_Base with RR 1:2. Skipping stats calculation.
10:06:55.728 - utils.real_gpu_accelerator - INFO - No trades generated for strategy Breakout_Scalping_Base with RR 1:2. Skipping stats calculation.
10:06:55.772 - utils.real_gpu_accelerator - INFO - No trades generated for strategy Volume_Scalping_Base with RR 1:2. Skipping stats calculation.
10:06:55.816 - utils.real_gpu_accelerator - INFO - No trades generated for strategy MACD_Crossover_Scalping_Base with RR 1:2. Skipping stats calculation.
10:06:55.861 - utils.real_gpu_accelerator - INFO - No trades generated for strategy Bollinger_Bands_Scalping_Base with RR 1:2. Skipping stats calculation.
10:06:55.917 - utils.real_gpu_accelerator - INFO - No trades generated for strategy VWAP_Scalping_Base with RR 1:2. Skipping stats calculation.
10:06:55.974 - utils.real_gpu_accelerator - INFO - No trades generated for strategy Parabolic_SAR_Scalping_Base with RR 1:2. Skipping stats calculation.
10:06:56.029 - utils.real_gpu_accelerator - INFO - No trades generated for strategy Stochastic_Scalping_Base with RR 1:2. Skipping stats calculation.
10:06:56.083 - utils.real_gpu_accelerator - INFO - No trades generated for strategy STC_Scalping_Base with RR 1:2. Skipping stats calculation.
10:06:56.140 - utils.real_gpu_accelerator - INFO - No trades generated for strategy ADX_Scalping_Base with RR 1:2. Skipping stats calculation.
10:06:56.185 - utils.real_gpu_accelerator - INFO - No trades generated for strategy Fibonacci_Scalping_Base with RR 1:2. Skipping stats calculation.
10:06:56.243 - utils.real_gpu_accelerator - INFO - No trades generated for strategy RSI_Intraday_Base with RR 1:2. Skipping stats calculation.
10:06:56.301 - utils.real_gpu_accelerator - INFO - No trades generated for strategy EMA_Crossover_Intraday_Base with RR 1:2. Skipping stats calculation.
10:06:56.356 - utils.real_gpu_accelerator - INFO - No trades generated for strategy VWAP_Intraday_Base with RR 1:2. Skipping stats calculation.
10:06:56.399 - utils.real_gpu_accelerator - INFO - No trades generated for strategy MACD_Crossover_Intraday_Base with RR 1:2. Skipping stats calculation.
10:06:56.454 - utils.real_gpu_accelerator - INFO - No trades generated for strategy Bollinger_Bands_Intraday_Base with RR 1:2. Skipping stats calculation.
10:06:56.509 - utils.real_gpu_accelerator - INFO - No trades generated for strategy Stochastic_Intraday_Base with RR 1:2. Skipping stats calculation.
10:06:56.560 - utils.real_gpu_accelerator - INFO - No trades generated for strategy Parabolic_SAR_Intraday_Base with RR 1:2. Skipping stats calculation.
10:06:56.615 - utils.real_gpu_accelerator - INFO - No trades generated for strategy ADX_Intraday_Base with RR 1:2. Skipping stats calculation.
10:06:56.674 - utils.real_gpu_accelerator - INFO - No trades generated for strategy Fibonacci_Intraday_Base with RR 1:2. Skipping stats calculation.
10:06:56.732 - utils.real_gpu_accelerator - INFO - No trades generated for strategy Momentum_Intraday_Base with RR 1:2. Skipping stats calculation.
10:06:56.787 - utils.real_gpu_accelerator - INFO - No trades generated for strategy Price_Action_Intraday_Base with RR 1:2. Skipping stats calculation.
10:06:56.843 - utils.real_gpu_accelerator - INFO - No trades generated for strategy Gap_Trading_Intraday_Base with RR 1:2. Skipping stats calculation.
10:06:56.887 - utils.real_gpu_accelerator - INFO - No trades generated for strategy Reversal_Intraday_Base with RR 1:2. Skipping stats calculation.
10:06:56.944 - utils.real_gpu_accelerator - INFO - No trades generated for strategy Trend_Following_Intraday_Base with RR 1:2. Skipping stats calculation.
10:06:56.944 - utils.real_gpu_accelerator - INFO - 🚀 GPU vectorized backtest completed in 1.439s
10:06:56.944 - agents.backtesting.enhanced_backtesting_kimi - INFO - Processing ABB (5min) on GPU...
10:06:57.030 - agents.backtesting.kimi.file_io - INFO - Found 'timestamp' column, converting to 'datetime'.
10:06:57.082 - agents.backtesting.kimi.file_io - INFO - Successfully converted 'timestamp' to 'datetime'.
10:06:57.102 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Momentum_Scalping_Base' signal 'entry_long': pl.col("close") > pl.col("open") & pl.col("close") > pl.col("close").shift(1) & pl.col("volume") > pl.col("volume").rolling("datetime", period="10i").mean() * 1.5
10:06:57.102 - agents.signal_generation.signal_agent - ERROR - Error evaluating expression 'pl.col("close") > pl.col("open") & pl.col("close") > pl.col("close").shift(1) & pl.col("volume") > pl.col("volume").rolling("datetime", period="10i").mean() * 1.5' for Momentum_Scalping_Base: the truth value of an Expr is ambiguous

You probably got here by using a Python standard library function instead of the native expressions API.
Here are some things you might want to try:
- instead of `pl.col('a') and pl.col('b')`, use `pl.col('a') & pl.col('b')`
- instead of `pl.col('a') in [y, z]`, use `pl.col('a').is_in([y, z])`
- instead of `max(pl.col('a'), pl.col('b'))`, use `pl.max_horizontal(pl.col('a'), pl.col('b'))`

10:06:57.105 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Momentum_Scalping_Base' signal 'entry_short': pl.col("close") < pl.col("open") & pl.col("close") < pl.col("close").shift(1) & pl.col("volume") > pl.col("volume").rolling("datetime", period="10i").mean() * 1.5
10:06:57.105 - agents.signal_generation.signal_agent - ERROR - Error evaluating expression 'pl.col("close") < pl.col("open") & pl.col("close") < pl.col("close").shift(1) & pl.col("volume") > pl.col("volume").rolling("datetime", period="10i").mean() * 1.5' for Momentum_Scalping_Base: the truth value of an Expr is ambiguous

You probably got here by using a Python standard library function instead of the native expressions API.
Here are some things you might want to try:
- instead of `pl.col('a') and pl.col('b')`, use `pl.col('a') & pl.col('b')`
- instead of `pl.col('a') in [y, z]`, use `pl.col('a').is_in([y, z])`
- instead of `max(pl.col('a'), pl.col('b'))`, use `pl.max_horizontal(pl.col('a'), pl.col('b'))`

10:06:57.108 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Momentum_Scalping_Base' signal 'exit_long': pl.col("close") < pl.col("close").shift(1)
10:06:57.108 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Momentum_Scalping_Base' signal 'exit_short': pl.col("close") > pl.col("close").shift(1)
10:06:57.133 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'EMA_9_Scalping_Base' signal 'entry_long': pl.col("close") > pl.col("ema_9")
10:06:57.133 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'EMA_9_Scalping_Base' signal 'entry_short': pl.col("close") < pl.col("ema_9")
10:06:57.133 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'EMA_9_Scalping_Base' signal 'exit_long': pl.col("close") < pl.col("ema_9")
10:06:57.134 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'EMA_9_Scalping_Base' signal 'exit_short': pl.col("close") > pl.col("ema_9")
10:06:57.142 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Three_MA_Crossover_Scalping_Base' signal 'entry_long': pl.col("ema_5") > pl.col("ema_10") & pl.col("ema_10") > pl.col("ema_20")
10:06:57.142 - agents.signal_generation.signal_agent - ERROR - Error evaluating expression 'pl.col("ema_5") > pl.col("ema_10") & pl.col("ema_10") > pl.col("ema_20")' for Three_MA_Crossover_Scalping_Base: the truth value of an Expr is ambiguous

You probably got here by using a Python standard library function instead of the native expressions API.
Here are some things you might want to try:
- instead of `pl.col('a') and pl.col('b')`, use `pl.col('a') & pl.col('b')`
- instead of `pl.col('a') in [y, z]`, use `pl.col('a').is_in([y, z])`
- instead of `max(pl.col('a'), pl.col('b'))`, use `pl.max_horizontal(pl.col('a'), pl.col('b'))`

10:06:57.144 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Three_MA_Crossover_Scalping_Base' signal 'entry_short': pl.col("ema_5") < pl.col("ema_10") & pl.col("ema_10") < pl.col("ema_20")
10:06:57.144 - agents.signal_generation.signal_agent - ERROR - Error evaluating expression 'pl.col("ema_5") < pl.col("ema_10") & pl.col("ema_10") < pl.col("ema_20")' for Three_MA_Crossover_Scalping_Base: the truth value of an Expr is ambiguous

You probably got here by using a Python standard library function instead of the native expressions API.
Here are some things you might want to try:
- instead of `pl.col('a') and pl.col('b')`, use `pl.col('a') & pl.col('b')`
- instead of `pl.col('a') in [y, z]`, use `pl.col('a').is_in([y, z])`
- instead of `max(pl.col('a'), pl.col('b'))`, use `pl.max_horizontal(pl.col('a'), pl.col('b'))`

10:06:57.146 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Three_MA_Crossover_Scalping_Base' signal 'exit_long': pl.col("ema_5") < pl.col("ema_10")
10:06:57.146 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Three_MA_Crossover_Scalping_Base' signal 'exit_short': pl.col("ema_5") > pl.col("ema_10")
10:06:57.154 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Range_Trading_Scalping_Base' signal 'entry_long': pl.col("close") < pl.col("bollinger_lower") & pl.col("close").shift(1) >= pl.col("bollinger_lower").shift(1)
10:06:57.155 - agents.signal_generation.signal_agent - ERROR - Error evaluating expression 'pl.col("close") < pl.col("bollinger_lower") & pl.col("close").shift(1) >= pl.col("bollinger_lower").shift(1)' for Range_Trading_Scalping_Base: the truth value of an Expr is ambiguous

You probably got here by using a Python standard library function instead of the native expressions API.
Here are some things you might want to try:
- instead of `pl.col('a') and pl.col('b')`, use `pl.col('a') & pl.col('b')`
- instead of `pl.col('a') in [y, z]`, use `pl.col('a').is_in([y, z])`
- instead of `max(pl.col('a'), pl.col('b'))`, use `pl.max_horizontal(pl.col('a'), pl.col('b'))`

10:06:57.156 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Range_Trading_Scalping_Base' signal 'entry_short': pl.col("close") > pl.col("bollinger_upper") & pl.col("close").shift(1) <= pl.col("bollinger_upper").shift(1)
10:06:57.156 - agents.signal_generation.signal_agent - ERROR - Error evaluating expression 'pl.col("close") > pl.col("bollinger_upper") & pl.col("close").shift(1) <= pl.col("bollinger_upper").shift(1)' for Range_Trading_Scalping_Base: the truth value of an Expr is ambiguous

You probably got here by using a Python standard library function instead of the native expressions API.
Here are some things you might want to try:
- instead of `pl.col('a') and pl.col('b')`, use `pl.col('a') & pl.col('b')`
- instead of `pl.col('a') in [y, z]`, use `pl.col('a').is_in([y, z])`
- instead of `max(pl.col('a'), pl.col('b'))`, use `pl.max_horizontal(pl.col('a'), pl.col('b'))`

10:06:57.158 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Range_Trading_Scalping_Base' signal 'exit_long': pl.col("close") > pl.col("bollinger_middle")
10:06:57.159 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Range_Trading_Scalping_Base' signal 'exit_short': pl.col("close") < pl.col("bollinger_middle")
10:06:57.167 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Breakout_Scalping_Base' signal 'entry_long': pl.col("close") > pl.col("high").shift(1)
10:06:57.167 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Breakout_Scalping_Base' signal 'entry_short': pl.col("close") < pl.col("low").shift(1)
10:06:57.167 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Breakout_Scalping_Base' signal 'exit_long': pl.col("close") < pl.col("close").shift(1)
10:06:57.167 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Breakout_Scalping_Base' signal 'exit_short': pl.col("close") > pl.col("close").shift(1)
10:06:57.194 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Volume_Scalping_Base' signal 'entry_long': pl.col("volume") > pl.col("volume").rolling("datetime", period="20i").mean() * 2 & pl.col("close") > pl.col("open")
10:06:57.194 - agents.signal_generation.signal_agent - ERROR - Error evaluating expression 'pl.col("volume") > pl.col("volume").rolling("datetime", period="20i").mean() * 2 & pl.col("close") > pl.col("open")' for Volume_Scalping_Base: the truth value of an Expr is ambiguous

You probably got here by using a Python standard library function instead of the native expressions API.
Here are some things you might want to try:
- instead of `pl.col('a') and pl.col('b')`, use `pl.col('a') & pl.col('b')`
- instead of `pl.col('a') in [y, z]`, use `pl.col('a').is_in([y, z])`
- instead of `max(pl.col('a'), pl.col('b'))`, use `pl.max_horizontal(pl.col('a'), pl.col('b'))`

10:06:57.196 - agents.signal_generation.signal_agent - INFO - Evaluating expression for strategy 'Volume_Scalping_Base' signal 'entry_short': pl.col("volume") > pl.col("volume").rolling("datetime", period="20i").mean() * 2 & pl.col("close") < pl.col("open")
10:06:57.196 - agents.signal_generation.signal_agent - ERROR - Error evaluating expression 'pl.col("volume") > pl.col("volume").rolling("datetime", period="20i").mean() * 2 & pl.col("close") < pl.col("open")' for Volume_Scalping_Base: the truth value of an Expr is ambiguous

You probably got here by using a Python standard library function instead of the native expressions API.
Here are some things you might want to try:
- instead of `pl.col('a') and pl.col('b')`, use `pl.col('a') & pl.col('b')`
- instead of `pl.col('a') in [y, z]`, use `pl.col('a').is_in([y, z])`
- instead of `max(pl.col('a'), pl.col('b'))`, use `pl.max_horizontal(pl.col('a'), pl.col('b'))`
